# Water Polo Manager 🏊‍♂️

A comprehensive water polo management game built with Next.js, featuring team building, tactical formations, and global competition.

## 🌊 Game Overview

Water Polo Manager is a Top Eleven-style sports management game focused entirely on water polo strategy, team development, and global competition. Build your squad of 14 players, master tactical formations, and compete in leagues worldwide.

## 🚀 Features

- **Team Management**: Build and manage your squad with unique player positions
- **Formation Builder**: Interactive drag-and-drop formation creator
- **Player Development**: Train players and improve their skills over time
- **Global Competition**: Compete in leagues, cups, and tournaments
- **Real-time Matches**: Watch your team play with dynamic match simulation
- **Community Features**: Join associations and compete with other managers
- **Responsive Design**: Optimized for desktop and mobile devices

## 🛠️ Tech Stack

- **Frontend**: Next.js 15 (App Router), React 18+, TypeScript
- **Styling**: Tailwind CSS v4
- **Authentication**: Supabase Auth with Email/Password
- **Database**: Supabase PostgreSQL
- **Animations**: Framer Motion
- **UI Components**: Custom components with Lucide React icons
- **Deployment**: Vercel (recommended)

## 📦 Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**

   Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

3. **Set up Supabase database**

   Run the SQL files in order in your Supabase SQL editor:
   1. First run `supabase-setup-step1.sql` (creates tables)
   2. Then run `supabase-setup-step2.sql` (enables RLS and policies)
   3. Finally run `supabase-setup-step3.sql` (creates functions and triggers)

4. **Configure Email Authentication**

   In your Supabase dashboard:
   - Go to Authentication > Settings
   - Enable email authentication (should be enabled by default)
   - Configure email templates if desired

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**

   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎮 Game Mechanics

### Player Positions
- **Goalkeeper**: Last line of defense
- **Wings** (Left/Right): Primary scoring threats
- **Drivers** (Left/Right): Versatile field players
- **Center Forward**: Target player in attack
- **Point**: Playmaker and team quarterback

### Player Stats
- **Shooting**: Accuracy and power of shots
- **Swimming**: Speed and endurance in water
- **Passing**: Ball distribution and vision
- **Defense**: Tackling and positioning

### Formations
- Standard 7v7 setup with customizable positions
- Tactical variations for different game situations
- Real-time formation adjustments during matches

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect your repository to Vercel**
2. **Set environment variables** in Vercel dashboard
3. **Deploy** - Vercel will automatically build and deploy your app

### Manual Deployment

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start the production server**
   ```bash
   npm start
   ```

## 🔧 Supabase Setup

1. Create a new Supabase project
2. Run the SQL setup files in order (step1, step2, step3)
3. Enable email authentication in Authentication settings
4. Configure Row Level Security policies

## 📞 Support

- **Documentation**: Check the `/support` page
- **Issues**: Report bugs on GitHub
- **Community**: Join our Discord server

---

**Built with ❤️ for water polo fans worldwide**
