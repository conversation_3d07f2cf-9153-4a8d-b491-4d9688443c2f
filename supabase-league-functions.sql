-- League System Database Functions
-- These functions support the league system operations

-- Function to safely increment league team count
CREATE OR REPLACE FUNCTION increment_league_teams(league_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE leagues 
  SET current_teams = current_teams + 1,
      updated_at = NOW()
  WHERE id = league_id;
END;
$$ LANGUAGE plpgsql;

-- Function to safely decrement league team count
CREATE OR REPLACE FUNCTION decrement_league_teams(league_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE leagues 
  SET current_teams = GREATEST(current_teams - 1, 0),
      updated_at = NOW()
  WHERE id = league_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get league standings
CREATE OR REPLACE FUNCTION get_league_standings(league_id UUID)
RETURNS TABLE (
  team_id UUID,
  team_name TEXT,
  position INTEGER,
  matches_played INTEGER,
  wins INTEGER,
  draws INTEGER,
  losses INTEGER,
  goals_for INTEGER,
  goals_against INTEGER,
  goal_difference INTEGER,
  points INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id as team_id,
    t.name as team_name,
    ROW_NUMBER() OVER (ORDER BY t.league_points DESC, (t.goals_for - t.goals_against) DESC, t.goals_for DESC)::INTEGER as position,
    t.matches_played,
    t.matches_won as wins,
    t.matches_drawn as draws,
    t.matches_lost as losses,
    t.goals_for,
    t.goals_against,
    (t.goals_for - t.goals_against) as goal_difference,
    t.league_points as points
  FROM teams t
  WHERE t.league_id = get_league_standings.league_id
  ORDER BY t.league_points DESC, (t.goals_for - t.goals_against) DESC, t.goals_for DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to update team stats after a match
CREATE OR REPLACE FUNCTION update_team_match_stats(
  team_id UUID,
  goals_scored INTEGER,
  goals_conceded INTEGER,
  result TEXT -- 'win', 'draw', 'loss'
)
RETURNS void AS $$
BEGIN
  UPDATE teams 
  SET 
    matches_played = matches_played + 1,
    matches_won = matches_won + CASE WHEN result = 'win' THEN 1 ELSE 0 END,
    matches_drawn = matches_drawn + CASE WHEN result = 'draw' THEN 1 ELSE 0 END,
    matches_lost = matches_lost + CASE WHEN result = 'loss' THEN 1 ELSE 0 END,
    goals_for = goals_for + goals_scored,
    goals_against = goals_against + goals_conceded,
    league_points = league_points + CASE 
      WHEN result = 'win' THEN 3 
      WHEN result = 'draw' THEN 1 
      ELSE 0 
    END,
    updated_at = NOW()
  WHERE id = team_id;
END;
$$ LANGUAGE plpgsql;

-- Function to generate round-robin fixtures for a league
CREATE OR REPLACE FUNCTION generate_league_fixtures(
  league_id UUID,
  season_id UUID
)
RETURNS TABLE (
  home_team_id UUID,
  away_team_id UUID,
  matchday INTEGER
) AS $$
DECLARE
  team_ids UUID[];
  team_count INTEGER;
  total_rounds INTEGER;
  current_round INTEGER;
  i INTEGER;
  j INTEGER;
  matchday_counter INTEGER;
BEGIN
  -- Get all team IDs in the league
  SELECT array_agg(id) INTO team_ids
  FROM teams 
  WHERE teams.league_id = generate_league_fixtures.league_id;
  
  team_count := array_length(team_ids, 1);
  
  IF team_count IS NULL OR team_count < 2 THEN
    RETURN;
  END IF;
  
  -- Calculate total rounds (each team plays each other twice)
  total_rounds := (team_count - 1) * 2;
  matchday_counter := 1;
  
  -- Generate fixtures for each round
  FOR current_round IN 1..total_rounds LOOP
    FOR i IN 1..team_count LOOP
      FOR j IN (i+1)..team_count LOOP
        -- First half of season: team i vs team j
        IF current_round <= (team_count - 1) THEN
          home_team_id := team_ids[i];
          away_team_id := team_ids[j];
          matchday := matchday_counter;
          RETURN NEXT;
        -- Second half of season: team j vs team i (reverse fixtures)
        ELSE
          home_team_id := team_ids[j];
          away_team_id := team_ids[i];
          matchday := matchday_counter;
          RETURN NEXT;
        END IF;
      END LOOP;
    END LOOP;
    matchday_counter := matchday_counter + 1;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to check if a team can be promoted
CREATE OR REPLACE FUNCTION can_team_be_promoted(team_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  team_league_id UUID;
  team_position INTEGER;
  tier_level INTEGER;
BEGIN
  -- Get team's current league and position
  SELECT 
    t.league_id,
    t.league_position
  INTO team_league_id, team_position
  FROM teams t
  WHERE t.id = team_id;
  
  -- Get tier level
  SELECT lt.level INTO tier_level
  FROM leagues l
  JOIN league_tiers lt ON l.tier_id = lt.id
  WHERE l.id = team_league_id;
  
  -- Can be promoted if in top 3 positions and not in highest tier
  RETURN (team_position <= 3 AND tier_level < 5);
END;
$$ LANGUAGE plpgsql;

-- Function to check if a team will be relegated
CREATE OR REPLACE FUNCTION will_team_be_relegated(team_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  team_league_id UUID;
  team_position INTEGER;
  tier_level INTEGER;
  total_teams INTEGER;
BEGIN
  -- Get team's current league and position
  SELECT 
    t.league_id,
    t.league_position
  INTO team_league_id, team_position
  FROM teams t
  WHERE t.id = team_id;
  
  -- Get tier level and total teams in league
  SELECT lt.level, l.current_teams 
  INTO tier_level, total_teams
  FROM leagues l
  JOIN league_tiers lt ON l.tier_id = lt.id
  WHERE l.id = team_league_id;
  
  -- Will be relegated if in bottom 3 positions and not in lowest tier
  RETURN (team_position > (total_teams - 3) AND tier_level > 1);
END;
$$ LANGUAGE plpgsql;
