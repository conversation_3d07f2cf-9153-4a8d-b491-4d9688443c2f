-- Insert League Tiers Data
-- This script populates the league_tiers table with the 5 tiers and 5 divisions each

-- Bronze Tier (Level 1) - Starter leagues
INSERT INTO public.league_tiers (name, level, division, prize_money_base, promotion_bonus, relegation_penalty, tactics_bonus_enabled, scouting_quality_multiplier, fanbase_growth_multiplier) VALUES
('Bronze', 1, 1, 5000, 2000, 500, false, 0.8, 0.8),
('Bronze', 1, 2, 4500, 1800, 450, false, 0.8, 0.8),
('Bronze', 1, 3, 4000, 1600, 400, false, 0.8, 0.8),
('Bronze', 1, 4, 3500, 1400, 350, false, 0.8, 0.8),
('Bronze', 1, 5, 3000, 1200, 300, false, 0.8, 0.8);

-- Silver Tier (Level 2) - Intermediate with tactics bonuses
INSERT INTO public.league_tiers (name, level, division, prize_money_base, promotion_bonus, relegation_penalty, tactics_bonus_enabled, scouting_quality_multiplier, fanbase_growth_multiplier) VALUES
('Silver', 2, 1, 10000, 4000, 1000, true, 1.0, 1.0),
('Silver', 2, 2, 9000, 3600, 900, true, 1.0, 1.0),
('Silver', 2, 3, 8000, 3200, 800, true, 1.0, 1.0),
('Silver', 2, 4, 7000, 2800, 700, true, 1.0, 1.0),
('Silver', 2, 5, 6000, 2400, 600, true, 1.0, 1.0);

-- Gold Tier (Level 3) - High skill required
INSERT INTO public.league_tiers (name, level, division, prize_money_base, promotion_bonus, relegation_penalty, tactics_bonus_enabled, scouting_quality_multiplier, fanbase_growth_multiplier) VALUES
('Gold', 3, 1, 20000, 8000, 2000, true, 1.2, 1.2),
('Gold', 3, 2, 18000, 7200, 1800, true, 1.2, 1.2),
('Gold', 3, 3, 16000, 6400, 1600, true, 1.2, 1.2),
('Gold', 3, 4, 14000, 5600, 1400, true, 1.2, 1.2),
('Gold', 3, 5, 12000, 4800, 1200, true, 1.2, 1.2);

-- Elite Tier (Level 4) - Top-tier, invitation-based
INSERT INTO public.league_tiers (name, level, division, prize_money_base, promotion_bonus, relegation_penalty, tactics_bonus_enabled, scouting_quality_multiplier, fanbase_growth_multiplier) VALUES
('Elite', 4, 1, 40000, 16000, 4000, true, 1.5, 1.5),
('Elite', 4, 2, 36000, 14400, 3600, true, 1.5, 1.5),
('Elite', 4, 3, 32000, 12800, 3200, true, 1.5, 1.5),
('Elite', 4, 4, 28000, 11200, 2800, true, 1.5, 1.5),
('Elite', 4, 5, 24000, 9600, 2400, true, 1.5, 1.5);

-- Legends Tier (Level 5) - Global Top 1%
INSERT INTO public.league_tiers (name, level, division, prize_money_base, promotion_bonus, relegation_penalty, tactics_bonus_enabled, scouting_quality_multiplier, fanbase_growth_multiplier) VALUES
('Legends', 5, 1, 100000, 40000, 10000, true, 2.0, 2.0),
('Legends', 5, 2, 90000, 36000, 9000, true, 2.0, 2.0),
('Legends', 5, 3, 80000, 32000, 8000, true, 2.0, 2.0),
('Legends', 5, 4, 70000, 28000, 7000, true, 2.0, 2.0),
('Legends', 5, 5, 60000, 24000, 6000, true, 2.0, 2.0);

-- Create initial season
INSERT INTO public.seasons (name, start_date, end_date, total_matchdays, status) VALUES
('Season 2024/25', NOW(), NOW() + INTERVAL '14 days', 26, 'preparing');

-- Create leagues for Bronze Division V (starting league for new players)
INSERT INTO public.leagues (tier_id, season_id, name, status)
SELECT 
    lt.id,
    s.id,
    'Bronze Division V - League ' || generate_series(1, 10),
    'preparing'
FROM public.league_tiers lt
CROSS JOIN public.seasons s
WHERE lt.name = 'Bronze' AND lt.division = 5 AND s.name = 'Season 2024/25';
