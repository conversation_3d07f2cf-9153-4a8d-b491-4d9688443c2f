-- Enhanced League System Setup Script
-- Run this after the main schema to set up the enhanced league system

-- First, run the league tiers setup
\i supabase-league-tiers-setup.sql

-- Update existing teams to be in Bronze Division V by default
UPDATE public.teams 
SET league_id = (
  SELECT l.id 
  FROM public.leagues l
  JOIN public.league_tiers lt ON l.tier_id = lt.id
  WHERE lt.name = 'Bronze' AND lt.division = 5
  LIMIT 1
)
WHERE league_id IS NULL;

-- Create some sample match notifications for testing
INSERT INTO public.match_notifications (user_id, match_id, notification_type, scheduled_time, message)
SELECT 
  t.user_id,
  m.id,
  'pre_match',
  m.match_date - INTERVAL '15 minutes',
  'Your match between ' || ht.name || ' and ' || at.name || ' starts in 15 minutes!'
FROM public.matches m
JOIN public.teams ht ON m.home_team_id = ht.id
JOIN public.teams at ON m.away_team_id = at.id
JOIN public.teams t ON (t.id = ht.id OR t.id = at.id)
WHERE m.match_date > NOW()
LIMIT 10;

-- Create some sample league standings
INSERT INTO public.league_standings (league_id, team_id, season_id, position, matches_played, wins, draws, losses, goals_for, goals_against, points, form)
SELECT 
  t.league_id,
  t.id,
  s.id,
  ROW_NUMBER() OVER (PARTITION BY t.league_id ORDER BY RANDOM()),
  FLOOR(RANDOM() * 10) + 5, -- 5-15 matches played
  FLOOR(RANDOM() * 8) + 2,  -- 2-10 wins
  FLOOR(RANDOM() * 3) + 1,  -- 1-4 draws
  FLOOR(RANDOM() * 5) + 1,  -- 1-6 losses
  FLOOR(RANDOM() * 20) + 10, -- 10-30 goals for
  FLOOR(RANDOM() * 15) + 5,  -- 5-20 goals against
  (FLOOR(RANDOM() * 8) + 2) * 3 + (FLOOR(RANDOM() * 3) + 1), -- Points calculation
  '["W","L","D","W","L"]'::jsonb -- Sample form
FROM public.teams t
JOIN public.seasons s ON s.status = 'active'
WHERE t.league_id IS NOT NULL;

-- Update points correctly based on wins and draws
UPDATE public.league_standings 
SET points = (wins * 3) + draws;

-- Create some sample season rewards for completed seasons
INSERT INTO public.season_rewards (season_id, team_id, league_id, final_position, prize_money, promotion_bonus, relegation_penalty)
SELECT 
  s.id,
  ls.team_id,
  ls.league_id,
  ls.position,
  CASE 
    WHEN ls.position <= 3 THEN 15000 + (4 - ls.position) * 5000
    WHEN ls.position >= 12 THEN 5000
    ELSE 10000
  END,
  CASE WHEN ls.position <= 3 THEN 5000 ELSE 0 END,
  CASE WHEN ls.position >= 12 THEN 2000 ELSE 0 END
FROM public.seasons s
JOIN public.league_standings ls ON ls.season_id = s.id
WHERE s.status = 'finished'
LIMIT 50;

-- Add some sample match events for analytics
INSERT INTO public.match_events (match_id, team_id, player_id, event_type, minute, quarter, description, impact_rating)
SELECT 
  m.id,
  CASE WHEN RANDOM() > 0.5 THEN m.home_team_id ELSE m.away_team_id END,
  p.id,
  CASE 
    WHEN RANDOM() > 0.7 THEN 'goal'
    WHEN RANDOM() > 0.5 THEN 'save'
    WHEN RANDOM() > 0.3 THEN 'exclusion'
    ELSE 'penalty'
  END,
  FLOOR(RANDOM() * 32) + 1,
  FLOOR(RANDOM() * 4) + 1,
  'Sample event description',
  RANDOM() * 2 - 1 -- -1 to 1 impact rating
FROM public.matches m
JOIN public.players p ON (p.team_id = m.home_team_id OR p.team_id = m.away_team_id)
WHERE m.home_score IS NOT NULL AND m.away_score IS NOT NULL
AND RANDOM() > 0.8 -- Only add events to 20% of matches
LIMIT 200;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_matches_league_season ON public.matches(league_id, season_id);
CREATE INDEX IF NOT EXISTS idx_matches_date ON public.matches(match_date);
CREATE INDEX IF NOT EXISTS idx_match_events_match_player ON public.match_events(match_id, player_id);
CREATE INDEX IF NOT EXISTS idx_match_events_type ON public.match_events(event_type);
CREATE INDEX IF NOT EXISTS idx_league_standings_league_season ON public.league_standings(league_id, season_id);
CREATE INDEX IF NOT EXISTS idx_league_standings_position ON public.league_standings(position);
CREATE INDEX IF NOT EXISTS idx_match_notifications_user_scheduled ON public.match_notifications(user_id, scheduled_time);
CREATE INDEX IF NOT EXISTS idx_season_rewards_season_team ON public.season_rewards(season_id, team_id);

-- Create a function to automatically update league standings
CREATE OR REPLACE FUNCTION update_league_standings()
RETURNS TRIGGER AS $$
BEGIN
  -- Update standings when match result is updated
  IF NEW.home_score IS NOT NULL AND NEW.away_score IS NOT NULL THEN
    -- Update home team standings
    INSERT INTO public.league_standings (league_id, team_id, season_id, position, matches_played, wins, draws, losses, goals_for, goals_against, points)
    VALUES (NEW.league_id, NEW.home_team_id, NEW.season_id, 1, 0, 0, 0, 0, 0, 0, 0)
    ON CONFLICT (league_id, team_id, season_id) DO NOTHING;
    
    -- Update away team standings
    INSERT INTO public.league_standings (league_id, team_id, season_id, position, matches_played, wins, draws, losses, goals_for, goals_against, points)
    VALUES (NEW.league_id, NEW.away_team_id, NEW.season_id, 1, 0, 0, 0, 0, 0, 0, 0)
    ON CONFLICT (league_id, team_id, season_id) DO NOTHING;
    
    -- Recalculate standings (simplified version)
    -- In a real implementation, this would be more sophisticated
    UPDATE public.league_standings 
    SET updated_at = NOW()
    WHERE league_id = NEW.league_id AND season_id = NEW.season_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic standings updates
DROP TRIGGER IF EXISTS trigger_update_league_standings ON public.matches;
CREATE TRIGGER trigger_update_league_standings
  AFTER UPDATE ON public.matches
  FOR EACH ROW
  EXECUTE FUNCTION update_league_standings();

-- Create a function to process match notifications
CREATE OR REPLACE FUNCTION process_match_notifications()
RETURNS INTEGER AS $$
DECLARE
  notification_count INTEGER := 0;
BEGIN
  -- Mark due notifications as sent
  UPDATE public.match_notifications 
  SET sent_at = NOW()
  WHERE sent_at IS NULL 
    AND scheduled_time <= NOW();
  
  GET DIAGNOSTICS notification_count = ROW_COUNT;
  
  RETURN notification_count;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION update_league_standings() TO authenticated;
GRANT EXECUTE ON FUNCTION process_match_notifications() TO authenticated;

-- Insert some sample data for testing
INSERT INTO public.economy_transactions (user_id, type, source, coins_change, description, metadata)
SELECT 
  p.id,
  'earn',
  'season_reward',
  FLOOR(RANDOM() * 10000) + 5000,
  'Season completion reward',
  '{"position": 5, "tier": "Bronze V"}'::jsonb
FROM public.profiles p
WHERE EXISTS (SELECT 1 FROM public.teams WHERE user_id = p.id)
LIMIT 10;

-- Create a view for easy league standings access
CREATE OR REPLACE VIEW league_standings_view AS
SELECT 
  ls.*,
  t.name as team_name,
  t.logo_url as team_logo,
  l.name as league_name,
  lt.name as tier_name,
  lt.division as tier_division,
  s.name as season_name
FROM public.league_standings ls
JOIN public.teams t ON ls.team_id = t.id
JOIN public.leagues l ON ls.league_id = l.id
JOIN public.league_tiers lt ON l.tier_id = lt.id
JOIN public.seasons s ON ls.season_id = s.id
ORDER BY ls.league_id, ls.position;

-- Grant access to the view
GRANT SELECT ON league_standings_view TO authenticated;

COMMIT;
