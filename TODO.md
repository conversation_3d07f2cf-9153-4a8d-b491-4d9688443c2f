fixes:
- the search bar in the sI dupport page has white texts that blends in with the background
- make the blogs prettier
- improve UI
- fix setting page
- league games 
- improve training mechanics select all are multiple
- join league
- improve transfer bids UI and function
- transfers availible coins

add ons:
- google sign in
- step by step guide animation
- auto simulation for games




the game:

this is the game mechanics that you should build into this web

🎯 Game Overview

web-based water polo management simulation game . Players take on the role of manager and head coach of a water polo club, controlling training, tactics, player development, and club progression — not direct gameplay.

⸻

🧩 Gameplay Loop
	1.	Login / Daily Entry
	•	Daily rewards 
	2.	Training & Team Setup
	•	Assign players to training drills
	•	Earn XP to improve player stats
	•	Unlock perks at key level thresholds
	3.	Squad & Formation Management
	•	Choose 7 starters and assign positions
	•	Set tactics and game strategies
	4.	Match Simulation
	•	Scheduled matches are simulated live
	•	Managers can substitute, timeout, or change tactics
	•	Results are based on stats, tactics, and morale
	5.	Post-Match Results
	•	Score, ratings, injuries, and XP
	•	Morale and fatigue adjustments
	6.	Progression
	•	Upgrade training pool, youth academy, recovery room, stadium
	•	Participate in new competitions and unlock advanced systems

⸻

🧠 Core Systems

Player Model

Each player has stats and metadata:
	•	Stats: shooting, speed, endurance, passing, defense, awareness(for non-goalkeeper)
stats: endurance, passing, defense, awareness, goalkeeping(for goalkeeper)
	•	Roles: Goalkeeper, Wing(L/R), Driver, Point, Center Forward
	•	Attributes: morale, fatigue, perks, injuries

Match Engine
	•	Uses logic based on:
	•	Team formations
	•	Tactic style (zone, press, counter, etc.)
	•	Player comparisons (1v1 matchups)
	•	Output includes:
	•	Real-time simulated match feed
	•	Score, events, and play-by-play commentary

Training Logic
	•	Sessions affect specific stats 
	•	Training intensity impacts fatigue
	•	Recovery pool improves recovery time

Transfers & Contracts
	•	Market system includes:
	•	Free agents
	•	Transfer auctions
	•	Youth academy prospects
	•	Contracts include:
	•	Morale modifiers
	•	Contract years

League & Tournament Logic
	•	14-team leagues
	•	Matches occur every 6 hours
	•	Season cycle: 14 days
	•	Knockout cups and association tournaments

⸻

📈 Facilities System

Facility	Benefit
Training Pool	Boosts player XP gain
Recovery Pool	Reduces post-match fatigue
Youth Academy	Improves prospect quality
Aquatic Arena	Increases income and fanbase growth


⸻

💵 In-Game Economy

Currency Types
	•	Coins: earned via matches, used for upgrades and signings
	•	Tokens: premium currency for super good players in auctions and speed-ups
	•	Boosters: consumables for healing or XP gain

Monetization Options
	•	Premium pack purchases
	•	Entry tickets for elite tournaments

⸻

📲 Social & Online Features
	•	Friends system
	•	Leaderboards
	•	Association chat
	•	Custom logos and uniforms

⸻

🌐 Events & Special Modes
none for now

⸻

🔐 Supabase Integration

Tables:
	•	users
	•	players
	•	formations
	•	matches
	•	training_sessions
	•	facilities
	•	transactions

⸻

📆 Time-Based Systems
	•	League and cup matches run on a 6 hour cycle
	•	Match results and history stored temporarily (24h replay log)

⸻

✅ Summary

the game functions as an asynchronous sports strategy simulation game. Players focus on smart decision-making and long-term planning. The game rewards:
	•	Tactical thinking
	•	Squad development
	•	Efficient resource and time management

It is designed for daily play, competitive growth, and team pride — making it the first full-featured water polo management game of its kind.



league details:


🏆 League System 

⸻

📅 Season Format
	•	Length: 14 days (2 weeks)
	•	Teams per league: 14 managers (human or bot-filled)
	•	Matches per team: 26 (home and away against each team)
	•	Match days: 4 matches per day (every 6 hours)
00:00/24:00 06:00 12:00 18:00

⸻

🗂️ League Tiers

Tier	            Description
Bronze(I,II,III,IV,V)	Starter leagues; most users begin here
Silver(I,II,III,IV,V)	Intermediate; introduces tactics bonuses
Gold(I,II,III,IV,V)	    High skill required; more rewards
Elite(I,II,III,IV,V)	    Top-tier; invitation-based or by MMR
Legends(I,II,III,IV,V)	Global Top 1%; exclusive tournaments

Tiers affect prize pools, fanbase growth, and scouting odds.

⸻

🧮 League Rules & Structure

📊 Standings & Points

Outcome	Points Awarded
Win	       3
Draw	   1
Loss	   0

goals
GF	GA	GD

🔁 Promotion / Relegation

Rank Range	Result
1st–3rd	Promoted to next tier
4th–11th	Stay in current tier
12th–14th	Relegated one tier

⚔️ Matchmaking
	•	Each league is matched based on:
	•	Manager level
	•	Bot teams fill empty slots but never get relegated or promoted

⸻

🧠 Tactical Depth

During each match:
	•	Managers submit lineup + tactics beforehand
	•	Matches simulate live or auto-resolve if unattended
	•	fatigue affect performance
	•	Dynamic form: morale apply win/loss bonuses

⸻

🏅 Season Rewards

coins depending on the places the team got

Bonus:
	•	Top Scorer & Best GK get badges and 1-time XP boost

⸻

🧾 Match Schedule

Matches are pre-scheduled:
	•	Home/Away alternating
	•	Match results processed server-side
	•	Managers get notifications 15 minutes before kickoff
	•	Live viewer lets you:
	•	Adjust tactics (2 per half)
	•	Call 2 timeout

⸻

📈 Progression Impact

League performance affects:
	•	Manager Level XP
	•	Team Fanbase Growth
	•	Sponsorship Unlocks
	•	Scouting Quality
	•	Facility Income Scaling

⸻

💬 Player Feedback Loop

Between each match:
	•	Coaches receive:
	•	Match analysis
	•	Player form/morale notes
	•	Training suggestions
	•	Weekly league wrap-ups with:
	•	“Player of the Week”
	•	Power Rankings
