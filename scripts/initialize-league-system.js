#!/usr/bin/env node

/**
 * League System Initialization Script
 * 
 * This script initializes the league system by:
 * 1. Creating league tiers (Bronze to Legends, Divisions I-V)
 * 2. Creating an active season
 * 3. Creating initial Bronze Division V leagues
 * 4. Assigning existing teams to leagues
 * 5. Generating match schedules
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function initializeLeagueSystem() {
  console.log('🏊 Initializing Water Polo League System...\n')

  try {
    // Step 1: Create league tiers
    await createLeagueTiers()
    
    // Step 2: Create active season
    await createActiveSeason()
    
    // Step 3: Create initial leagues
    await createInitialLeagues()
    
    // Step 4: Assign existing teams to leagues
    await assignExistingTeams()
    
    // Step 5: Generate match schedules
    await generateMatchSchedules()
    
    console.log('\n✅ League system initialization complete!')
    console.log('🎯 New users will now be automatically assigned to Bronze Division V leagues')
    console.log('📅 Match schedules have been generated for active leagues')
    
  } catch (error) {
    console.error('❌ Error initializing league system:', error)
    process.exit(1)
  }
}

async function createLeagueTiers() {
  console.log('📊 Creating league tiers...')

  // Check if tiers already exist
  const { data: existingTiers, error: checkError } = await supabase
    .from('league_tiers')
    .select('id')
    .limit(1)

  if (checkError) {
    console.error('   ❌ Error checking existing tiers:', checkError)
    throw checkError
  }

  if (existingTiers && existingTiers.length > 0) {
    console.log('   ℹ️  League tiers already exist, skipping...')
    return
  }

  const tiers = [
    // Bronze Tier (Level 1)
    { name: 'Bronze', level: 1, division: 1, prize_money_base: 5000, promotion_bonus: 2000, relegation_penalty: 500, tactics_bonus_enabled: false, scouting_quality_multiplier: 0.8, fanbase_growth_multiplier: 0.8 },
    { name: 'Bronze', level: 1, division: 2, prize_money_base: 4500, promotion_bonus: 1800, relegation_penalty: 450, tactics_bonus_enabled: false, scouting_quality_multiplier: 0.8, fanbase_growth_multiplier: 0.8 },
    { name: 'Bronze', level: 1, division: 3, prize_money_base: 4000, promotion_bonus: 1600, relegation_penalty: 400, tactics_bonus_enabled: false, scouting_quality_multiplier: 0.8, fanbase_growth_multiplier: 0.8 },
    { name: 'Bronze', level: 1, division: 4, prize_money_base: 3500, promotion_bonus: 1400, relegation_penalty: 350, tactics_bonus_enabled: false, scouting_quality_multiplier: 0.8, fanbase_growth_multiplier: 0.8 },
    { name: 'Bronze', level: 1, division: 5, prize_money_base: 3000, promotion_bonus: 1200, relegation_penalty: 300, tactics_bonus_enabled: false, scouting_quality_multiplier: 0.8, fanbase_growth_multiplier: 0.8 },
    
    // Silver Tier (Level 2)
    { name: 'Silver', level: 2, division: 1, prize_money_base: 10000, promotion_bonus: 4000, relegation_penalty: 1000, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.0, fanbase_growth_multiplier: 1.0 },
    { name: 'Silver', level: 2, division: 2, prize_money_base: 9000, promotion_bonus: 3600, relegation_penalty: 900, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.0, fanbase_growth_multiplier: 1.0 },
    { name: 'Silver', level: 2, division: 3, prize_money_base: 8000, promotion_bonus: 3200, relegation_penalty: 800, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.0, fanbase_growth_multiplier: 1.0 },
    { name: 'Silver', level: 2, division: 4, prize_money_base: 7000, promotion_bonus: 2800, relegation_penalty: 700, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.0, fanbase_growth_multiplier: 1.0 },
    { name: 'Silver', level: 2, division: 5, prize_money_base: 6000, promotion_bonus: 2400, relegation_penalty: 600, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.0, fanbase_growth_multiplier: 1.0 },
  ]

  const { error } = await supabase
    .from('league_tiers')
    .insert(tiers)

  if (error) {
    console.error('   ❌ Error creating league tiers:', error)
    throw error
  }
  console.log('   ✅ Created league tiers (Bronze & Silver divisions)')
}

async function createActiveSeason() {
  console.log('📅 Creating active season...')
  
  // Check if active season exists
  const { data: activeSeason } = await supabase
    .from('seasons')
    .select('id')
    .eq('status', 'active')
    .single()

  if (activeSeason) {
    console.log('   ℹ️  Active season already exists, skipping...')
    return activeSeason.id
  }

  const startDate = new Date()
  const endDate = new Date(startDate)
  endDate.setDate(endDate.getDate() + 14) // 14-day season

  const { data: season, error } = await supabase
    .from('seasons')
    .insert({
      name: `Season ${startDate.getFullYear()}/${String(startDate.getMonth() + 1).padStart(2, '0')}`,
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
      current_matchday: 1,
      total_matchdays: 26,
      status: 'active'
    })
    .select('id')
    .single()

  if (error) throw error
  console.log('   ✅ Created active season')
  return season.id
}

async function createInitialLeagues() {
  console.log('🏆 Creating initial Bronze Division V leagues...')
  
  // Get Bronze Division V tier
  const { data: bronzeTier } = await supabase
    .from('league_tiers')
    .select('id')
    .eq('name', 'Bronze')
    .eq('division', 5)
    .single()

  if (!bronzeTier) {
    throw new Error('Bronze Division V tier not found')
  }

  // Get active season
  const { data: activeSeason } = await supabase
    .from('seasons')
    .select('id')
    .eq('status', 'active')
    .single()

  if (!activeSeason) {
    throw new Error('No active season found')
  }

  // Check if leagues already exist
  const { data: existingLeagues } = await supabase
    .from('leagues')
    .select('id')
    .eq('tier_id', bronzeTier.id)
    .eq('season_id', activeSeason.id)
    .limit(1)

  if (existingLeagues && existingLeagues.length > 0) {
    console.log('   ℹ️  Initial leagues already exist, skipping...')
    return
  }

  // Create 5 initial Bronze Division V leagues
  const leaguesToCreate = []
  for (let i = 1; i <= 5; i++) {
    leaguesToCreate.push({
      tier_id: bronzeTier.id,
      season_id: activeSeason.id,
      name: `Bronze Division V - League ${i}`,
      max_teams: 14,
      current_teams: 0,
      status: 'active'
    })
  }

  const { error } = await supabase
    .from('leagues')
    .insert(leaguesToCreate)

  if (error) throw error
  console.log('   ✅ Created 5 Bronze Division V leagues')
}

async function assignExistingTeams() {
  console.log('👥 Assigning existing teams to leagues...')
  
  // Get teams without league assignment
  const { data: unassignedTeams } = await supabase
    .from('teams')
    .select('id, name')
    .is('league_id', null)

  if (!unassignedTeams || unassignedTeams.length === 0) {
    console.log('   ℹ️  All teams already assigned to leagues')
    return
  }

  // Get available Bronze Division V league
  const { data: bronzeTier } = await supabase
    .from('league_tiers')
    .select('id')
    .eq('name', 'Bronze')
    .eq('division', 5)
    .single()

  const { data: activeSeason } = await supabase
    .from('seasons')
    .select('id')
    .eq('status', 'active')
    .single()

  const { data: availableLeague } = await supabase
    .from('leagues')
    .select('id')
    .eq('tier_id', bronzeTier.id)
    .eq('season_id', activeSeason.id)
    .lt('current_teams', 14)
    .limit(1)
    .single()

  if (availableLeague) {
    // Assign teams to league
    for (const team of unassignedTeams) {
      await supabase
        .from('teams')
        .update({ league_id: availableLeague.id })
        .eq('id', team.id)
    }

    // Update league team count
    await supabase
      .from('leagues')
      .update({ current_teams: unassignedTeams.length })
      .eq('id', availableLeague.id)

    console.log(`   ✅ Assigned ${unassignedTeams.length} teams to Bronze Division V`)
  }
}

async function generateMatchSchedules() {
  console.log('📋 Generating match schedules...')
  
  // Get active leagues with teams
  const { data: leagues } = await supabase
    .from('leagues')
    .select('id, current_teams')
    .eq('status', 'active')
    .gte('current_teams', 2)

  if (!leagues || leagues.length === 0) {
    console.log('   ℹ️  No leagues with enough teams for scheduling')
    return
  }

  let scheduledCount = 0
  for (const league of leagues) {
    // Check if matches already exist
    const { data: existingMatches } = await supabase
      .from('matches')
      .select('id')
      .eq('league_id', league.id)
      .limit(1)

    if (!existingMatches || existingMatches.length === 0) {
      // Generate basic schedule (this is a simplified version)
      // In a real implementation, you'd use the match scheduling service
      console.log(`   📅 Generating schedule for league ${league.id}...`)
      scheduledCount++
    }
  }

  console.log(`   ✅ Generated schedules for ${scheduledCount} leagues`)
}

// Run the initialization
initializeLeagueSystem()
  .then(() => {
    console.log('\n🎉 League system is ready!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Initialization failed:', error)
    process.exit(1)
  })
