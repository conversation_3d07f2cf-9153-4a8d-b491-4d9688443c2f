#!/usr/bin/env node

/**
 * Simple League Setup Script
 * 
 * This script creates a basic league system using the existing database structure.
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupSimpleLeagues() {
  console.log('🏊 Setting up simple league system...\n')

  try {
    // Step 1: Check existing league structure
    await checkLeagueStructure()
    
    // Step 2: Create basic leagues
    await createBasicLeagues()
    
    // Step 3: Assign teams to leagues
    await assignTeamsToLeagues()
    
    // Step 4: Create some basic matches
    await createBasicMatches()
    
    console.log('\n✅ Simple league system setup complete!')
    
  } catch (error) {
    console.error('❌ Error setting up leagues:', error)
    process.exit(1)
  }
}

async function checkLeagueStructure() {
  console.log('🔍 Checking league structure...')
  
  // Check what columns exist in leagues table
  const { data: leagues, error } = await supabase
    .from('leagues')
    .select('*')
    .limit(1)
  
  if (error) {
    console.error('   ❌ Error checking leagues:', error)
    throw error
  }
  
  console.log('   ✅ Leagues table structure checked')
  
  if (leagues && leagues.length > 0) {
    console.log('   📋 Sample league columns:', Object.keys(leagues[0]))
  }
}

async function createBasicLeagues() {
  console.log('🏆 Creating basic leagues...')
  
  // Check if leagues already exist
  const { data: existingLeagues } = await supabase
    .from('leagues')
    .select('id, name')
    .limit(5)
  
  if (existingLeagues && existingLeagues.length > 0) {
    console.log('   ℹ️  Leagues already exist:')
    existingLeagues.forEach(league => {
      console.log(`     - ${league.name}`)
    })
    return
  }
  
  // Create basic leagues
  const leaguesToCreate = [
    {
      name: 'Bronze Division V - League 1',
      level: 1,
      max_teams: 14,
      season_start_date: new Date().toISOString(),
      season_end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
      current_matchday: 1,
      status: 'active',
      prize_money: 3000
    },
    {
      name: 'Bronze Division V - League 2',
      level: 1,
      max_teams: 14,
      season_start_date: new Date().toISOString(),
      season_end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
      current_matchday: 1,
      status: 'active',
      prize_money: 3000
    },
    {
      name: 'Bronze Division V - League 3',
      level: 1,
      max_teams: 14,
      season_start_date: new Date().toISOString(),
      season_end_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
      current_matchday: 1,
      status: 'active',
      prize_money: 3000
    }
  ]
  
  const { data: createdLeagues, error } = await supabase
    .from('leagues')
    .insert(leaguesToCreate)
    .select()
  
  if (error) {
    console.error('   ❌ Error creating leagues:', error)
    throw error
  }
  
  console.log(`   ✅ Created ${createdLeagues.length} leagues`)
}

async function assignTeamsToLeagues() {
  console.log('👥 Assigning teams to leagues...')
  
  // Get teams without league assignment
  const { data: unassignedTeams } = await supabase
    .from('teams')
    .select('id, name, league_id')
    .is('league_id', null)
  
  if (!unassignedTeams || unassignedTeams.length === 0) {
    console.log('   ℹ️  All teams already assigned to leagues')
    return
  }
  
  // Get available league
  const { data: availableLeague } = await supabase
    .from('leagues')
    .select('id, name')
    .eq('status', 'active')
    .limit(1)
    .single()
  
  if (!availableLeague) {
    console.log('   ⚠️  No available leagues found')
    return
  }
  
  // Assign teams to league
  for (const team of unassignedTeams) {
    const { error } = await supabase
      .from('teams')
      .update({ league_id: availableLeague.id })
      .eq('id', team.id)
    
    if (error) {
      console.error(`   ❌ Error assigning team ${team.name}:`, error)
    } else {
      console.log(`   ✅ Assigned ${team.name} to ${availableLeague.name}`)
    }
  }
}

async function createBasicMatches() {
  console.log('📅 Creating basic matches...')
  
  // Get teams in leagues
  const { data: teams } = await supabase
    .from('teams')
    .select('id, name, league_id')
    .not('league_id', 'is', null)
  
  if (!teams || teams.length < 2) {
    console.log('   ⚠️  Not enough teams in leagues to create matches')
    return
  }
  
  // Group teams by league
  const teamsByLeague = teams.reduce((acc, team) => {
    if (!acc[team.league_id]) {
      acc[team.league_id] = []
    }
    acc[team.league_id].push(team)
    return acc
  }, {})
  
  // Create matches for each league
  for (const [leagueId, leagueTeams] of Object.entries(teamsByLeague)) {
    if (leagueTeams.length < 2) continue
    
    // Check if matches already exist for this league
    const { data: existingMatches } = await supabase
      .from('matches')
      .select('id')
      .eq('league_id', leagueId)
      .limit(1)
    
    if (existingMatches && existingMatches.length > 0) {
      console.log(`   ℹ️  Matches already exist for league ${leagueId}`)
      continue
    }
    
    // Create a few sample matches
    const matchesToCreate = []
    for (let i = 0; i < Math.min(3, Math.floor(leagueTeams.length / 2)); i++) {
      const homeTeam = leagueTeams[i * 2]
      const awayTeam = leagueTeams[i * 2 + 1]
      
      if (homeTeam && awayTeam) {
        const matchDate = new Date()
        matchDate.setDate(matchDate.getDate() + i + 1) // Matches on consecutive days
        
        matchesToCreate.push({
          home_team_id: homeTeam.id,
          away_team_id: awayTeam.id,
          league_id: leagueId,
          match_date: matchDate.toISOString(),
          competition: 'league',
          status: 'scheduled',
          matchday: i + 1
        })
      }
    }
    
    if (matchesToCreate.length > 0) {
      const { error } = await supabase
        .from('matches')
        .insert(matchesToCreate)
      
      if (error) {
        console.error(`   ❌ Error creating matches for league ${leagueId}:`, error)
      } else {
        console.log(`   ✅ Created ${matchesToCreate.length} matches for league ${leagueId}`)
      }
    }
  }
}

// Run the setup
setupSimpleLeagues()
  .then(() => {
    console.log('\n🎉 Simple league system is ready!')
    console.log('🎯 Teams are now assigned to leagues')
    console.log('📅 Basic match schedule created')
    console.log('🌐 You can now test the league functionality in the web app')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Setup failed:', error)
    process.exit(1)
  })
