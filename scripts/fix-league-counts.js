#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function fixLeagueCounts() {
  console.log('🔧 Fixing league team counts...\n')

  try {
    // Get all leagues
    const { data: leagues, error: leaguesError } = await supabase
      .from('leagues')
      .select('id, name, current_teams')
    
    if (leaguesError) {
      console.error('❌ Error fetching leagues:', leaguesError)
      return
    }
    
    console.log('🏆 Processing leagues...')
    
    for (const league of leagues) {
      // Count actual teams in this league
      const { data: teams, error: teamsError } = await supabase
        .from('teams')
        .select('id')
        .eq('league_id', league.id)
      
      if (teamsError) {
        console.error(`❌ Error counting teams for league ${league.name}:`, teamsError)
        continue
      }
      
      const actualCount = teams ? teams.length : 0
      const storedCount = league.current_teams || 0
      
      console.log(`  - ${league.name}:`)
      console.log(`    Stored count: ${storedCount}`)
      console.log(`    Actual count: ${actualCount}`)
      
      if (actualCount !== storedCount) {
        console.log(`    🔄 Updating count to ${actualCount}...`)
        
        const { error: updateError } = await supabase
          .from('leagues')
          .update({ current_teams: actualCount })
          .eq('id', league.id)
        
        if (updateError) {
          console.error(`    ❌ Error updating count:`, updateError)
        } else {
          console.log(`    ✅ Updated successfully`)
        }
      } else {
        console.log(`    ✅ Count is correct`)
      }
      console.log('')
    }
    
    console.log('✅ League counts fixed!')
    
  } catch (error) {
    console.error('❌ Error fixing league counts:', error)
  }
}

fixLeagueCounts().catch(console.error)
