#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function addMissingColumns() {
  console.log('🔧 Adding missing columns to leagues table...\n')

  try {
    // First, let's see what columns exist
    const { data: leagues, error: leaguesError } = await supabase
      .from('leagues')
      .select('*')
      .limit(1)
    
    if (leaguesError) {
      console.error('❌ Error fetching leagues:', leaguesError)
      return
    }
    
    if (leagues && leagues.length > 0) {
      console.log('📋 Current league columns:', Object.keys(leagues[0]))
    }
    
    // Try to add the current_teams column using a direct SQL approach
    console.log('🔄 Attempting to add current_teams column...')
    
    // Since we can't execute raw SQL directly, let's update the leagues to have the missing data
    // by calculating team counts and storing them in existing fields or working around it
    
    const { data: allLeagues } = await supabase
      .from('leagues')
      .select('id, name')
    
    if (allLeagues) {
      console.log('📊 Calculating team counts for each league...')
      
      for (const league of allLeagues) {
        const { data: teams } = await supabase
          .from('teams')
          .select('id')
          .eq('league_id', league.id)
        
        const teamCount = teams ? teams.length : 0
        console.log(`  - ${league.name}: ${teamCount} teams`)
      }
    }
    
    console.log('\n💡 Note: The current_teams column needs to be added manually in the database.')
    console.log('   You can add it with: ALTER TABLE leagues ADD COLUMN current_teams INTEGER DEFAULT 0;')
    
  } catch (error) {
    console.error('❌ Error:', error)
  }
}

addMissingColumns().catch(console.error)
