#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function finalVerification() {
  console.log('🎯 Final League System Verification\n')

  try {
    // Test 1: Check teams are assigned to leagues
    console.log('1️⃣ Checking team league assignments...')
    const { data: teams } = await supabase
      .from('teams')
      .select('id, name, league_id')
    
    const assignedTeams = teams?.filter(t => t.league_id) || []
    const unassignedTeams = teams?.filter(t => !t.league_id) || []
    
    console.log(`   ✅ ${assignedTeams.length} teams assigned to leagues`)
    if (unassignedTeams.length > 0) {
      console.log(`   ⚠️  ${unassignedTeams.length} teams still unassigned`)
    }
    
    // Test 2: Check league page query works
    console.log('\n2️⃣ Testing league page query...')
    if (assignedTeams.length > 0) {
      const testTeam = assignedTeams[0]
      const { data: league, error } = await supabase
        .from('leagues')
        .select('*')
        .eq('id', testTeam.league_id)
        .single()
      
      if (error) {
        console.log('   ❌ League query failed:', error.message)
      } else {
        console.log('   ✅ League query successful:', league.name)
      }
    }
    
    // Test 3: Check league standings data
    console.log('\n3️⃣ Testing league standings...')
    const { data: leagues } = await supabase
      .from('leagues')
      .select('id, name')
      .limit(1)
    
    if (leagues && leagues.length > 0) {
      const { data: leagueTeams } = await supabase
        .from('teams')
        .select('id, name, league_points, matches_played')
        .eq('league_id', leagues[0].id)
      
      console.log(`   ✅ Found ${leagueTeams?.length || 0} teams in ${leagues[0].name}`)
      if (leagueTeams && leagueTeams.length > 0) {
        console.log('   📊 Sample team stats:')
        leagueTeams.slice(0, 3).forEach(team => {
          console.log(`     - ${team.name}: ${team.league_points || 0} pts, ${team.matches_played || 0} matches`)
        })
      }
    }
    
    // Test 4: Check match data
    console.log('\n4️⃣ Testing match data...')
    const { data: matches } = await supabase
      .from('matches')
      .select('id, home_team_id, away_team_id, status')
      .limit(5)
    
    console.log(`   ✅ Found ${matches?.length || 0} matches in database`)
    
    // Test 5: Verify no broken queries
    console.log('\n5️⃣ Checking for problematic queries...')
    try {
      // This should fail gracefully if league_tiers doesn't exist
      const { error: tierError } = await supabase
        .from('league_tiers')
        .select('id')
        .limit(1)
      
      if (tierError && tierError.code === '42P01') {
        console.log('   ✅ league_tiers table missing (expected for simplified setup)')
      } else if (tierError) {
        console.log('   ⚠️  Unexpected error with league_tiers:', tierError.message)
      } else {
        console.log('   ✅ league_tiers table exists')
      }
    } catch (e) {
      console.log('   ✅ league_tiers check handled gracefully')
    }
    
    console.log('\n🎉 VERIFICATION COMPLETE!')
    console.log('\n📋 SUMMARY:')
    console.log(`   ✅ Teams assigned: ${assignedTeams.length}`)
    console.log(`   ✅ Leagues active: ${leagues?.length || 0}`)
    console.log(`   ✅ Matches created: ${matches?.length || 0}`)
    console.log(`   ✅ Database queries working`)
    console.log(`   ✅ League page should be functional`)
    
    console.log('\n🌐 TEST THE LEAGUE PAGE:')
    console.log('   1. Open: http://localhost:3003/league')
    console.log('   2. Expected: Full league interface (no "Assignment in Progress")')
    console.log('   3. Features: Overview, Schedule, Standings, Analytics tabs')
    
    if (assignedTeams.length === 0) {
      console.log('\n⚠️  NO TEAMS ASSIGNED - Run setup script:')
      console.log('   node scripts/setup-simple-leagues.js')
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error)
  }
}

finalVerification().catch(console.error)
