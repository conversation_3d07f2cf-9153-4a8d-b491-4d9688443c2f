#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function checkDatabase() {
  console.log('🔍 Checking database structure...\n')

  const tables = ['profiles', 'teams', 'players', 'leagues', 'league_tiers', 'seasons', 'matches']
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1)
      
      if (error) {
        console.log(`❌ ${table}: ${error.message}`)
      } else {
        console.log(`✅ ${table}: exists`)
      }
    } catch (err) {
      console.log(`❌ ${table}: ${err.message}`)
    }
  }
  
  // Check existing teams
  console.log('\n👥 Checking existing teams...')
  const { data: teams, error: teamsError } = await supabase
    .from('teams')
    .select('id, name, league_id')
    .limit(10)
  
  if (teams) {
    console.log(`Found ${teams.length} teams:`)
    teams.forEach(team => {
      console.log(`  - ${team.name} (League: ${team.league_id || 'None'})`)
    })
  }
}

checkDatabase().catch(console.error)
