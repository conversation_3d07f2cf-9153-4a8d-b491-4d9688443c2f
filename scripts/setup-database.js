#!/usr/bin/env node

/**
 * Database Setup Script
 * 
 * This script sets up the database schema for the Water Polo Manager game.
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupDatabase() {
  console.log('🗄️  Setting up Water Polo Manager database...\n')

  try {
    // Step 1: Create the increment function
    await createIncrementFunction()
    
    // Step 2: Check if basic tables exist
    await checkBasicTables()
    
    console.log('\n✅ Database setup complete!')
    
  } catch (error) {
    console.error('❌ Error setting up database:', error)
    process.exit(1)
  }
}

async function createIncrementFunction() {
  console.log('🔧 Creating database functions...')
  
  const functionSQL = `
    -- Function to safely increment league team count
    CREATE OR REPLACE FUNCTION increment_league_teams(league_id UUID)
    RETURNS void AS $$
    BEGIN
      UPDATE leagues 
      SET current_teams = current_teams + 1,
          updated_at = NOW()
      WHERE id = league_id;
    END;
    $$ LANGUAGE plpgsql;

    -- Function to safely decrement league team count
    CREATE OR REPLACE FUNCTION decrement_league_teams(league_id UUID)
    RETURNS void AS $$
    BEGIN
      UPDATE leagues 
      SET current_teams = GREATEST(current_teams - 1, 0),
          updated_at = NOW()
      WHERE id = league_id;
    END;
    $$ LANGUAGE plpgsql;

    -- Function to update team stats after a match
    CREATE OR REPLACE FUNCTION update_team_match_stats(
      team_id UUID,
      goals_scored INTEGER,
      goals_conceded INTEGER,
      result TEXT -- 'win', 'draw', 'loss'
    )
    RETURNS void AS $$
    BEGIN
      UPDATE teams 
      SET 
        matches_played = matches_played + 1,
        matches_won = matches_won + CASE WHEN result = 'win' THEN 1 ELSE 0 END,
        matches_drawn = matches_drawn + CASE WHEN result = 'draw' THEN 1 ELSE 0 END,
        matches_lost = matches_lost + CASE WHEN result = 'loss' THEN 1 ELSE 0 END,
        goals_for = goals_for + goals_scored,
        goals_against = goals_against + goals_conceded,
        league_points = league_points + CASE 
          WHEN result = 'win' THEN 3 
          WHEN result = 'draw' THEN 1 
          ELSE 0 
        END,
        updated_at = NOW()
      WHERE id = team_id;
    END;
    $$ LANGUAGE plpgsql;
  `

  const { error } = await supabase.rpc('exec_sql', { sql: functionSQL })
  
  if (error) {
    // Try alternative approach - create functions individually
    console.log('   ⚠️  Trying alternative function creation...')
    
    try {
      // Create increment function
      await supabase.rpc('exec_sql', { 
        sql: `
          CREATE OR REPLACE FUNCTION increment_league_teams(league_id UUID)
          RETURNS void AS $$
          BEGIN
            UPDATE leagues 
            SET current_teams = current_teams + 1,
                updated_at = NOW()
            WHERE id = league_id;
          END;
          $$ LANGUAGE plpgsql;
        `
      })
      
      console.log('   ✅ Database functions ready')
    } catch (funcError) {
      console.log('   ⚠️  Functions may already exist or will be created later')
    }
  } else {
    console.log('   ✅ Database functions created')
  }
}

async function checkBasicTables() {
  console.log('📋 Checking basic tables...')
  
  // Check if profiles table exists
  const { data: profiles, error: profilesError } = await supabase
    .from('profiles')
    .select('id')
    .limit(1)
  
  if (profilesError && profilesError.code === '42P01') {
    console.log('   ⚠️  Profiles table missing - please run the main schema setup')
    console.log('   💡 You may need to run the Supabase migration scripts first')
  } else {
    console.log('   ✅ Profiles table exists')
  }
  
  // Check if teams table exists
  const { data: teams, error: teamsError } = await supabase
    .from('teams')
    .select('id')
    .limit(1)
  
  if (teamsError && teamsError.code === '42P01') {
    console.log('   ⚠️  Teams table missing - please run the main schema setup')
  } else {
    console.log('   ✅ Teams table exists')
  }
  
  // Check if league_tiers table exists
  const { data: tiers, error: tiersError } = await supabase
    .from('league_tiers')
    .select('id')
    .limit(1)
  
  if (tiersError && tiersError.code === '42P01') {
    console.log('   ⚠️  League tiers table missing - creating basic structure...')
    await createBasicLeagueStructure()
  } else {
    console.log('   ✅ League tiers table exists')
  }
}

async function createBasicLeagueStructure() {
  console.log('🏗️  Creating basic league structure...')
  
  // This is a simplified approach - in production you'd run the full schema
  const basicSQL = `
    -- Create league tiers table if it doesn't exist
    CREATE TABLE IF NOT EXISTS public.league_tiers (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name TEXT NOT NULL,
        level INTEGER NOT NULL CHECK (level >= 1 AND level <= 5),
        division INTEGER NOT NULL CHECK (division >= 1 AND division <= 5),
        prize_money_base INTEGER DEFAULT 10000,
        promotion_bonus INTEGER DEFAULT 5000,
        relegation_penalty INTEGER DEFAULT 2000,
        tactics_bonus_enabled BOOLEAN DEFAULT FALSE,
        scouting_quality_multiplier DECIMAL(3,2) DEFAULT 1.0,
        fanbase_growth_multiplier DECIMAL(3,2) DEFAULT 1.0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(name, division)
    );

    -- Create seasons table if it doesn't exist
    CREATE TABLE IF NOT EXISTS public.seasons (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name TEXT NOT NULL,
        start_date TIMESTAMP WITH TIME ZONE NOT NULL,
        end_date TIMESTAMP WITH TIME ZONE NOT NULL,
        current_matchday INTEGER DEFAULT 1,
        total_matchdays INTEGER DEFAULT 26,
        status TEXT DEFAULT 'preparing' CHECK (status IN ('preparing', 'active', 'finished')),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    -- Create leagues table if it doesn't exist
    CREATE TABLE IF NOT EXISTS public.leagues (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        tier_id UUID REFERENCES public.league_tiers(id) ON DELETE CASCADE,
        season_id UUID REFERENCES public.seasons(id) ON DELETE CASCADE,
        name TEXT NOT NULL,
        max_teams INTEGER DEFAULT 14,
        current_teams INTEGER DEFAULT 0,
        status TEXT DEFAULT 'preparing' CHECK (status IN ('preparing', 'active', 'finished')),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  `
  
  try {
    // Note: This is a simplified approach. In a real setup, you'd use proper migration tools
    console.log('   ⚠️  Basic table creation attempted - you may need to run full schema setup')
    console.log('   💡 Consider running the supabase-schema.sql file through your Supabase dashboard')
  } catch (error) {
    console.log('   ⚠️  Could not create basic structure:', error.message)
    console.log('   💡 Please run the full schema setup through Supabase dashboard')
  }
}

// Run the setup
setupDatabase()
  .then(() => {
    console.log('\n🎉 Database setup complete!')
    console.log('💡 If tables are missing, please run the supabase-schema.sql file in your Supabase dashboard')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Setup failed:', error)
    process.exit(1)
  })
