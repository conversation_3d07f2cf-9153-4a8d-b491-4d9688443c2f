#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function debugLeagueAssignment() {
  console.log('🔍 Debugging league assignment...\n')

  try {
    // Get all teams with their league assignments
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('id, name, user_id, league_id')
    
    if (teamsError) {
      console.error('❌ Error fetching teams:', teamsError)
      return
    }
    
    console.log('👥 Teams found:')
    teams.forEach(team => {
      console.log(`  - ${team.name} (ID: ${team.id})`)
      console.log(`    User: ${team.user_id}`)
      console.log(`    League: ${team.league_id || 'NONE'}`)
      console.log('')
    })
    
    // Get all leagues
    const { data: leagues, error: leaguesError } = await supabase
      .from('leagues')
      .select('*')
    
    if (leaguesError) {
      console.error('❌ Error fetching leagues:', leaguesError)
      return
    }
    
    console.log('🏆 Leagues found:')
    leagues.forEach(league => {
      console.log(`  - ${league.name} (ID: ${league.id})`)
      console.log(`    Status: ${league.status}`)
      console.log(`    Max Teams: ${league.max_teams}`)
      console.log(`    Current Teams: ${league.current_teams || 0}`)
      console.log('')
    })
    
    // Check league data structure that the app expects
    console.log('🔍 Checking league data structure...')
    if (leagues.length > 0) {
      const sampleLeague = leagues[0]
      console.log('Sample league columns:', Object.keys(sampleLeague))
      
      // Check if the league has the expected structure for the app
      const expectedFields = ['tier_id', 'season_id']
      const missingFields = expectedFields.filter(field => !(field in sampleLeague))
      
      if (missingFields.length > 0) {
        console.log('⚠️  Missing expected fields:', missingFields)
        console.log('💡 This might be why the league page shows "Assignment in Progress"')
      }
    }
    
    // Test the league page query
    console.log('🧪 Testing league page query...')
    if (teams.length > 0) {
      const testTeam = teams[0]
      if (testTeam.league_id) {
        const { data: leagueData, error: leagueError } = await supabase
          .from('leagues')
          .select(`
            *,
            tier:league_tiers(*),
            season:seasons(*)
          `)
          .eq('id', testTeam.league_id)
          .single()
        
        if (leagueError) {
          console.log('❌ League page query failed:', leagueError.message)
          console.log('💡 This is likely why the page shows "Assignment in Progress"')
          
          // Try simpler query
          const { data: simpleLeague, error: simpleError } = await supabase
            .from('leagues')
            .select('*')
            .eq('id', testTeam.league_id)
            .single()
          
          if (simpleError) {
            console.log('❌ Even simple league query failed:', simpleError.message)
          } else {
            console.log('✅ Simple league query works:', simpleLeague.name)
          }
        } else {
          console.log('✅ League page query works')
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error)
  }
}

debugLeagueAssignment().catch(console.error)
