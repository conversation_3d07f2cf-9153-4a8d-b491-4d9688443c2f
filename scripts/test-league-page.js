#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testLeaguePage() {
  console.log('🧪 Testing league page data flow...\n')

  try {
    // Simulate the league page data fetching process
    
    // Step 1: Get user profile (simulate)
    console.log('1️⃣ Getting user profile...')
    const userId = 'caead96d-d5f8-4011-8a11-a8be8d216379' // From debug output
    
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()
    
    if (!profile) {
      console.log('❌ No profile found')
      return
    }
    console.log('✅ Profile found:', profile.full_name || profile.email)
    
    // Step 2: Get teams
    console.log('\n2️⃣ Getting teams...')
    const { data: teams } = await supabase
      .from('teams')
      .select('*')
      .eq('user_id', userId)
    
    if (!teams || teams.length === 0) {
      console.log('❌ No teams found')
      return
    }
    
    const team = teams[0] // Use first team
    console.log('✅ Team found:', team.name)
    console.log('   League ID:', team.league_id || 'NONE')
    
    // Step 3: Test league assignment if needed
    if (!team.league_id) {
      console.log('\n3️⃣ Team needs league assignment...')
      // This would trigger the assignment logic
      console.log('⚠️  League assignment would be triggered here')
      return
    }
    
    // Step 4: Get league data (the fixed query)
    console.log('\n3️⃣ Getting league data...')
    const { data: league, error: leagueError } = await supabase
      .from('leagues')
      .select('*')
      .eq('id', team.league_id)
      .single()
    
    if (leagueError) {
      console.log('❌ League query failed:', leagueError.message)
      return
    }
    
    if (!league) {
      console.log('❌ No league found')
      return
    }
    
    console.log('✅ League found:', league.name)
    console.log('   Status:', league.status)
    console.log('   Level:', league.level)
    console.log('   Max Teams:', league.max_teams)
    
    // Step 5: Test what the league page would show
    console.log('\n4️⃣ League page would show:')
    console.log('   ✅ League Overview: Available')
    console.log('   ✅ League Name:', league.name)
    console.log('   ✅ Team in League:', team.name)
    console.log('   ✅ League Status:', league.status)
    
    // Step 6: Test league standings
    console.log('\n5️⃣ Testing league standings...')
    const { data: leagueTeams } = await supabase
      .from('teams')
      .select('*')
      .eq('league_id', league.id)
    
    if (leagueTeams) {
      console.log(`   ✅ Found ${leagueTeams.length} teams in league:`)
      leagueTeams.forEach((t, index) => {
        console.log(`     ${index + 1}. ${t.name} (${t.league_points || 0} pts)`)
      })
    }
    
    console.log('\n🎉 League page should work correctly!')
    console.log('💡 If you\'re still seeing "Assignment in Progress", try:')
    console.log('   1. Clear browser cache')
    console.log('   2. Hard refresh (Ctrl+F5 or Cmd+Shift+R)')
    console.log('   3. Check browser console for any JavaScript errors')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testLeaguePage().catch(console.error)
