'use client'

import { motion } from 'framer-motion'
import MainLayout from '@/components/layout/main-layout'
import { Card, CardContent } from '@/components/ui/card'
import But<PERSON> from '@/components/ui/button'
import { useAuth } from '@/lib/auth-context'
import {
  Users,
  Trophy,
  Target,
  TrendingUp,
  Gamepad2,
  Globe,
  Brain,
  Award,
  Play,
  Shield,
  Zap,
  Star,
  Calendar,
  BarChart3
} from 'lucide-react'
import Link from 'next/link'

export default function FeaturesPage() {
  const { user } = useAuth()
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  const features = [
    {
      icon: Users,
      title: 'Team Management',
      description: 'Build and manage your squad of 14 players with unique skills, positions, and personalities.',
      details: ['7 starting players + 7 substitutes', 'Unique player positions and roles', 'Individual player development paths', 'Team chemistry and morale system']
    },
    {
      icon: Target,
      title: 'Tactical Engine',
      description: 'Create custom formations and strategies to outsmart your opponents in every match.',
      details: ['Drag-and-drop formation builder', 'Real-time tactical adjustments', 'Counter-strategy system', 'Match preparation tools']
    },
    {
      icon: TrendingUp,
      title: 'Player Development',
      description: 'Train your players, improve their stats, and unlock special abilities and traits.',
      details: ['5 core skill categories', 'Individual training programs', 'Youth academy system', 'Special abilities and traits']
    },
    {
      icon: Trophy,
      title: 'Global Competitions',
      description: 'Compete in leagues, cups, and tournaments against managers from around the world.',
      details: ['Weekly league matches', 'Knockout cup competitions', 'International tournaments', 'Live manager battles']
    },
    {
      icon: Brain,
      title: 'Smart AI',
      description: 'Advanced match simulation based on tactics, player stats, and real-time decisions.',
      details: ['Realistic match physics', 'Dynamic weather effects', 'Injury and fatigue system', 'Psychological factors']
    },
    {
      icon: Globe,
      title: 'Global Competition',
      description: 'Compete in worldwide leagues and tournaments to prove your tactical mastery.',
      details: ['International leagues', 'Tournament brackets', 'Global rankings', 'Championship rewards']
    }
  ]

  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-pool-blue via-primary to-pool-dark overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full animate-wave" />
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-white/5 rounded-full animate-wave" style={{ animationDelay: '1s' }} />
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-8"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-white leading-tight">
              Game Features
            </h1>
            <p className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto">
              Discover everything that makes Water Polo Manager the most comprehensive
              and realistic water polo management experience.
            </p>
            <Link href={user ? "/dashboard" : "/login"}>
              <Button
                variant="ghost"
                size="lg"
                className="bg-white hover:bg-gray-50 text-primary hover:text-primary-dark border-2 border-primary hover:border-primary-dark px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl"
                style={{ color: 'var(--primary)' }}
              >
                <Play className="w-5 h-5 mr-2" />
                {user ? "Play" : "Get Started Free"}
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Main Features Grid */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-12"
          >
            {features.map((feature, index) => (
              <motion.div key={feature.title} variants={itemVariants}>
                <Card className="h-full hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/20 bg-card-bg">
                  <CardContent className="p-8">
                    <div className="flex items-start space-x-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center flex-shrink-0 shadow-md">
                        <feature.icon className="w-8 h-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-2xl font-bold text-foreground mb-3">
                          {feature.title}
                        </h3>
                        <p className="text-muted mb-6 leading-relaxed">
                          {feature.description}
                        </p>
                        <ul className="space-y-2">
                          {feature.details.map((detail, i) => (
                            <li key={i} className="flex items-center">
                              <div className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0" />
                              <span className="text-sm text-muted">{detail}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Gameplay Mechanics */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Authentic Water Polo Gameplay
            </h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto">
              Experience the real sport with authentic rules, positions, and strategies.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 bg-card-bg">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md">
                    <Gamepad2 className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-foreground">Real-Time Matches</h3>
                  <p className="text-muted">
                    Watch your team play in live matches with dynamic commentary and realistic gameplay.
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 bg-card-bg">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md">
                    <BarChart3 className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-foreground">Advanced Statistics</h3>
                  <p className="text-muted">
                    Detailed match analysis and player performance metrics to improve your strategy.
                  </p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <Card className="text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20 bg-card-bg">
                <CardContent className="p-8">
                  <div className="w-16 h-16 bg-gradient-to-br from-primary to-secondary rounded-xl flex items-center justify-center mx-auto mb-4 shadow-md">
                    <Calendar className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-foreground">Season Management</h3>
                  <p className="text-muted">
                    Plan your season with training schedules, match preparation, and transfer windows.
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-primary to-secondary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white">
              Ready to Start Your Journey?
            </h2>
            <p className="text-xl text-white/90">
              Join thousands of managers and build your water polo dynasty today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href={user ? "/dashboard" : "/login"}>
                <Button
                  variant="ghost"
                  size="lg"
                  className="bg-white hover:bg-gray-50 border-2 border-primary hover:border-primary-dark px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl"
                  style={{ color: 'var(--primary)' }}
                >
                  <Play className="w-5 h-5 mr-2" />
                  {user ? "Play" : "Get Started - Free"}
                </Button>
              </Link>
              {user && (
                <Link href="/dashboard">
                  <Button
                    variant="ghost"
                    size="lg"
                    className="bg-transparent border-2 border-white hover:bg-white/10 hover:border-white/80 px-8 py-4 text-lg font-semibold"
                    style={{ color: 'white' }}
                  >
                    View Dashboard
                  </Button>
                </Link>
              )}
            </div>
          </motion.div>
        </div>
      </section>
    </MainLayout>
  )
}
