import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/lib/auth-context";
import { CurrencyProvider } from "@/lib/currency-context";
import { RatingProvider } from "@/lib/rating-context";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Water Polo Manager - Build Your Dynasty",
  description: "The ultimate water polo management game. Build your team, develop players, and compete in leagues worldwide.",
  keywords: ["water polo", "sports management", "game", "strategy", "team building"],
  authors: [{ name: "Water Polo Manager Team" }],
  openGraph: {
    title: "Water Polo Manager - Build Your Dynasty",
    description: "The ultimate water polo management game. Build your team, develop players, and compete in leagues worldwide.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Water Polo Manager - Build Your Dynasty",
    description: "The ultimate water polo management game. Build your team, develop players, and compete in leagues worldwide.",
  },
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#0066cc",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <CurrencyProvider>
            <RatingProvider>
              {children}
            </RatingProvider>
          </CurrencyProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
