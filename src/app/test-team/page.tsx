'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/auth-context'
import { defaultTeamService, DEFAULT_ROSTER } from '@/lib/default-team-service'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { Users, Trophy, CheckCircle } from 'lucide-react'

export default function TestTeamPage() {
  const { user, profile } = useAuth()
  const [isCreating, setIsCreating] = useState(false)
  const [result, setResult] = useState<any>(null)

  const handleCreateTeam = async () => {
    if (!user) return

    setIsCreating(true)
    try {
      const teamResult = await defaultTeamService.createDefaultTeam(user.id, 'Test Team FC')
      setResult(teamResult)
    } catch (error) {
      console.error('Error creating team:', error)
      setResult({ success: false, error: 'Failed to create team' })
    } finally {
      setIsCreating(false)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p>Please log in to test team creation</p>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Trophy className="w-6 h-6" />
              <span>Default Team Creation Test</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium mb-2">Current Status</h3>
                <p className="text-sm text-muted">
                  User ID: {user.id}
                </p>
                <p className="text-sm text-muted">
                  Has Team: {profile?.team ? 'Yes' : 'No'}
                </p>
                {profile?.team && (
                  <p className="text-sm text-muted">
                    Team Name: {profile.team.name}
                  </p>
                )}
              </div>
              
              <div>
                <h3 className="font-medium mb-2">Actions</h3>
                <Button
                  onClick={handleCreateTeam}
                  disabled={isCreating || !!profile?.team}
                  className="w-full"
                >
                  {isCreating ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Creating Team...</span>
                    </div>
                  ) : profile?.team ? (
                    'Team Already Exists'
                  ) : (
                    'Create Default Team'
                  )}
                </Button>
              </div>
            </div>

            {result && (
              <div className={`p-4 rounded-lg ${
                result.success ? 'bg-green-50 dark:bg-green-900/20' : 'bg-red-50 dark:bg-red-900/20'
              }`}>
                <div className="flex items-center space-x-2 mb-2">
                  {result.success ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <div className="w-5 h-5 rounded-full bg-red-500" />
                  )}
                  <span className={`font-medium ${
                    result.success ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'
                  }`}>
                    {result.success ? 'Success!' : 'Error'}
                  </span>
                </div>
                <p className="text-sm">
                  {result.success ? 'Default team created successfully' : result.error}
                </p>
                {result.team && (
                  <div className="mt-2 text-sm">
                    <p>Team ID: {result.team.id}</p>
                    <p>Team Name: {result.team.name}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="w-6 h-6" />
              <span>Default Roster Preview</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {DEFAULT_ROSTER.map((player, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                  <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-primary">
                      {player.jersey_number}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{player.name}</div>
                    <div className="text-sm text-muted capitalize">
                      {player.position.replace('-', ' ')} • Age {player.age}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {player.position === 'goalkeeper' 
                        ? `${player.stats.goalkeeping} GK`
                        : `${Math.max(player.stats.shooting, player.stats.speed, player.stats.passing)} OVR`
                      }
                    </div>
                    <div className="text-xs text-muted">
                      Potential: {player.potential}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {profile?.team?.players && (
          <Card>
            <CardHeader>
              <CardTitle>Current Team Players</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {profile.team.players.map((player, index) => (
                  <div key={player.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-sm font-bold text-blue-600 dark:text-blue-300">
                        {player.jersey_number || index + 1}
                      </span>
                    </div>
                    <div className="flex-1">
                      <div className="font-medium">{player.name}</div>
                      <div className="text-sm text-muted capitalize">
                        {player.position.replace('-', ' ')} • Age {player.age}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {player.position === 'goalkeeper' 
                          ? `${player.goalkeeping} GK`
                          : `${Math.max(player.shooting, player.speed, player.passing)} OVR`
                        }
                      </div>
                      <div className="text-xs text-muted">
                        Morale: {player.morale}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
