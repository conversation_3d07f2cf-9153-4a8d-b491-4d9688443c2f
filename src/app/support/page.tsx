'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import MainLayout from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import But<PERSON> from '@/components/ui/button'
import { 
  HelpCircle, 
  Mail, 
  MessageCircle, 
  Bug, 
  Lightbulb,
  ChevronDown,
  ChevronUp,
  Search,
  ExternalLink
} from 'lucide-react'

interface FAQ {
  id: string
  question: string
  answer: string
  category: string
}

export default function SupportPage() {
  const [openFAQ, setOpenFAQ] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')

  const faqs: FAQ[] = [
    {
      id: '1',
      question: 'How do I create my first team?',
      answer: 'After signing in, go to the Team Builder page where you can create your team, add players, and set up your formation. Start with the default formation and customize it as you learn the game.',
      category: 'Getting Started'
    },
    {
      id: '2',
      question: 'What are the different player positions?',
      answer: 'Water polo has 7 positions: Goalkeeper, Left Wing, Right Wing, Left Driver, Right Driver, Center Forward, and Point. Each position has specific roles and required skills.',
      category: 'Gameplay'
    },
    {
      id: '3',
      question: 'How does player training work?',
      answer: 'Players can be trained in 4 main skills: Shooting, Swimming, Passing, and Defense. Training improves these stats over time, and younger players develop faster than older ones.',
      category: 'Player Development'
    },
    {
      id: '4',
      question: 'Can I play against other managers?',
      answer: 'Yes! You can compete in leagues, tournaments, and live manager battles. Join associations to team up with other managers and participate in group competitions.',
      category: 'Multiplayer'
    },
    {
      id: '5',
      question: 'How do formations affect gameplay?',
      answer: 'Formations determine player positioning and tactical approach. Offensive formations create more scoring chances but leave you vulnerable to counter-attacks, while defensive formations are safer but limit scoring opportunities.',
      category: 'Strategy'
    },
    {
      id: '6',
      question: 'Is the game free to play?',
      answer: 'Yes, Water Polo Manager is completely free to play. All core features including team management, competitions, and multiplayer are available without payment.',
      category: 'Account'
    }
  ]

  const filteredFAQs = faqs.filter(faq => 
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
    faq.category.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const toggleFAQ = (id: string) => {
    setOpenFAQ(openFAQ === id ? null : id)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary to-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Support Center
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto">
              Get help, find answers, and connect with our community. 
              We're here to help you become the best water polo manager.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
          >
            <motion.div variants={itemVariants}>
              <Card className="text-center hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-8">
                  <Bug className="w-12 h-12 text-red-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-3">Report a Bug</h3>
                  <p className="text-muted mb-6">
                    Found something that's not working? Let us know so we can fix it.
                  </p>
                  <Button variant="outline" className="w-full hover:bg-primary hover:text-white hover:border-primary">
                    <Bug className="w-4 h-4 mr-2" />
                    Report Bug
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card className="text-center hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-8">
                  <Lightbulb className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-3">Feature Request</h3>
                  <p className="text-muted mb-6">
                    Have an idea for a new feature? We'd love to hear your suggestions.
                  </p>
                  <Button variant="outline" className="w-full hover:bg-primary hover:text-white hover:border-primary">
                    <Lightbulb className="w-4 h-4 mr-2" />
                    Suggest Feature
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Card className="text-center hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-8">
                  <Mail className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-3">Contact Support</h3>
                  <p className="text-muted mb-6">
                    Need personal assistance? Our support team is here to help.
                  </p>
                  <Button variant="outline" className="w-full hover:bg-primary hover:text-white hover:border-primary">
                    <Mail className="w-4 h-4 mr-2" />
                    Contact Us
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-gray-700 mb-8">
              Find quick answers to common questions about Water Polo Manager
            </p>
            
            {/* Search */}
            <div className="relative max-w-md mx-auto">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-light w-5 h-5" />
              <input
                type="text"
                placeholder="Search FAQs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-foreground bg-white placeholder-gray-500"
              />
            </div>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="space-y-4"
          >
            {filteredFAQs.map((faq) => (
              <motion.div key={faq.id} variants={itemVariants}>
                <Card>
                  <CardContent className="p-0">
                    <button
                      onClick={() => toggleFAQ(faq.id)}
                      className="w-full p-6 text-left flex items-center justify-between hover:bg-hover-bg transition-colors duration-200"
                    >
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-1">
                          <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded">
                            {faq.category}
                          </span>
                        </div>
                        <h3 className="font-semibold text-foreground">{faq.question}</h3>
                      </div>
                      {openFAQ === faq.id ? (
                        <ChevronUp className="w-5 h-5 text-muted-light" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-muted-light" />
                      )}
                    </button>
                    
                    {openFAQ === faq.id && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                        className="px-6 pb-6"
                      >
                        <p className="text-muted leading-relaxed">{faq.answer}</p>
                      </motion.div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {filteredFAQs.length === 0 && (
            <div className="text-center py-12">
              <HelpCircle className="w-12 h-12 text-muted-light mx-auto mb-4" />
              <p className="text-muted">No FAQs found matching your search.</p>
            </div>
          )}
        </div>
      </section>

      {/* Community Resources */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Community Resources
            </h2>
            <p className="text-gray-700">
              Connect with other managers and get help from the community
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-8 text-center">
                <MessageCircle className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-3">Discord Community</h3>
                <p className="text-muted mb-6">
                  Join our Discord server to chat with other managers, share strategies, and get real-time help.
                </p>
                <Button className="hover:bg-primary-dark">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Join Discord
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-8 text-center">
                <HelpCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-3">Community Forums</h3>
                <p className="text-muted mb-6">
                  Browse our forums for in-depth discussions, guides, and community-generated content.
                </p>
                <Button variant="outline" className="hover:bg-primary hover:text-white hover:border-primary">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Visit Forums
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </MainLayout>
  )
}
