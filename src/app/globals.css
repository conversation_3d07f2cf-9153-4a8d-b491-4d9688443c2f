@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #1f2937; /* Improved contrast from #171717 */

  /* Water Polo Theme Colors - WCAG AA Compliant */
  --primary: #1d4ed8; /* Improved from #0066cc for better contrast */
  --primary-dark: #1e40af; /* Darker for hover states */
  --primary-light: #3b82f6; /* Lighter variant for backgrounds */
  --secondary: #0891b2; /* Improved from #00ccff for better contrast */
  --secondary-dark: #0e7490;
  --accent: #ea580c; /* Improved from #ff6600 for better contrast */
  --accent-light: #f97316;

  /* Status Colors - WCAG AA Compliant */
  --success: #059669; /* Improved from #00cc66 */
  --success-light: #10b981;
  --success-bg: #d1fae5; /* Light background for success states */
  --warning: #d97706; /* Improved from #ffcc00 for better contrast */
  --warning-light: #f59e0b;
  --warning-bg: #fef3c7; /* Light background for warning states */
  --error: #dc2626; /* Improved from #ff3333 */
  --error-light: #ef4444;
  --error-bg: #fee2e2; /* Light background for error states */

  /* Pool/Water Colors - Accessible versions */
  --pool-blue: #1d4ed8; /* Matches primary for consistency */
  --pool-light: #60a5fa; /* Lighter but still accessible */
  --pool-dark: #1e3a8a; /* Darker for better contrast */
  --water-surface: #3b82f6; /* Accessible blue tone */

  /* UI Colors - Enhanced for accessibility */
  --card-bg: #ffffff;
  --card-border: #d1d5db; /* Improved border visibility */
  --border: #d1d5db; /* Improved from #e5e7eb */
  --muted: #4b5563; /* Improved contrast from #6b7280 */
  --muted-bg: #f3f4f6; /* Slightly darker for better definition */
  --muted-light: #9ca3af; /* For less important text */

  /* Focus and interaction states */
  --focus-ring: #3b82f6;
  --focus-ring-offset: #ffffff;
  --hover-bg: #f9fafb;
  --active-bg: #f3f4f6;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-primary-light: var(--primary-light);
  --color-secondary: var(--secondary);
  --color-secondary-dark: var(--secondary-dark);
  --color-accent: var(--accent);
  --color-accent-light: var(--accent-light);
  --color-success: var(--success);
  --color-success-light: var(--success-light);
  --color-success-bg: var(--success-bg);
  --color-warning: var(--warning);
  --color-warning-light: var(--warning-light);
  --color-warning-bg: var(--warning-bg);
  --color-error: var(--error);
  --color-error-light: var(--error-light);
  --color-error-bg: var(--error-bg);
  --color-pool-blue: var(--pool-blue);
  --color-pool-light: var(--pool-light);
  --color-pool-dark: var(--pool-dark);
  --color-water-surface: var(--water-surface);
  --color-card-bg: var(--card-bg);
  --color-card-border: var(--card-border);
  --color-border: var(--border);
  --color-muted: var(--muted);
  --color-muted-bg: var(--muted-bg);
  --color-muted-light: var(--muted-light);
  --color-focus-ring: var(--focus-ring);
  --color-focus-ring-offset: var(--focus-ring-offset);
  --color-hover-bg: var(--hover-bg);
  --color-active-bg: var(--active-bg);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #111827;
    --foreground: #f9fafb; /* Improved contrast */

    /* Dark mode status colors */
    --success: #10b981;
    --success-bg: #064e3b;
    --warning: #f59e0b;
    --warning-bg: #78350f;
    --error: #ef4444;
    --error-bg: #7f1d1d;

    /* Dark mode UI colors */
    --card-bg: #1f2937;
    --card-border: #374151;
    --border: #4b5563; /* Improved visibility */
    --muted: #d1d5db; /* Better contrast in dark mode */
    --muted-bg: #374151;
    --muted-light: #9ca3af;

    /* Dark mode interaction states */
    --hover-bg: #374151;
    --active-bg: #4b5563;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom markdown prose styles */
.markdown-content {
  line-height: 1.75;
  color: #374151;
}

.markdown-content h1 {
  font-size: 2.25rem;
  font-weight: 800;
  color: #111827;
  margin-top: 0;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.markdown-content h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.markdown-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  line-height: 1.4;
}

.markdown-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.markdown-content p {
  margin-bottom: 1.5rem;
  color: #374151;
}

.markdown-content strong {
  font-weight: 600;
  color: #111827;
}

.markdown-content ul, .markdown-content ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.markdown-content li {
  margin-bottom: 0.5rem;
  color: #374151;
}

.markdown-content a {
  color: var(--primary);
  text-decoration: underline;
  text-decoration-color: var(--primary);
  text-underline-offset: 2px;
}

.markdown-content a:hover {
  color: var(--primary-dark);
  text-decoration-color: var(--primary-dark);
}

.markdown-content blockquote {
  border-left: 4px solid var(--primary);
  padding-left: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  color: #4b5563;
}

.markdown-content code {
  background-color: #f3f4f6;
  color: #111827;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.markdown-content pre {
  background-color: #1f2937;
  color: #e5e7eb;
  padding: 1.5rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.markdown-content pre code {
  background-color: transparent;
  color: inherit;
  padding: 0;
}

.markdown-content hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 3rem 0;
}

/* Article enhancements */
.markdown-content {
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.markdown-content p:first-of-type {
  font-size: 1.125rem;
  font-weight: 400;
  color: #4b5563;
  margin-bottom: 2rem;
}

.markdown-content h2:first-of-type {
  margin-top: 2rem;
}

/* Custom animations */
@keyframes wave {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes ripple {
  0% { transform: scale(0); opacity: 1; }
  100% { transform: scale(4); opacity: 0; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-5px) rotate(1deg); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
}

@keyframes slide-in-right {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slide-in-left {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes fade-in-up {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-wave {
  animation: wave 2s ease-in-out infinite;
}

.animate-ripple {
  animation: ripple 0.6s linear;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

/* Glassmorphism effect - Enhanced for accessibility */
.glass {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Enhanced interaction styles */
.interactive-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.interactive-card:active {
  transform: translateY(0);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Smooth focus styles */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  border-color: rgb(59, 130, 246);
}

/* Enhanced button hover effects */
.btn-hover-lift {
  transition: all 0.2s ease;
}

.btn-hover-lift:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive utilities */
@media (max-width: 640px) {
  .mobile-stack {
    flex-direction: column !important;
  }

  .mobile-full {
    width: 100% !important;
  }

  .mobile-text-center {
    text-align: center !important;
  }

  .mobile-hidden {
    display: none !important;
  }

  .mobile-p-4 {
    padding: 1rem !important;
  }

  .mobile-gap-4 {
    gap: 1rem !important;
  }
}

@media (max-width: 768px) {
  .tablet-stack {
    flex-direction: column !important;
  }

  .tablet-hidden {
    display: none !important;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .interactive-card:hover {
    transform: none;
    box-shadow: none;
  }

  .interactive-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .btn-hover-lift:hover {
    transform: none;
    box-shadow: none;
  }

  .btn-hover-lift:active {
    transform: scale(0.95);
  }
}

/* Improved scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted-bg);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--muted);
}

/* Loading skeleton animation */
@keyframes skeleton-loading {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.skeleton {
  background: linear-gradient(90deg, var(--muted-bg) 25%, var(--hover-bg) 50%, var(--muted-bg) 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}
