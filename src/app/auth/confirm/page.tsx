'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { useAuth } from '@/lib/auth-context'
import But<PERSON> from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { CheckCircle, XCircle, Loader2, ArrowRight, Home } from 'lucide-react'

export default function ConfirmPage() {
  const { user } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [error, setError] = useState('')

  useEffect(() => {
    const confirmEmail = async () => {
      try {
        const { createClient } = await import('@/lib/supabase-client')
        const supabase = createClient()

        // Get the token from URL parameters
        const token_hash = searchParams.get('token_hash')
        const type = searchParams.get('type')

        if (!token_hash || type !== 'signup') {
          setStatus('error')
          setError('Invalid confirmation link')
          return
        }

        // Verify the email confirmation
        const { data, error } = await supabase.auth.verifyOtp({
          token_hash,
          type: 'signup'
        })

        if (error) {
          console.error('Email confirmation error:', error)
          setStatus('error')
          setError(error.message || 'Failed to confirm email')
          return
        }

        if (data.user) {
          setStatus('success')
          // Redirect to dashboard after a short delay
          setTimeout(() => {
            router.push('/dashboard')
          }, 3000)
        }
      } catch (err: any) {
        console.error('Confirmation error:', err)
        setStatus('error')
        setError(err.message || 'An unexpected error occurred')
      }
    }

    confirmEmail()
  }, [searchParams, router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-pool-blue via-primary to-pool-dark p-4">
      {/* Background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full animate-wave" />
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-white/5 rounded-full animate-wave" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-white/10 rounded-full animate-wave" style={{ animationDelay: '2s' }} />
      </div>

      <div className="relative z-10 w-full max-w-md">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <Card variant="glass" className="bg-white/20 backdrop-blur-md border-white/30 shadow-2xl">
            <CardContent className="text-center p-8">
              {status === 'loading' && (
                <>
                  <div className="w-20 h-20 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Loader2 className="w-12 h-12 text-blue-400 animate-spin" />
                  </div>
                  <h2 className="text-2xl font-bold text-white mb-4">Confirming Your Email</h2>
                  <p className="text-white/90 mb-6">
                    Please wait while we verify your email address...
                  </p>
                </>
              )}

              {status === 'success' && (
                <>
                  <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <CheckCircle className="w-12 h-12 text-green-400" />
                  </div>
                  <h2 className="text-2xl font-bold text-white mb-4">Email Confirmed!</h2>
                  <p className="text-white/90 mb-6">
                    Welcome to Water Polo Manager! Your account has been successfully activated.
                  </p>
                  <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4 mb-6">
                    <p className="text-green-200 text-sm">
                      🎉 You're all set! Redirecting to your dashboard in a few seconds...
                    </p>
                  </div>
                  <div className="space-y-3">
                    <Button
                      onClick={() => router.push('/dashboard')}
                      className="w-full bg-white hover:bg-gray-100 font-semibold"
                      style={{ color: '#1f2937' }}
                    >
                      <ArrowRight className="w-4 h-4 mr-2" />
                      Go to Dashboard
                    </Button>
                    <Button
                      onClick={() => router.push('/')}
                      variant="outline"
                      className="w-full border-white/30 text-white hover:bg-white/10"
                    >
                      <Home className="w-4 h-4 mr-2" />
                      Back to Home
                    </Button>
                  </div>
                </>
              )}

              {status === 'error' && (
                <>
                  <div className="w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <XCircle className="w-12 h-12 text-red-400" />
                  </div>
                  <h2 className="text-2xl font-bold text-white mb-4">Confirmation Failed</h2>
                  <p className="text-white/90 mb-4">
                    We couldn't confirm your email address.
                  </p>
                  <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
                    <p className="text-red-200 text-sm">{error}</p>
                  </div>
                  <div className="space-y-3">
                    <Button
                      onClick={() => router.push('/login')}
                      className="w-full bg-white hover:bg-gray-100 font-semibold"
                      style={{ color: '#1f2937' }}
                    >
                      <ArrowRight className="w-4 h-4 mr-2" />
                      Try Signing Up Again
                    </Button>
                    <Button
                      onClick={() => router.push('/')}
                      variant="outline"
                      className="w-full border-white/30 text-white hover:bg-white/10"
                    >
                      <Home className="w-4 h-4 mr-2" />
                      Back to Home
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
