import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase-server'
import GameLayout from '@/components/layout/game-layout'
import EnhancedLeaguePage from '@/components/league/enhanced-league-page'
import MatchSchedule from '@/components/league/match-schedule'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import LeagueTableComponent from '@/components/game/league-table'
import LeagueAnalyticsDashboard from '@/components/league/league-analytics-dashboard'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Trophy, Calendar, Users, BarChart } from 'lucide-react'

export default async function LeaguePage() {
  const supabase = await createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login?redirectTo=/league')
  }

  // Fetch user profile and team data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  // Fetch teams
  const { data: teams } = await supabase
    .from('teams')
    .select('*')
    .eq('user_id', user.id)

  // Add first team to profile
  const profileWithTeam = profile ? {
    ...profile,
    team: teams && teams.length > 0 ? teams[0] : null
  } : null

  // If team doesn't have a league, try to assign one
  if (profileWithTeam?.team && !profileWithTeam.team.league_id) {
    // Import and use the default team service to assign league
    const { defaultTeamService } = await import('@/lib/default-team-service')
    await defaultTeamService.assignToBeginnerLeague(profileWithTeam.team.id)

    // Refetch team data
    const { data: updatedTeam } = await supabase
      .from('teams')
      .select('*')
      .eq('id', profileWithTeam.team.id)
      .single()

    if (updatedTeam) {
      profileWithTeam.team = updatedTeam
    }
  }

  // Fetch league data (simplified query for current database structure)
  const { data: league } = profileWithTeam?.team?.league_id ? await supabase
    .from('leagues')
    .select('*')
    .eq('id', profileWithTeam.team.league_id)
    .single() : { data: null }

  return (
    <GameLayout>
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid grid-cols-4 w-full max-w-2xl mx-auto mb-6">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <Trophy className="w-4 h-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="schedule" className="flex items-center space-x-2">
            <Calendar className="w-4 h-4" />
            <span>Schedule</span>
          </TabsTrigger>
          <TabsTrigger value="standings" className="flex items-center space-x-2">
            <Users className="w-4 h-4" />
            <span>Standings</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart className="w-4 h-4" />
            <span>Analytics</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {profileWithTeam ? (
            <EnhancedLeaguePage user={user} profile={profileWithTeam} />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-muted-foreground">No team found. Please create a team first.</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="schedule" className="space-y-6">
          {profileWithTeam?.team && league ? (
            <MatchSchedule
              leagueId={profileWithTeam.team.league_id}
              teamId={profileWithTeam.team.id}
              seasonId={league.id || 'current-season'}
            />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-muted-foreground">
                    {!profileWithTeam?.team ? 'No team found.' : 'League assignment in progress. Please refresh the page.'}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="standings" className="space-y-6">
          {league ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Trophy className="w-5 h-5" />
                  <span>League Standings</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <LeagueTableComponent
                  league={league}
                  userTeamId={profileWithTeam?.team?.id}
                  showPromotionRelegation={true}
                />
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-muted-foreground">
                    {!profileWithTeam?.team ? 'No team found.' : 'League assignment in progress. Please refresh the page.'}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {profileWithTeam?.team && league ? (
            <LeagueAnalyticsDashboard
              teamId={profileWithTeam.team.id}
              leagueId={league.id}
            />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center">
                  <p className="text-muted-foreground">
                    {!profileWithTeam?.team ? 'No team found.' : 'League assignment in progress. Please refresh the page.'}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </GameLayout>
  )
}
