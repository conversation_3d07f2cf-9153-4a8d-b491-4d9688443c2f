import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase-server'
import GameLayout from '@/components/layout/game-layout'
import DashboardContent from '@/components/dashboard/dashboard-content'

export default async function DashboardPage() {
  const supabase = await createClient()
  
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // Fetch user profile and team data
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  // Fetch teams separately
  const { data: teams } = await supabase
    .from('teams')
    .select(`
      *,
      players:players(*)
    `)
    .eq('user_id', user.id)

  // Add first team to profile
  const profileWithTeam = profile ? {
    ...profile,
    team: teams && teams.length > 0 ? teams[0] : null
  } : null

  return (
    <GameLayout>
      <DashboardContent user={user} profile={profileWithTeam} />
    </GameLayout>
  )
}
