'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { useAuth } from '@/lib/auth-context'
import But<PERSON> from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Mail, Lock, User, Shield, Users, Trophy, CheckCircle, ArrowRight, RefreshCw } from 'lucide-react'

export default function LoginPage() {
  const { user, signInWithEmail, signUpWithEmail, loading } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirectTo = searchParams.get('redirectTo') || '/dashboard'

  const [isSignUp, setIsSignUp] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [error, setError] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [signUpSuccess, setSignUpSuccess] = useState(false)
  const [userEmail, setUserEmail] = useState('')

  useEffect(() => {
    if (user && !loading) {
      router.push(redirectTo)
    }
  }, [user, loading, router, redirectTo])

  const handleResendConfirmation = async () => {
    try {
      setIsSubmitting(true)
      const { createClient } = await import('@/lib/supabase-client')
      const supabase = createClient()

      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: userEmail
      })

      if (error) throw error

      // Show success message briefly
      setError('')
      const successMsg = 'Confirmation email sent successfully!'
      setError(successMsg)
      setTimeout(() => setError(''), 3000)
    } catch (error: any) {
      setError(error.message || 'Failed to resend confirmation email')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsSubmitting(true)

    try {
      if (isSignUp) {
        await signUpWithEmail(email, password, fullName)
        setUserEmail(email)
        setSignUpSuccess(true)
        // Clear form
        setEmail('')
        setPassword('')
        setFullName('')
      } else {
        await signInWithEmail(email, password)
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-pool-blue to-pool-dark">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white"></div>
      </div>
    )
  }

  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-pool-blue to-pool-dark">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
          <p>Redirecting to dashboard...</p>
        </div>
      </div>
    )
  }

  // Success state for sign-up
  if (signUpSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-pool-blue via-primary to-pool-dark p-4">
        {/* Background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full animate-wave" />
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-white/5 rounded-full animate-wave" style={{ animationDelay: '1s' }} />
          <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-white/10 rounded-full animate-wave" style={{ animationDelay: '2s' }} />
        </div>

        <div className="relative z-10 w-full max-w-md">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Card variant="glass" className="bg-white/20 backdrop-blur-md border-white/30 shadow-2xl">
              <CardContent className="text-center p-8">
                <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <CheckCircle className="w-12 h-12 text-green-400" />
                </div>

                <h2 className="text-2xl font-bold text-white mb-4">Check Your Email!</h2>

                <p className="text-white/90 mb-6 leading-relaxed">
                  We've sent a confirmation email to <strong className="text-white">{userEmail}</strong>.
                  Click the link in the email to activate your account and start managing your water polo team.
                </p>

                <div className="bg-blue-500/20 border border-blue-500/30 rounded-lg p-4 mb-6">
                  <div className="flex items-start space-x-3">
                    <Mail className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                    <div className="text-left">
                      <p className="text-blue-200 text-sm font-medium mb-1">Email Tips:</p>
                      <ul className="text-blue-200/80 text-xs space-y-1">
                        <li>• Check your spam/junk folder</li>
                        <li>• The email may take a few minutes to arrive</li>
                        <li>• Make sure {userEmail} is correct</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {error && (
                  <div className={`border rounded-lg p-3 mb-4 ${
                    error.includes('successfully')
                      ? 'bg-green-500/20 border-green-500/50'
                      : 'bg-red-500/20 border-red-500/50'
                  }`}>
                    <p className={`text-sm ${
                      error.includes('successfully') ? 'text-green-200' : 'text-red-200'
                    }`}>{error}</p>
                  </div>
                )}

                <div className="space-y-3">
                  <Button
                    onClick={handleResendConfirmation}
                    loading={isSubmitting}
                    variant="outline"
                    className="w-full border-white/30 text-white hover:bg-white/10"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Resend Confirmation Email
                  </Button>

                  <Button
                    onClick={() => {
                      setSignUpSuccess(false)
                      setUserEmail('')
                      setError('')
                    }}
                    variant="ghost"
                    className="w-full bg-white hover:bg-gray-100 font-semibold"
                    style={{ color: '#1f2937' }}
                  >
                    <ArrowRight className="w-4 h-4 mr-2" />
                    Back to Sign In
                  </Button>
                </div>

                <div className="mt-6 pt-6 border-t border-white/20">
                  <p className="text-white/70 text-sm">
                    Already confirmed your email?{' '}
                    <button
                      onClick={() => {
                        setSignUpSuccess(false)
                        setIsSignUp(false)
                        setUserEmail('')
                        setError('')
                      }}
                      className="text-white hover:underline font-medium"
                    >
                      Sign in here
                    </button>
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-pool-blue via-primary to-pool-dark p-4">
      {/* Background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full animate-wave" />
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-white/5 rounded-full animate-wave" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-white/10 rounded-full animate-wave" style={{ animationDelay: '2s' }} />
      </div>

      <div className="relative z-10 w-full max-w-md">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Logo and title */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4">
              <div className="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">WP</span>
              </div>
            </div>
            <h1 className="text-3xl font-bold text-white mb-2 drop-shadow-lg">Welcome Back</h1>
            <p className="text-white drop-shadow-md">Sign in to continue building your water polo dynasty</p>
          </div>

          <Card variant="glass" className="bg-white/20 backdrop-blur-md border-white/30 shadow-2xl">
            <CardHeader className="text-center">
              <CardTitle className="text-white drop-shadow-md">
                {isSignUp ? 'Create Your Account' : 'Sign In to Your Account'}
              </CardTitle>
              <CardDescription className="text-white drop-shadow-sm">
                {isSignUp ? 'Join thousands of managers worldwide' : 'Welcome back, manager!'}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              <form onSubmit={handleSubmit} className="space-y-4">
                {isSignUp && (
                  <div>
                    <label htmlFor="fullName" className="block text-sm font-medium text-white mb-2">
                      Full Name
                    </label>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/80 w-5 h-5" />
                      <input
                        id="fullName"
                        type="text"
                        value={fullName}
                        onChange={(e) => setFullName(e.target.value)}
                        className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/70 focus:ring-2 focus:ring-secondary focus:border-transparent"
                        placeholder="Enter your full name"
                        required={isSignUp}
                      />
                    </div>
                  </div>
                )}

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-white mb-2">
                    Email Address
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/80 w-5 h-5" />
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/70 focus:ring-2 focus:ring-secondary focus:border-transparent"
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-white mb-2">
                    Password
                    {isSignUp && (
                      <span className="text-white/70 text-xs ml-2">(minimum 6 characters)</span>
                    )}
                  </label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/80 w-5 h-5" />
                    <input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/70 focus:ring-2 focus:ring-secondary focus:border-transparent"
                      placeholder={isSignUp ? "Create a secure password" : "Enter your password"}
                      required
                      minLength={6}
                    />
                  </div>
                  {isSignUp && password.length > 0 && password.length < 6 && (
                    <p className="text-yellow-200 text-xs mt-1">Password must be at least 6 characters long</p>
                  )}
                </div>

                {error && (
                  <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3">
                    <p className="text-red-200 text-sm">{error}</p>
                  </div>
                )}

                <Button
                  type="submit"
                  loading={isSubmitting}
                  variant="ghost"
                  className="w-full bg-white hover:bg-gray-100 font-semibold py-3 px-4 rounded-lg transition-colors duration-200"
                  style={{ color: '#1f2937' }}
                  size="lg"
                >
                  {isSignUp ? 'Create Account' : 'Sign In'}
                </Button>
              </form>

              <div className="text-center">
                <button
                  onClick={() => {
                    setIsSignUp(!isSignUp)
                    setError('')
                    setEmail('')
                    setPassword('')
                    setFullName('')
                    setSignUpSuccess(false)
                    setUserEmail('')
                  }}
                  className="text-white/90 hover:text-white text-sm underline"
                >
                  {isSignUp ? 'Already have an account? Sign in' : 'Need an account? Sign up'}
                </button>
              </div>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-white/20" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-transparent text-white/90">Why join?</span>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4">
                <div className="flex items-center space-x-3 text-white/90">
                  <Shield className="w-5 h-5 text-secondary" />
                  <span className="text-sm">Secure cloud save</span>
                </div>
                <div className="flex items-center space-x-3 text-white/90">
                  <Users className="w-5 h-5 text-secondary" />
                  <span className="text-sm">Join global competitions</span>
                </div>
                <div className="flex items-center space-x-3 text-white/90">
                  <Trophy className="w-5 h-5 text-secondary" />
                  <span className="text-sm">Track achievements</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="mt-6 text-center">
            <p className="text-white/80 text-sm">
              By {isSignUp ? 'creating an account' : 'signing in'}, you agree to our{' '}
              <a href="/terms" className="text-white hover:underline">Terms of Service</a>
              {' '}and{' '}
              <a href="/privacy" className="text-white hover:underline">Privacy Policy</a>
            </p>
          </div>

          <div className="mt-8 text-center">
            <p className="text-white/80 text-sm mb-4">
              Don't want to sign in right now?
            </p>
            <Button
              variant="outline"
              onClick={() => router.push('/')}
              className="border-white/30 text-white hover:bg-white/10"
            >
              Continue Browsing
            </Button>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
