'use client'

import { useState } from 'react'
import { createClient } from '@/lib/supabase-client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'

export default function DebugPage() {
  const [results, setResults] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const supabase = createClient()

  const testDatabaseConnection = async () => {
    setLoading(true)
    setResults([])
    
    const tests = [
      {
        name: 'Test Auth User',
        test: async () => {
          const { data, error } = await supabase.auth.getUser()
          return { data: data.user ? { id: data.user.id, email: data.user.email } : null, error }
        }
      },
      {
        name: 'Test Profiles Table Structure',
        test: async () => {
          // Test if table exists by trying to select with no auth
          const { data, error } = await supabase.from('profiles').select('id').limit(0)
          return { data, error }
        }
      },
      {
        name: 'Test Current User Profile',
        test: async () => {
          const { data: user } = await supabase.auth.getUser()
          if (!user.user) return { data: null, error: { message: 'Not authenticated' } }

          const { data, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.user.id)
            .single()
          return { data, error }
        }
      },
      {
        name: 'Test Teams Table Structure',
        test: async () => {
          const { data, error } = await supabase.from('teams').select('id').limit(0)
          return { data, error }
        }
      },
      {
        name: 'Test Current User Teams',
        test: async () => {
          const { data: user } = await supabase.auth.getUser()
          if (!user.user) return { data: null, error: { message: 'Not authenticated' } }

          const { data, error } = await supabase
            .from('teams')
            .select('*')
            .eq('user_id', user.user.id)
          return { data, error }
        }
      },
      {
        name: 'Test Players Table',
        test: async () => {
          const { data, error } = await supabase.from('players').select('*').limit(1)
          return { data, error }
        }
      },
      {
        name: 'Test Facilities Table',
        test: async () => {
          const { data, error } = await supabase.from('facilities').select('*').limit(1)
          return { data, error }
        }
      },
      {
        name: 'Test Formations Table',
        test: async () => {
          const { data, error } = await supabase.from('formations').select('*').limit(1)
          return { data, error }
        }
      },
      {
        name: 'Test User Currencies Table',
        test: async () => {
          const { data, error } = await supabase.from('user_currencies').select('*').limit(1)
          return { data, error }
        }
      },
      {
        name: 'Test Leagues Table',
        test: async () => {
          const { data, error } = await supabase.from('leagues').select('*').limit(1)
          return { data, error }
        }
      }
    ]

    const testResults = []
    
    for (const test of tests) {
      try {
        const result = await test.test()
        testResults.push({
          name: test.name,
          success: !result.error,
          data: result.data,
          error: result.error
        })
      } catch (error) {
        testResults.push({
          name: test.name,
          success: false,
          data: null,
          error
        })
      }
    }
    
    setResults(testResults)
    setLoading(false)
  }

  return (
    <div className="min-h-screen bg-muted-bg p-8">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Database Connection Debug</CardTitle>
          </CardHeader>
          <CardContent>
            <Button onClick={testDatabaseConnection} disabled={loading}>
              {loading ? 'Testing...' : 'Test Database Connection'}
            </Button>
            
            {results.length > 0 && (
              <div className="mt-6 space-y-4">
                {results.map((result, index) => (
                  <div key={index} className="border rounded p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{result.name}</h3>
                      <span className={`px-2 py-1 rounded text-sm ${
                        result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {result.success ? 'SUCCESS' : 'FAILED'}
                      </span>
                    </div>
                    
                    {result.error && (
                      <div className="mb-2">
                        <strong>Error:</strong>
                        <pre className="bg-red-50 p-2 rounded text-sm mt-1 overflow-auto">
                          {JSON.stringify(result.error, null, 2)}
                        </pre>
                      </div>
                    )}
                    
                    {result.data && (
                      <div>
                        <strong>Data:</strong>
                        <pre className="bg-gray-50 p-2 rounded text-sm mt-1 overflow-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
