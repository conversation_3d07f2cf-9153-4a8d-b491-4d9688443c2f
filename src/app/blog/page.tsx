'use client'

import { motion } from 'framer-motion'
import MainLayout from '@/components/layout/main-layout'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { Calendar, User, ArrowRight, Trophy, Target, Users } from 'lucide-react'
import Link from 'next/link'
import { formatDate } from '@/lib/utils'

interface BlogPost {
  id: string
  title: string
  excerpt: string
  content: string
  author: string
  publishedAt: string
  category: string
  readTime: number
  featured: boolean
}

export default function BlogPage() {
  // Mock blog posts
  const blogPosts: BlogPost[] = [
    {
      id: 'mastering-water-polo-tactics',
      title: 'Mastering Water Polo Tactics: A Manager\'s Guide',
      excerpt: 'Learn the essential tactical concepts that separate good managers from great ones in Water Polo Manager.',
      content: '',
      author: 'Website Admin',
      publishedAt: '2025-01-15',
      category: 'Strategy',
      readTime: 8,
      featured: true
    },
    {
      id: 'player-development-guide',
      title: 'The Complete Player Development Guide',
      excerpt: 'Discover how to maximize your players\' potential through effective training and development strategies.',
      content: '',
      author: 'Website Admin',
      publishedAt: '2025-01-12',
      category: 'Development',
      readTime: 12,
      featured: true
    },
    {
      id: 'formation-analysis-7v7',
      title: 'Formation Analysis: Finding the Perfect 7v7 Setup',
      excerpt: 'Deep dive into the most effective formations and when to use them in competitive matches.',
      content: '',
      author: 'Website Admin',
      publishedAt: '2025-01-08',
      category: 'Tactics',
      readTime: 6,
      featured: false
    },
    {
      id: 'water-polo-fundamentals',
      title: 'Water Polo Fundamentals: Essential Skills Every Player Needs',
      excerpt: 'Master the core techniques and skills that form the foundation of successful water polo gameplay.',
      content: '',
      author: 'Website Admin',
      publishedAt: '2025-01-05',
      category: 'Training',
      readTime: 7,
      featured: false
    },
    {
      id: 'transfer-market-tips',
      title: 'Transfer Market Mastery: Finding Hidden Gems',
      excerpt: 'Learn how to identify undervalued players and build a championship team on any budget.',
      content: '',
      author: 'Website Admin',
      publishedAt: '2025-01-02',
      category: 'Strategy',
      readTime: 10,
      featured: false
    }
  ]

  const featuredPosts = blogPosts.filter(post => post.featured)
  const regularPosts = blogPosts.filter(post => !post.featured)

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Strategy':
      case 'Tactics':
        return Target
      case 'Development':
        return Trophy
      case 'Community':
        return Users
      default:
        return User
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Strategy':
      case 'Tactics':
        return 'bg-blue-100 text-blue-800'
      case 'Development':
        return 'bg-green-100 text-green-800'
      case 'Community':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <MainLayout>
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary to-secondary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="space-y-6"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-white">
              Water Polo Manager Blog
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto">
              Strategy guides, player development tips, and community highlights 
              to help you become the ultimate water polo manager.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Featured Posts */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Featured Articles</h2>
            <p className="text-gray-700">Our most popular and impactful content</p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16"
          >
            {featuredPosts.map((post) => {
              const CategoryIcon = getCategoryIcon(post.category)
              
              return (
                <motion.div key={post.id} variants={itemVariants}>
                  <Card className="h-full hover:shadow-lg transition-shadow duration-300 group">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-3">
                        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(post.category)}`}>
                          <CategoryIcon className="w-4 h-4 mr-1" />
                          {post.category}
                        </span>
                        <span className="text-sm text-muted-light">{post.readTime} min read</span>
                      </div>
                      <CardTitle className="text-xl group-hover:text-primary transition-colors duration-200">
                        {post.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted mb-6 leading-relaxed">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-sm text-muted-light">
                          <div className="flex items-center">
                            <User className="w-4 h-4 mr-1" />
                            {post.author}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            {formatDate(post.publishedAt)}
                          </div>
                        </div>
                        <Link href={`/blog/${post.id}`}>
                          <Button variant="outline" size="sm" className="hover:bg-primary hover:text-white hover:border-primary">
                            Read More
                            <ArrowRight className="w-4 h-4 ml-1" />
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </motion.div>
        </div>
      </section>

      {/* Regular Posts */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-12"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Latest Articles</h2>
            <p className="text-gray-700">Stay updated with the latest strategies and tips</p>
          </motion.div>

          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {regularPosts.map((post) => {
              const CategoryIcon = getCategoryIcon(post.category)
              
              return (
                <motion.div key={post.id} variants={itemVariants}>
                  <Card className="h-full hover:shadow-lg transition-shadow duration-300 group">
                    <CardHeader>
                      <div className="flex items-center justify-between mb-3">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(post.category)}`}>
                          <CategoryIcon className="w-3 h-3 mr-1" />
                          {post.category}
                        </span>
                        <span className="text-xs text-muted-light">{post.readTime} min</span>
                      </div>
                      <CardTitle className="text-lg group-hover:text-primary transition-colors duration-200">
                        {post.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted mb-4 text-sm leading-relaxed">
                        {post.excerpt}
                      </p>
                      <div className="flex items-center justify-between text-xs text-muted-light">
                        <span>{post.author}</span>
                        <span>{formatDate(post.publishedAt)}</span>
                      </div>
                      <Link href={`/blog/${post.id}`}>
                        <Button variant="outline" size="sm" className="w-full mt-4 hover:bg-primary hover:text-white hover:border-primary">
                          Read Article
                          <ArrowRight className="w-4 h-4 ml-1" />
                        </Button>
                      </Link>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </motion.div>
        </div>
      </section>
    </MainLayout>
  )
}
