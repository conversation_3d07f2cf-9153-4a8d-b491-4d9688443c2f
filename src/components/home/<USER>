'use client'

import { motion } from 'framer-motion'
import { 
  Users, 
  Trophy, 
  Target, 
  TrendingUp, 
  Gamepad2, 
  Globe,
  Brain,
  Award
} from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'

export default function FeaturesSection() {
  const features = [
    {
      icon: Users,
      title: 'Team Management',
      description: 'Build and manage your squad of 14 players with unique skills and positions.',
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: TrendingUp,
      title: 'Player Development',
      description: 'Train your players, improve their stats, and unlock special abilities.',
      color: 'from-green-500 to-green-600'
    },
    {
      icon: Target,
      title: 'Tactical Engine',
      description: 'Create custom formations and strategies to outsmart your opponents.',
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: Trophy,
      title: 'Competitions',
      description: 'Compete in leagues, cups, and live manager-vs-manager associations.',
      color: 'from-yellow-500 to-yellow-600'
    },
    {
      icon: Globe,
      title: 'Global Leagues',
      description: 'Join players from around the world in competitive water polo action.',
      color: 'from-indigo-500 to-indigo-600'
    },
    {
      icon: Brain,
      title: 'Smart AI',
      description: 'Advanced match simulation based on tactics, player stats, and strategy.',
      color: 'from-red-500 to-red-600'
    },
    {
      icon: Gamepad2,
      title: 'Live Matches',
      description: 'Watch your team play in real-time with dynamic match commentary.',
      color: 'from-teal-500 to-teal-600'
    },
    {
      icon: Award,
      title: 'Achievements',
      description: 'Unlock badges, trophies, and special rewards as you progress.',
      color: 'from-orange-500 to-orange-600'
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <section className="py-20 bg-muted-bg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Everything You Need to
            <span className="block text-primary">Dominate the Pool</span>
          </h2>
          <p className="text-xl text-muted max-w-3xl mx-auto">
            From team building to tactical mastery, Water Polo Manager gives you all the tools
            to create a championship-winning dynasty.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        >
          {features.map((feature, index) => (
            <motion.div key={feature.title} variants={itemVariants}>
              <Card className="h-full hover:shadow-lg transition-shadow duration-300 group cursor-pointer">
                <CardContent className="p-6 text-center space-y-4">
                  <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors duration-300">
                    {feature.title}
                  </h3>
                  <p className="text-muted leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional feature highlight */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <Card variant="glass" className="bg-gradient-to-r from-primary to-secondary text-white">
            <CardContent className="p-8 md:p-12 text-center">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                Real Water Polo Strategy
              </h3>
              <p className="text-lg md:text-xl text-white/90 mb-6 max-w-3xl mx-auto">
                Experience authentic water polo gameplay with realistic player positions, 
                tactical formations, and strategic depth that mirrors the real sport.
              </p>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-8">
                <div className="text-center">
                  <div className="text-3xl font-bold">7v7</div>
                  <div className="text-white/80">Player Format</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">5</div>
                  <div className="text-white/80">Skill Categories</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">∞</div>
                  <div className="text-white/80">Formations</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold">24/7</div>
                  <div className="text-white/80">Competition</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  )
}
