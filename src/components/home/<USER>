'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { Play, Trophy, Users, Target } from 'lucide-react'
import Button from '@/components/ui/button'
import { useAuth } from '@/lib/auth-context'

export default function HeroSection() {
  const { user } = useAuth()
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-pool-blue via-primary to-pool-dark">
      {/* Animated background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-white/10 rounded-full animate-wave" />
        <div className="absolute top-40 right-20 w-24 h-24 bg-white/5 rounded-full animate-wave" style={{ animationDelay: '1s' }} />
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-white/5 rounded-full animate-wave" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-20 right-1/3 w-28 h-28 bg-white/10 rounded-full animate-wave" style={{ animationDelay: '0.5s' }} />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8"
        >
          {/* Main heading */}
          <div className="space-y-4">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight"
            >
              Build Your
              <span className="block bg-gradient-to-r from-secondary to-white bg-clip-text text-transparent">
                Water Polo Dynasty
              </span>
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed"
            >
              Manage your team, develop world-class players, and compete in leagues worldwide. 
              The ultimate water polo management experience awaits.
            </motion.p>
          </div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Link href={user ? "/dashboard" : "/login"}>
              <Button
                variant="ghost"
                size="lg"
                className="bg-white hover:bg-gray-100 active:bg-gray-200 px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 border-2 border-white/20"
                style={{ color: 'var(--primary)' }}
              >
                <Play className="w-5 h-5 mr-2" />
                {user ? "Play" : "Get Started Free"}
              </Button>
            </Link>

            <Link href="/features">
              <Button variant="outline" size="lg" className="border-2 border-white text-white hover:bg-white/20 hover:border-white px-8 py-4 text-lg font-semibold backdrop-blur-sm">
                Learn More
              </Button>
            </Link>
          </motion.div>

          {/* Key features preview */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 max-w-4xl mx-auto"
          >
            <div className="text-center space-y-3">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto">
                <Trophy className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white">Compete Globally</h3>
              <p className="text-white/80 text-sm">
                Join leagues and tournaments from around the world
              </p>
            </div>
            
            <div className="text-center space-y-3">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white">Build Your Squad</h3>
              <p className="text-white/80 text-sm">
                Scout, train, and develop the perfect water polo team
              </p>
            </div>
            
            <div className="text-center space-y-3">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto">
                <Target className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white">Master Strategy</h3>
              <p className="text-white/80 text-sm">
                Create winning tactics and formations for every match
              </p>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce" />
        </div>
      </motion.div>
    </section>
  )
}
