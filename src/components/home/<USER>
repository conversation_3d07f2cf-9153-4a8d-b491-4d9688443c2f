'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { Play, ArrowRight, Trophy, Users } from 'lucide-react'
import Button from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useAuth } from '@/lib/auth-context'

export default function CTASection() {
  const { user } = useAuth()
  return (
    <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-primary/10 rounded-full blur-xl" />
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-secondary/10 rounded-full blur-xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-pool-blue/5 rounded-full blur-3xl" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
            Ready to Build Your
            <span className="block bg-gradient-to-r from-secondary to-primary bg-clip-text text-transparent">
              Championship Team?
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Start your journey to water polo greatness and build the ultimate team.
            Your dynasty awaits.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link href={user ? "/dashboard" : "/login"}>
              <Button
                size="lg"
                className="bg-primary hover:bg-primary-dark px-8 py-4 text-lg font-semibold"
                style={{ color: 'white' }}
              >
                <Play className="w-5 h-5 mr-2" />
                {user ? "Play" : "Get Started Free"}
              </Button>
            </Link>
            
            <Link href="/features">
              <Button variant="outline" size="lg" className="border-2 border-white/40 text-white hover:bg-white/10 hover:border-white/60 px-8 py-4 text-lg font-semibold backdrop-blur-sm">
                Explore Features
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </div>
        </motion.div>

        {/* Feature cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card variant="glass" className="bg-white/5 border-white/10 text-white">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                    <Trophy className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">Competitive Play</h3>
                    <p className="text-gray-400">Climb the global rankings</p>
                  </div>
                </div>
                <p className="text-gray-300">
                  Compete in daily tournaments, seasonal leagues, and special events. 
                  Prove you're the best manager in the world.
                </p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <Card variant="glass" className="bg-white/5 border-white/10 text-white">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="w-12 h-12 bg-secondary rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">Strategic Depth</h3>
                    <p className="text-gray-400">Master tactical gameplay</p>
                  </div>
                </div>
                <p className="text-gray-300">
                  Develop winning strategies, create custom formations, and adapt your
                  tactics to dominate every match.
                </p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Final CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="inline-flex items-center space-x-2 bg-green-500/20 text-green-400 px-4 py-2 rounded-full">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span className="font-medium">Free to play • No credit card required</span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
