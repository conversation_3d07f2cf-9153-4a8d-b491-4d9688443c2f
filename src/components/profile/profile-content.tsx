'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { User, Mail, Calendar, Trophy, Target, Edit3, Save, X } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { useAuth } from '@/lib/auth-context'

interface ProfileContentProps {
  user: any
  profile: any
}

export default function ProfileContent({ user, profile }: ProfileContentProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedProfile, setEditedProfile] = useState({
    full_name: profile?.full_name || '',
    bio: profile?.bio || '',
  })
  const { refreshProfile } = useAuth()

  const handleSave = async () => {
    // TODO: Implement profile update logic
    console.log('Saving profile:', editedProfile)
    setIsEditing(false)
    await refreshProfile()
  }

  const handleCancel = () => {
    setEditedProfile({
      full_name: profile?.full_name || '',
      bio: profile?.bio || '',
    })
    setIsEditing(false)
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-foreground">Profile Settings</h1>
          {!isEditing ? (
            <Button
              onClick={() => setIsEditing(true)}
              className="flex items-center space-x-2"
            >
              <Edit3 className="w-4 h-4" />
              <span>Edit Profile</span>
            </Button>
          ) : (
            <div className="flex space-x-2">
              <Button
                onClick={handleSave}
                className="flex items-center space-x-2"
              >
                <Save className="w-4 h-4" />
                <span>Save</span>
              </Button>
              <Button
                variant="outline"
                onClick={handleCancel}
                className="flex items-center space-x-2"
              >
                <X className="w-4 h-4" />
                <span>Cancel</span>
              </Button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Profile Info */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold mb-4 flex items-center">
                  <User className="w-5 h-5 mr-2" />
                  Personal Information
                </h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-muted mb-1">
                      Full Name
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={editedProfile.full_name}
                        onChange={(e) => setEditedProfile({ ...editedProfile, full_name: e.target.value })}
                        className="w-full px-3 py-2 border border-card-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-foreground bg-white"
                      />
                    ) : (
                      <p className="text-foreground">{profile?.full_name || 'Not set'}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-muted mb-1">
                      Email
                    </label>
                    <p className="text-foreground flex items-center">
                      <Mail className="w-4 h-4 mr-2" />
                      {user?.email}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-muted mb-1">
                      Bio
                    </label>
                    {isEditing ? (
                      <textarea
                        value={editedProfile.bio}
                        onChange={(e) => setEditedProfile({ ...editedProfile, bio: e.target.value })}
                        rows={3}
                        className="w-full px-3 py-2 border border-card-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-foreground bg-white"
                        placeholder="Tell us about yourself..."
                      />
                    ) : (
                      <p className="text-foreground">{profile?.bio || 'No bio set'}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-muted mb-1">
                      Member Since
                    </label>
                    <p className="text-foreground flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      {new Date(user?.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Stats & Achievements */}
          <div className="space-y-6">
            <Card>
              <CardContent className="p-6">
                <h2 className="text-xl font-semibold mb-4 flex items-center">
                  <Trophy className="w-5 h-5 mr-2" />
                  Manager Stats
                </h2>
                
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-muted">Level</span>
                    <span className="font-semibold text-foreground">{profile?.manager_level || 1}</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-muted">Experience</span>
                    <span className="font-semibold text-foreground">{profile?.experience || 0} XP</span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-muted">Teams Managed</span>
                    <span className="font-semibold text-foreground">1</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {profile?.team && (
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-4 flex items-center">
                    <Target className="w-5 h-5 mr-2" />
                    Current Team
                  </h2>
                  
                  <div className="space-y-2">
                    <p className="font-semibold text-foreground">{profile.team.name}</p>
                    <p className="text-muted">Rating: {profile.team.team_rating || 0}</p>
                    <p className="text-muted">Cash: ${(profile.userCurrency?.coins || 0).toLocaleString()}</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  )
}
