'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { 
  Users, 
  Trophy, 
  Target, 
  Zap, 
  DollarSign, 
  Plus, 
  Search,
  AlertCircle,
  FileX,
  Database
} from 'lucide-react'

interface EmptyStateProps {
  icon?: React.ReactNode
  title: string
  description: string
  action?: {
    label: string
    onClick: () => void
    variant?: 'primary' | 'secondary' | 'outline'
  }
  className?: string
}

export function EmptyState({ icon, title, description, action, className }: EmptyStateProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn('text-center py-12', className)}
    >
      {icon && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
          className="flex justify-center mb-6"
        >
          <div className="p-4 rounded-full bg-muted-bg text-muted">
            {icon}
          </div>
        </motion.div>
      )}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="space-y-3"
      >
        <h3 className="text-lg font-semibold text-foreground">{title}</h3>
        <p className="text-muted max-w-md mx-auto leading-relaxed">{description}</p>
        {action && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="pt-4"
          >
            <Button
              variant={action.variant || 'primary'}
              onClick={action.onClick}
            >
              {action.label}
            </Button>
          </motion.div>
        )}
      </motion.div>
    </motion.div>
  )
}

interface EmptyCardProps extends EmptyStateProps {
  variant?: 'default' | 'glass' | 'elevated'
}

export function EmptyCard({ variant = 'default', ...props }: EmptyCardProps) {
  return (
    <Card variant={variant}>
      <CardContent>
        <EmptyState {...props} />
      </CardContent>
    </Card>
  )
}

// Predefined empty states for common scenarios
export function NoPlayersEmpty({ onCreateTeam }: { onCreateTeam: () => void }) {
  return (
    <EmptyState
      icon={<Users className="w-12 h-12" />}
      title="No Players Yet"
      description="Start with our default roster of 10 talented players to begin your water polo journey!"
      action={{
        label: "Create Default Team",
        onClick: onCreateTeam
      }}
    />
  )
}

export function NoTransfersEmpty({ onRefresh }: { onRefresh: () => void }) {
  return (
    <EmptyState
      icon={<Search className="w-12 h-12" />}
      title="No Players Available"
      description="The transfer market is currently empty. Check back later or refresh to see new players."
      action={{
        label: "Refresh Market",
        onClick: onRefresh,
        variant: "outline"
      }}
    />
  )
}

export function NoTrainingEmpty({ onStartTraining }: { onStartTraining: () => void }) {
  return (
    <EmptyState
      icon={<Zap className="w-12 h-12" />}
      title="No Training Sessions"
      description="Start training your players to improve their skills and unlock new abilities."
      action={{
        label: "Start Training",
        onClick: onStartTraining
      }}
    />
  )
}

export function NoMatchesEmpty() {
  return (
    <EmptyState
      icon={<Trophy className="w-12 h-12" />}
      title="No Matches Played"
      description="Your team hasn't played any matches yet. Complete your squad and join a league to start competing!"
    />
  )
}

export function NoLeagueEmpty({ onJoinLeague }: { onJoinLeague: () => void }) {
  return (
    <EmptyState
      icon={<Target className="w-12 h-12" />}
      title="Not in a League"
      description="Join a league to compete against other teams and climb the rankings!"
      action={{
        label: "Join League",
        onClick: onJoinLeague
      }}
    />
  )
}

export function InsufficientFundsEmpty({ currentBalance }: { currentBalance: number }) {
  return (
    <EmptyState
      icon={<DollarSign className="w-12 h-12" />}
      title="Insufficient Funds"
      description={`You need more coins to complete this action. Current balance: $${currentBalance.toLocaleString()}`}
    />
  )
}

export function SearchEmpty({ searchTerm }: { searchTerm: string }) {
  return (
    <EmptyState
      icon={<Search className="w-12 h-12" />}
      title="No Results Found"
      description={`No results found for "${searchTerm}". Try adjusting your search criteria.`}
    />
  )
}

export function ErrorEmpty({ onRetry }: { onRetry: () => void }) {
  return (
    <EmptyState
      icon={<AlertCircle className="w-12 h-12" />}
      title="Something Went Wrong"
      description="We encountered an error while loading this content. Please try again."
      action={{
        label: "Try Again",
        onClick: onRetry,
        variant: "outline"
      }}
    />
  )
}

export function NoDataEmpty() {
  return (
    <EmptyState
      icon={<Database className="w-12 h-12" />}
      title="No Data Available"
      description="There's no data to display at the moment. Check back later or contact support if this persists."
    />
  )
}

export function ComingSoonEmpty({ feature }: { feature: string }) {
  return (
    <EmptyState
      icon={<FileX className="w-12 h-12" />}
      title="Coming Soon"
      description={`${feature} is currently under development. Stay tuned for updates!`}
    />
  )
}

// Grid empty state for card layouts
export function GridEmptyState({ children, className }: { children: React.ReactNode, className?: string }) {
  return (
    <div className={cn('col-span-full', className)}>
      <Card variant="elevated">
        <CardContent>
          {children}
        </CardContent>
      </Card>
    </div>
  )
}
