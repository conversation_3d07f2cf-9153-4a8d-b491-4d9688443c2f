import { forwardRef } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'glass' | 'elevated' | 'interactive' | 'stat' | 'gradient'
  hover?: boolean
  animate?: boolean
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', hover = false, animate = false, ...props }, ref) => {
    const variants = {
      default: 'bg-card-bg border border-card-border shadow-sm',
      glass: 'glass border border-card-border/30',
      elevated: 'bg-card-bg border border-card-border shadow-lg hover:shadow-xl transition-all duration-300',
      interactive: 'bg-card-bg border border-card-border shadow-sm hover:shadow-md hover:border-primary/30 hover:-translate-y-1 transition-all duration-300 cursor-pointer',
      stat: 'bg-gradient-to-br from-card-bg to-muted-bg/30 border border-card-border shadow-sm hover:shadow-md transition-all duration-300',
      gradient: 'bg-gradient-to-br from-primary/5 to-secondary/5 border border-primary/20 shadow-sm hover:shadow-md transition-all duration-300',
    }

    const hoverEffects = hover ? 'hover:shadow-lg hover:-translate-y-1 transition-all duration-300' : ''

    const CardComponent = animate ? motion.div : 'div'
    const animationProps = animate ? {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.3 }
    } : {}

    return (
      <CardComponent
        ref={ref}
        className={cn('rounded-xl p-6 relative overflow-hidden', variants[variant], hoverEffects, className)}
        {...(animate ? animationProps : {})}
        {...props}
      />
    )
  }
)

Card.displayName = 'Card'

const CardHeader = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex flex-col space-y-2 pb-6', className)}
      {...props}
    />
  )
)

CardHeader.displayName = 'CardHeader'

const CardTitle = forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn('text-xl font-semibold leading-tight tracking-tight text-foreground flex items-center gap-2', className)}
      {...props}
    />
  )
)

CardTitle.displayName = 'CardTitle'

const CardDescription = forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => (
    <p
      ref={ref}
      className={cn('text-sm text-muted leading-relaxed', className)}
      {...props}
    />
  )
)

CardDescription.displayName = 'CardDescription'

const CardContent = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('pt-0', className)} {...props} />
  )
)

CardContent.displayName = 'CardContent'

const CardFooter = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex items-center justify-between pt-6 border-t border-card-border/50 mt-6', className)}
      {...props}
    />
  )
)

CardFooter.displayName = 'CardFooter'

// Enhanced stat card component for dashboard and other pages
interface StatCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string
  value: string | number
  icon?: React.ReactNode
  trend?: {
    value: number
    isPositive: boolean
  }
  color?: 'blue' | 'green' | 'yellow' | 'purple' | 'red'
}

const StatCard = forwardRef<HTMLDivElement, StatCardProps>(
  ({ className, title, value, icon, trend, color = 'blue', ...props }, ref) => {
    const colorClasses = {
      blue: 'text-blue-500 bg-blue-50 dark:bg-blue-900/20',
      green: 'text-green-500 bg-green-50 dark:bg-green-900/20',
      yellow: 'text-yellow-500 bg-yellow-50 dark:bg-yellow-900/20',
      purple: 'text-purple-500 bg-purple-50 dark:bg-purple-900/20',
      red: 'text-red-500 bg-red-50 dark:bg-red-900/20',
    }

    return (
      <Card ref={ref} variant="stat" className={cn('', className)} {...props}>
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted">{title}</p>
            <p className="text-2xl font-bold text-foreground">{value}</p>
            {trend && (
              <div className={cn('flex items-center text-xs', trend.isPositive ? 'text-green-600' : 'text-red-600')}>
                <span>{trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%</span>
              </div>
            )}
          </div>
          {icon && (
            <div className={cn('p-3 rounded-lg', colorClasses[color])}>
              {icon}
            </div>
          )}
        </div>
      </Card>
    )
  }
)

StatCard.displayName = 'StatCard'

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, StatCard }
