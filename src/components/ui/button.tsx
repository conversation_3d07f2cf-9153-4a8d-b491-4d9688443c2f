import { forwardRef } from 'react'
import { motion } from 'framer-motion'
import { cn } from '@/lib/utils'

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  children: React.ReactNode
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', loading = false, children, disabled, ...props }, ref) => {
    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-focus-ring disabled:opacity-50 disabled:cursor-not-allowed relative'

    const variants = {
      primary: 'bg-primary hover:bg-primary-dark active:bg-primary-dark text-white shadow-sm hover:shadow-md focus:ring-primary focus:ring-offset-focus-ring-offset',
      secondary: 'bg-secondary hover:bg-secondary-dark active:bg-secondary-dark text-white shadow-sm hover:shadow-md focus:ring-secondary focus:ring-offset-focus-ring-offset',
      outline: 'border-2 border-border bg-transparent hover:bg-hover-bg active:bg-active-bg text-foreground focus:ring-primary focus:ring-offset-focus-ring-offset hover:border-primary',
      ghost: 'bg-transparent hover:bg-hover-bg active:bg-active-bg text-foreground focus:ring-primary focus:ring-offset-focus-ring-offset',
      danger: 'bg-error hover:bg-error-light active:bg-error text-white shadow-sm hover:shadow-md focus:ring-error focus:ring-offset-focus-ring-offset',
    }
    
    const sizes = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg',
    }

    return (
      <motion.button
        ref={ref}
        className={cn(baseClasses, variants[variant], sizes[size], className)}
        disabled={disabled || loading}
        whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
        whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
        {...props}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {children}
      </motion.button>
    )
  }
)

Button.displayName = 'Button'

export default Button
