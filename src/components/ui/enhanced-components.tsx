'use client'

import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Trophy, 
  Target,
  Zap,
  Shield,
  Star,
  Award,
  Clock,
  Play,
  Pause,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react'

// Enhanced Progress Bar Component
interface ProgressBarProps {
  value: number
  max?: number
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple'
  size?: 'sm' | 'md' | 'lg'
  showValue?: boolean
  animated?: boolean
  className?: string
}

export function ProgressBar({ 
  value, 
  max = 100, 
  color = 'blue', 
  size = 'md', 
  showValue = false, 
  animated = true,
  className 
}: ProgressBarProps) {
  const percentage = Math.min((value / max) * 100, 100)
  
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500',
    purple: 'bg-purple-500'
  }
  
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  }

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <div className={cn('bg-gray-200 rounded-full overflow-hidden flex-1', sizeClasses[size])}>
        <motion.div
          className={cn('h-full rounded-full', colorClasses[color])}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: animated ? 0.8 : 0, ease: 'easeOut' }}
        />
      </div>
      {showValue && (
        <span className="text-sm font-medium text-muted min-w-[3rem] text-right">
          {Math.round(percentage)}%
        </span>
      )}
    </div>
  )
}

// Enhanced Badge Component
interface BadgeProps {
  children: React.ReactNode
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function Badge({ children, variant = 'default', size = 'md', className }: BadgeProps) {
  const variants = {
    default: 'bg-gray-100 text-gray-800 border-gray-200',
    success: 'bg-green-100 text-green-800 border-green-200',
    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    error: 'bg-red-100 text-red-800 border-red-200',
    info: 'bg-blue-100 text-blue-800 border-blue-200'
  }
  
  const sizes = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base'
  }

  return (
    <span className={cn(
      'inline-flex items-center rounded-full border font-medium',
      variants[variant],
      sizes[size],
      className
    )}>
      {children}
    </span>
  )
}

// Enhanced Alert Component
interface AlertProps {
  children: React.ReactNode
  variant?: 'info' | 'success' | 'warning' | 'error'
  title?: string
  dismissible?: boolean
  onDismiss?: () => void
  className?: string
}

export function Alert({ 
  children, 
  variant = 'info', 
  title, 
  dismissible = false, 
  onDismiss,
  className 
}: AlertProps) {
  const variants = {
    info: {
      bg: 'bg-blue-50 border-blue-200',
      text: 'text-blue-800',
      icon: Info
    },
    success: {
      bg: 'bg-green-50 border-green-200',
      text: 'text-green-800',
      icon: CheckCircle
    },
    warning: {
      bg: 'bg-yellow-50 border-yellow-200',
      text: 'text-yellow-800',
      icon: AlertCircle
    },
    error: {
      bg: 'bg-red-50 border-red-200',
      text: 'text-red-800',
      icon: AlertCircle
    }
  }

  const config = variants[variant]
  const IconComponent = config.icon

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        'rounded-lg border p-4',
        config.bg,
        config.text,
        className
      )}
    >
      <div className="flex items-start">
        <IconComponent className="w-5 h-5 mt-0.5 mr-3 flex-shrink-0" />
        <div className="flex-1">
          {title && (
            <h4 className="font-semibold mb-1">{title}</h4>
          )}
          <div className="text-sm">{children}</div>
        </div>
        {dismissible && onDismiss && (
          <button
            onClick={onDismiss}
            className="ml-3 flex-shrink-0 text-current hover:opacity-70 transition-opacity"
          >
            <span className="sr-only">Dismiss</span>
            ×
          </button>
        )}
      </div>
    </motion.div>
  )
}

// Enhanced Player Card Component
interface PlayerCardProps {
  player: {
    id: string
    name: string
    position: string
    age: number
    overall: number
    fatigue?: number
    morale?: number
    injury_status?: string
  }
  onClick?: () => void
  selected?: boolean
  compact?: boolean
  className?: string
}

export function PlayerCard({ 
  player, 
  onClick, 
  selected = false, 
  compact = false,
  className 
}: PlayerCardProps) {
  const isInjured = player.injury_status && player.injury_status !== 'healthy'
  const isFatigued = (player.fatigue || 0) > 70
  const hasHighMorale = (player.morale || 0) > 80

  return (
    <motion.div
      whileHover={{ scale: onClick ? 1.02 : 1 }}
      whileTap={{ scale: onClick ? 0.98 : 1 }}
      className={cn(
        'p-4 rounded-xl border-2 transition-all duration-200',
        selected 
          ? 'border-primary bg-primary/5' 
          : 'border-card-border bg-card-bg hover:bg-hover-bg hover:border-primary/30',
        onClick && 'cursor-pointer hover:shadow-md',
        isInjured && 'border-red-300 bg-red-50',
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="font-semibold text-foreground truncate">{player.name}</div>
        <div className="text-sm text-muted capitalize font-medium">
          {player.position.replace('-', ' ')}
        </div>
      </div>
      
      {!compact && (
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-muted">Age:</span>
            <span className="font-medium">{player.age}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted">Overall:</span>
            <div className="flex items-center gap-2">
              <ProgressBar 
                value={player.overall} 
                max={100} 
                color={player.overall > 80 ? 'green' : player.overall > 60 ? 'yellow' : 'red'}
                size="sm"
                className="w-12"
              />
              <span className="font-bold text-primary min-w-[2rem]">{player.overall}</span>
            </div>
          </div>
          {player.fatigue !== undefined && (
            <div className="flex justify-between items-center">
              <span className="text-muted">Fatigue:</span>
              <div className="flex items-center gap-2">
                <ProgressBar
                  value={player.fatigue || 0}
                  max={100}
                  color={(player.fatigue || 0) > 70 ? 'red' : (player.fatigue || 0) > 40 ? 'yellow' : 'green'}
                  size="sm"
                  className="w-12"
                />
                <span className={cn(
                  'font-medium min-w-[3rem] text-right',
                  isFatigued ? 'text-red-600' : 'text-green-600'
                )}>
                  {Math.round(player.fatigue || 0)}%
                </span>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Status indicators */}
      <div className="flex items-center justify-end gap-1 mt-2">
        {isInjured && (
          <Badge variant="error" size="sm">Injured</Badge>
        )}
        {isFatigued && !isInjured && (
          <Badge variant="warning" size="sm">Tired</Badge>
        )}
        {hasHighMorale && !isInjured && !isFatigued && (
          <Badge variant="success" size="sm">High Morale</Badge>
        )}
      </div>
    </motion.div>
  )
}

// Enhanced Action Button Component
interface ActionButtonProps {
  children: React.ReactNode
  icon?: React.ReactNode
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  loading?: boolean
  disabled?: boolean
  onClick?: () => void
  className?: string
}

export function ActionButton({
  children,
  icon,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  onClick,
  className
}: ActionButtonProps) {
  return (
    <motion.div
      whileHover={{ scale: disabled || loading ? 1 : 1.05 }}
      whileTap={{ scale: disabled || loading ? 1 : 0.95 }}
    >
      <Button
        variant={variant}
        size={size}
        loading={loading}
        disabled={disabled}
        onClick={onClick}
        className={cn('flex items-center gap-2', className)}
      >
        {icon && !loading && icon}
        {children}
      </Button>
    </motion.div>
  )
}
