'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { TransferListing, TransferBid, Team, Player } from '@/lib/types'
import { calculatePlayerOverall } from '@/lib/game-engine'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { 
  Search, 
  Filter, 
  DollarSign, 
  Clock, 
  User, 
  TrendingUp,
  Star,
  Calendar,
  Target
} from 'lucide-react'

interface TransferMarketProps {
  listings: TransferListing[]
  userTeam: Team
  userCash: number
  onPlaceBid: (listing: TransferListing, bidAmount: number) => void
  onBuyNow?: (listing: TransferListing) => void
}

export default function TransferMarket({
  listings,
  userCash,
  onPlaceBid,
  onBuyNow
}: TransferMarketProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [positionFilter, setPositionFilter] = useState<string>('all')
  const [priceFilter, setPriceFilter] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'price' | 'rating' | 'age' | 'expires'>('rating')
  const [selectedListing, setSelectedListing] = useState<TransferListing | null>(null)
  const [bidAmount, setBidAmount] = useState('')

  const positions = ['all', 'goalkeeper', 'left-wing', 'right-wing', 'left-driver', 'right-driver', 'center-forward', 'point']
  const priceRanges = [
    { label: 'All', value: 'all' },
    { label: 'Under $10k', value: '0-10000' },
    { label: '$10k - $50k', value: '10000-50000' },
    { label: '$50k - $100k', value: '50000-100000' },
    { label: 'Over $100k', value: '100000-999999999' }
  ]

  const filteredListings = listings
    .filter(listing => {
      if (!listing.player) return false
      
      // Search filter
      if (searchTerm && !listing.player.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false
      }
      
      // Position filter
      if (positionFilter !== 'all' && listing.player.position !== positionFilter) {
        return false
      }
      
      // Price filter
      if (priceFilter !== 'all') {
        const [min, max] = priceFilter.split('-').map(Number)
        if (listing.asking_price < min || listing.asking_price > max) {
          return false
        }
      }
      
      return listing.status === 'available'
    })
    .sort((a, b) => {
      if (!a.player || !b.player) return 0
      
      switch (sortBy) {
        case 'price':
          return a.asking_price - b.asking_price
        case 'rating':
          return calculatePlayerOverall(b.player) - calculatePlayerOverall(a.player)
        case 'age':
          return a.player.age - b.player.age
        case 'expires':
          return new Date(a.expires_at || '').getTime() - new Date(b.expires_at || '').getTime()
        default:
          return 0
      }
    })

  const handlePlaceBid = () => {
    if (!selectedListing || !bidAmount) return
    
    const amount = parseInt(bidAmount)
    if (amount <= 0 || amount > userCash) return
    
    onPlaceBid(selectedListing, amount)
    setSelectedListing(null)
    setBidAmount('')
  }

  const getTimeRemaining = (expiresAt: string) => {
    const now = new Date()
    const expires = new Date(expiresAt)
    const diff = expires.getTime() - now.getTime()
    
    if (diff <= 0) return 'Expired'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) return `${days}d ${hours}h`
    return `${hours}h`
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted w-4 h-4" />
              <input
                type="text"
                placeholder="Search players..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
            
            <select
              value={positionFilter}
              onChange={(e) => setPositionFilter(e.target.value)}
              className="px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            >
              {positions.map(position => (
                <option key={position} value={position}>
                  {position === 'all' ? 'All Positions' : position.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </option>
              ))}
            </select>
            
            <select
              value={priceFilter}
              onChange={(e) => setPriceFilter(e.target.value)}
              className="px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            >
              {priceRanges.map(range => (
                <option key={range.value} value={range.value}>
                  {range.label}
                </option>
              ))}
            </select>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="rating">Sort by Rating</option>
              <option value="price">Sort by Price</option>
              <option value="age">Sort by Age</option>
              <option value="expires">Sort by Time Left</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Transfer Listings */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredListings.map((listing, index) => {
          if (!listing.player) return null
          
          const player = listing.player
          const overall = calculatePlayerOverall(player)
          const timeRemaining = listing.expires_at ? getTimeRemaining(listing.expires_at) : 'No limit'
          const isExpiringSoon = listing.expires_at && new Date(listing.expires_at).getTime() - Date.now() < 24 * 60 * 60 * 1000
          
          return (
            <motion.div
              key={listing.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={`hover:shadow-lg transition-shadow cursor-pointer ${
                isExpiringSoon ? 'border-red-500/50' : ''
              }`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{player.name}</CardTitle>
                      <div className="text-sm text-muted capitalize">
                        {player.position.replace('-', ' ')} • Age {player.age}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-1">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span className="font-bold">{overall}</span>
                      </div>
                      <div className="text-xs text-muted">Overall</div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent>
                  {/* Player Stats */}
                  <div className="grid grid-cols-2 gap-2 mb-4 text-sm">
                    <div className="flex justify-between">
                      <span>Shooting:</span>
                      <span className="font-medium">{Math.round(player.shooting || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Speed:</span>
                      <span className="font-medium">{Math.round(player.speed || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Passing:</span>
                      <span className="font-medium">{Math.round(player.passing || 0)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Defense:</span>
                      <span className="font-medium">{Math.round(player.defense || 0)}</span>
                    </div>
                  </div>
                  
                  {/* Transfer Info */}
                  <div className="border-t pt-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted">Asking Price:</span>
                      <span className="font-bold text-lg">${listing.asking_price.toLocaleString()}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted">Type:</span>
                      <span className="capitalize">{listing.transfer_type}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted flex items-center">
                        <Clock className="w-3 h-3 mr-1" />
                        Expires:
                      </span>
                      <span className={isExpiringSoon ? 'text-red-500 font-medium' : ''}>
                        {timeRemaining}
                      </span>
                    </div>
                    
                    {listing.selling_team && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted">From:</span>
                        <span>{listing.selling_team.name}</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex space-x-2 mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedListing(listing)}
                      className="flex-1"
                    >
                      <Target className="w-4 h-4 mr-1" />
                      Bid
                    </Button>
                    
                    {onBuyNow && listing.transfer_type === 'sale' && (
                      <Button
                        size="sm"
                        onClick={() => onBuyNow(listing)}
                        disabled={listing.asking_price > userCash}
                        className="flex-1"
                      >
                        <DollarSign className="w-4 h-4 mr-1" />
                        Buy Now
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </div>

      {/* Bid Modal */}
      {selectedListing && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-background rounded-lg p-6 max-w-md w-full mx-4"
          >
            <h3 className="text-lg font-bold mb-4">
              Place Bid for {selectedListing.player?.name}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Bid Amount
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted w-4 h-4" />
                  <input
                    type="number"
                    value={bidAmount}
                    onChange={(e) => setBidAmount(e.target.value)}
                    placeholder="Enter bid amount"
                    className="w-full pl-10 pr-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
                <div className="text-xs text-muted mt-1">
                  Asking price: ${selectedListing.asking_price.toLocaleString()}
                </div>
                <div className="text-xs text-muted">
                  Available: ${userCash.toLocaleString()}
                </div>
              </div>
              
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setSelectedListing(null)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handlePlaceBid}
                  disabled={!bidAmount || parseInt(bidAmount) <= 0 || parseInt(bidAmount) > userCash}
                  className="flex-1"
                >
                  Place Bid
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Empty State */}
      {filteredListings.length === 0 && (
        <Card>
          <CardContent className="pt-6 text-center py-12">
            <User className="w-12 h-12 mx-auto text-muted mb-4" />
            <h3 className="text-lg font-medium mb-2">No players found</h3>
            <p className="text-muted">
              Try adjusting your search filters to find more players.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
