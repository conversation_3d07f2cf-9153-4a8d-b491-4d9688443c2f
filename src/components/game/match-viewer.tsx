'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { MatchSimulation, MatchEvent, Team } from '@/lib/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { 
  Play, 
  Pause, 
  SkipForward, 
  Target, 
  Shield, 
  Clock,
  Users,
  Trophy,
  AlertTriangle
} from 'lucide-react'

interface MatchViewerProps {
  simulation: MatchSimulation
  onMatchComplete?: (simulation: MatchSimulation) => void
  autoPlay?: boolean
}

export default function MatchViewer({ 
  simulation, 
  onMatchComplete, 
  autoPlay = false 
}: MatchViewerProps) {
  const [currentEventIndex, setCurrentEventIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(autoPlay)
  const [playbackSpeed, setPlaybackSpeed] = useState(1)

  const currentEvent = simulation.events[currentEventIndex]
  const isMatchComplete = currentEventIndex >= simulation.events.length

  useEffect(() => {
    if (!isPlaying || isMatchComplete) return

    const interval = setInterval(() => {
      setCurrentEventIndex(prev => {
        const next = prev + 1
        if (next >= simulation.events.length) {
          setIsPlaying(false)
          onMatchComplete?.(simulation)
          return prev
        }
        return next
      })
    }, 2000 / playbackSpeed)

    return () => clearInterval(interval)
  }, [isPlaying, isMatchComplete, playbackSpeed, simulation.events.length, onMatchComplete, simulation])

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'goal': return Target
      case 'save': return Shield
      case 'exclusion': return AlertTriangle
      case 'penalty': return Trophy
      default: return Clock
    }
  }

  const getEventColor = (eventType: string) => {
    switch (eventType) {
      case 'goal': return 'text-green-500'
      case 'save': return 'text-blue-500'
      case 'exclusion': return 'text-red-500'
      case 'penalty': return 'text-yellow-500'
      default: return 'text-muted'
    }
  }

  const getCurrentScore = () => {
    let homeScore = 0
    let awayScore = 0
    
    simulation.events.slice(0, currentEventIndex + 1).forEach(event => {
      if (event.event_type === 'goal') {
        if (event.team_id === simulation.home_team.id) {
          homeScore++
        } else {
          awayScore++
        }
      }
    })
    
    return { homeScore, awayScore }
  }

  const { homeScore, awayScore } = getCurrentScore()

  return (
    <div className="space-y-6">
      {/* Match Header */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="text-center flex-1">
              <div className="text-lg font-bold">{simulation.home_team.name}</div>
              <div className="text-sm text-muted">Home</div>
            </div>
            
            <div className="text-center px-8">
              <div className="text-4xl font-bold">
                {homeScore} - {awayScore}
              </div>
              <div className="text-sm text-muted">
                {currentEvent ? `${currentEvent.minute}'` : 'Full Time'}
              </div>
            </div>
            
            <div className="text-center flex-1">
              <div className="text-lg font-bold">{simulation.away_team.name}</div>
              <div className="text-sm text-muted">Away</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Match Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsPlaying(!isPlaying)}
              disabled={isMatchComplete}
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentEventIndex(prev => Math.min(prev + 1, simulation.events.length - 1))}
              disabled={isMatchComplete}
            >
              <SkipForward className="w-4 h-4" />
            </Button>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm">Speed:</span>
              {[0.5, 1, 2, 4].map(speed => (
                <Button
                  key={speed}
                  variant={playbackSpeed === speed ? "default" : "outline"}
                  size="sm"
                  onClick={() => setPlaybackSpeed(speed)}
                >
                  {speed}x
                </Button>
              ))}
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-4">
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ 
                  width: `${simulation.events.length > 0 ? (currentEventIndex / simulation.events.length) * 100 : 0}%` 
                }}
              />
            </div>
            <div className="flex justify-between text-xs text-muted mt-1">
              <span>0'</span>
              <span>32'</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Event */}
      <AnimatePresence mode="wait">
        {currentEvent && (
          <motion.div
            key={currentEventIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-4">
                  <div className={`p-3 rounded-full bg-muted ${getEventColor(currentEvent.event_type)}`}>
                    {(() => {
                      const Icon = getEventIcon(currentEvent.event_type)
                      return <Icon className="w-6 h-6" />
                    })()}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div className="font-medium">
                        {currentEvent.minute}' - {currentEvent.event_type.toUpperCase()}
                      </div>
                      <div className="text-sm text-muted">
                        {currentEvent.team_id === simulation.home_team.id 
                          ? simulation.home_team.name 
                          : simulation.away_team.name}
                      </div>
                    </div>
                    <div className="text-sm text-muted mt-1">
                      {currentEvent.description}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Match Events Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Match Events
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {simulation.events.slice(0, currentEventIndex + 1).map((event, index) => {
              const Icon = getEventIcon(event.event_type)
              const isCurrentEvent = index === currentEventIndex
              
              return (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`flex items-center space-x-3 p-2 rounded-lg ${
                    isCurrentEvent ? 'bg-primary/10 border border-primary' : 'hover:bg-muted/50'
                  }`}
                >
                  <div className="text-sm font-mono w-8">
                    {event.minute}'
                  </div>
                  
                  <div className={`p-1 rounded ${getEventColor(event.event_type)}`}>
                    <Icon className="w-4 h-4" />
                  </div>
                  
                  <div className="flex-1">
                    <div className="text-sm">
                      {event.description}
                    </div>
                  </div>
                  
                  <div className="text-xs text-muted">
                    {event.team_id === simulation.home_team.id ? 'H' : 'A'}
                  </div>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Match Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-center">{simulation.home_team.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Goals</span>
                <span className="font-bold">{homeScore}</span>
              </div>
              <div className="flex justify-between">
                <span>Saves</span>
                <span>{simulation.events.filter(e => e.event_type === 'save' && e.team_id === simulation.home_team.id).length}</span>
              </div>
              <div className="flex justify-between">
                <span>Exclusions</span>
                <span>{simulation.events.filter(e => e.event_type === 'exclusion' && e.team_id === simulation.home_team.id).length}</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-center">{simulation.away_team.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Goals</span>
                <span className="font-bold">{awayScore}</span>
              </div>
              <div className="flex justify-between">
                <span>Saves</span>
                <span>{simulation.events.filter(e => e.event_type === 'save' && e.team_id === simulation.away_team.id).length}</span>
              </div>
              <div className="flex justify-between">
                <span>Exclusions</span>
                <span>{simulation.events.filter(e => e.event_type === 'exclusion' && e.team_id === simulation.away_team.id).length}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Match Complete */}
      {isMatchComplete && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="border-primary">
            <CardContent className="pt-6 text-center">
              <Trophy className="w-12 h-12 mx-auto mb-4 text-primary" />
              <h3 className="text-xl font-bold mb-2">Full Time</h3>
              <div className="text-lg">
                {homeScore > awayScore 
                  ? `${simulation.home_team.name} wins!`
                  : awayScore > homeScore 
                    ? `${simulation.away_team.name} wins!`
                    : "It's a draw!"
                }
              </div>
              <div className="text-3xl font-bold mt-2">
                {homeScore} - {awayScore}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
