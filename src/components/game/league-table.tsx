'use client'

import { motion } from 'framer-motion'
import { League } from '@/lib/types'
import { generateLeagueTable, LeagueTable } from '@/lib/league-system'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Trophy, TrendingUp, TrendingDown, Minus } from 'lucide-react'

interface LeagueTableProps {
  league: League
  userTeamId?: string
  showPromotionRelegation?: boolean
}

export default function LeagueTableComponent({ 
  league, 
  userTeamId, 
  showPromotionRelegation = true 
}: LeagueTableProps) {
  const table = generateLeagueTable(league)

  const getPositionColor = (position: number, totalTeams: number) => {
    if (!showPromotionRelegation) return ''
    
    if (position === 1) return 'bg-yellow-500/20 border-yellow-500' // Champion
    if (position <= 2) return 'bg-green-500/20 border-green-500' // Promotion
    if (position >= totalTeams - 1) return 'bg-red-500/20 border-red-500' // Relegation
    
    return ''
  }

  const getPositionIcon = (position: number, totalTeams: number) => {
    if (!showPromotionRelegation) return null
    
    if (position === 1) return <Trophy className="w-4 h-4 text-yellow-500" />
    if (position <= 2) return <TrendingUp className="w-4 h-4 text-green-500" />
    if (position >= totalTeams - 1) return <TrendingDown className="w-4 h-4 text-red-500" />
    
    return <Minus className="w-4 h-4 text-muted" />
  }

  const getFormIcon = (result: 'W' | 'D' | 'L') => {
    const colors = {
      W: 'bg-green-500',
      D: 'bg-yellow-500',
      L: 'bg-red-500'
    }
    
    return (
      <div className={`w-2 h-2 rounded-full ${colors[result]}`} />
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <Trophy className="w-5 h-5 mr-2" />
            {league.name} - Level {league.level}
          </span>
          <div className="text-sm text-muted">
            Matchday {league.current_matchday}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b text-sm text-muted">
                <th className="text-left py-2 w-12">Pos</th>
                <th className="text-left py-2">Team</th>
                <th className="text-center py-2 w-12">P</th>
                <th className="text-center py-2 w-12">W</th>
                <th className="text-center py-2 w-12">D</th>
                <th className="text-center py-2 w-12">L</th>
                <th className="text-center py-2 w-16">GF</th>
                <th className="text-center py-2 w-16">GA</th>
                <th className="text-center py-2 w-16">GD</th>
                <th className="text-center py-2 w-12">Pts</th>
                <th className="text-center py-2 w-20">Form</th>
              </tr>
            </thead>
            <tbody>
              {table.map((entry, index) => {
                const isUserTeam = entry.team.id === userTeamId
                const positionColor = getPositionColor(entry.position, table.length)
                
                return (
                  <motion.tr
                    key={entry.team.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className={`border-b hover:bg-muted/50 transition-colors ${
                      isUserTeam ? 'bg-primary/5 font-medium' : ''
                    } ${positionColor}`}
                  >
                    <td className="py-3">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-mono w-6 text-center">
                          {entry.position}
                        </span>
                        {getPositionIcon(entry.position, table.length)}
                      </div>
                    </td>
                    
                    <td className="py-3">
                      <div className="flex items-center space-x-2">
                        {entry.team.logo_url && (
                          <img 
                            src={entry.team.logo_url} 
                            alt={`${entry.team.name} logo`}
                            className="w-6 h-6 rounded"
                          />
                        )}
                        <span className={`${isUserTeam ? 'font-bold' : ''}`}>
                          {entry.team.name}
                        </span>
                      </div>
                    </td>
                    
                    <td className="text-center py-3 text-sm">{entry.played}</td>
                    <td className="text-center py-3 text-sm">{entry.won}</td>
                    <td className="text-center py-3 text-sm">{entry.drawn}</td>
                    <td className="text-center py-3 text-sm">{entry.lost}</td>
                    <td className="text-center py-3 text-sm">{entry.goalsFor}</td>
                    <td className="text-center py-3 text-sm">{entry.goalsAgainst}</td>
                    <td className={`text-center py-3 text-sm ${
                      entry.goalDifference > 0 ? 'text-green-600' : 
                      entry.goalDifference < 0 ? 'text-red-600' : ''
                    }`}>
                      {entry.goalDifference > 0 ? '+' : ''}{entry.goalDifference}
                    </td>
                    <td className="text-center py-3 text-sm font-bold">
                      {entry.points}
                    </td>
                    
                    <td className="text-center py-3">
                      <div className="flex items-center justify-center space-x-1">
                        {entry.form.slice(-5).map((result, formIndex) => (
                          <div key={formIndex} title={result === 'W' ? 'Win' : result === 'D' ? 'Draw' : 'Loss'}>
                            {getFormIcon(result)}
                          </div>
                        ))}
                      </div>
                    </td>
                  </motion.tr>
                )
              })}
            </tbody>
          </table>
        </div>

        {/* Legend */}
        {showPromotionRelegation && (
          <div className="mt-6 flex flex-wrap gap-4 text-xs">
            <div className="flex items-center space-x-2">
              <Trophy className="w-4 h-4 text-yellow-500" />
              <span>Champion</span>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-4 h-4 text-green-500" />
              <span>Promotion</span>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingDown className="w-4 h-4 text-red-500" />
              <span>Relegation</span>
            </div>
          </div>
        )}

        {/* Season Progress */}
        <div className="mt-4 p-3 bg-muted/50 rounded-lg">
          <div className="flex justify-between items-center text-sm">
            <span>Season Progress</span>
            <span>{league.current_matchday} / {(table.length - 1) * 2} matchdays</span>
          </div>
          <div className="w-full bg-muted rounded-full h-2 mt-2">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ 
                width: `${(league.current_matchday / ((table.length - 1) * 2)) * 100}%` 
              }}
            />
          </div>
        </div>

        {/* Prize Money */}
        <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
          <div className="p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
            <div className="font-medium text-yellow-700">Champion Prize</div>
            <div className="text-lg font-bold">${league.prize_money.toLocaleString()}</div>
          </div>
          <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
            <div className="font-medium text-green-700">Promotion Bonus</div>
            <div className="text-lg font-bold">${Math.round(league.prize_money * 0.5).toLocaleString()}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
