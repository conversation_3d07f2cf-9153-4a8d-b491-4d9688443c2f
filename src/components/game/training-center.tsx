'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Player, TrainingSession, TRAINING_TYPES } from '@/lib/types'
import { calculateTrainingGains, applyTrainingToPlayer } from '@/lib/game-engine'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { 
  Zap, 
  Target, 
  Waves, 
  Users, 
  Shield, 
  Brain,
  Timer,
  TrendingUp,
  AlertCircle,
  Play
} from 'lucide-react'

interface TrainingCenterProps {
  players: Player[]
  onTrainingComplete: (updatedPlayers: Player[], session: TrainingSession) => void
  teamCash: number
  facilityBonus: number
}

export default function TrainingCenter({ 
  players, 
  onTrainingComplete, 
  teamCash, 
  facilityBonus 
}: TrainingCenterProps) {
  const [selectedTraining, setSelectedTraining] = useState<keyof typeof TRAINING_TYPES>('shooting')
  const [selectedIntensity, setSelectedIntensity] = useState<'light' | 'medium' | 'high' | 'extreme'>('medium')
  const [selectedPlayers, setSelectedPlayers] = useState<string[]>([])
  const [isTraining, setIsTraining] = useState(false)

  const trainingType = TRAINING_TYPES[selectedTraining]
  const intensityMultiplier = {
    light: 0.7,
    medium: 1.0,
    high: 1.3,
    extreme: 1.6
  }[selectedIntensity]

  const baseCost = trainingType.base_cost * intensityMultiplier
  const totalCost = Math.round(baseCost * selectedPlayers.length)

  const getTrainingIcon = (type: keyof typeof TRAINING_TYPES) => {
    const icons = {
      shooting: Target,
      speed: Waves,
      passing: Users,
      defense: Shield,
      endurance: Timer,
      awareness: Brain,
      goalkeeping: Zap,
      tactical: Users
    }
    return icons[type]
  }

  const handlePlayerToggle = (playerId: string) => {
    setSelectedPlayers(prev => 
      prev.includes(playerId) 
        ? prev.filter(id => id !== playerId)
        : [...prev, playerId]
    )
  }

  const handleStartTraining = async () => {
    if (selectedPlayers.length === 0 || totalCost > teamCash) return

    setIsTraining(true)

    // Simulate training delay
    await new Promise(resolve => setTimeout(resolve, 2000))

    const updatedPlayers = [...players]
    const trainingSession: TrainingSession = {
      id: crypto.randomUUID(),
      team_id: players[0]?.team_id || '',
      training_type: selectedTraining,
      intensity: selectedIntensity,
      duration_minutes: 90,
      cost: totalCost,
      experience_gained: 0,
      fatigue_added: 0,
      completed_at: new Date().toISOString(),
      created_at: new Date().toISOString()
    }

    selectedPlayers.forEach(playerId => {
      const playerIndex = updatedPlayers.findIndex(p => p.id === playerId)
      if (playerIndex !== -1) {
        const player = updatedPlayers[playerIndex]
        const gains = calculateTrainingGains(player, selectedTraining, selectedIntensity, facilityBonus)
        updatedPlayers[playerIndex] = applyTrainingToPlayer(player, gains.statGains, gains.experienceGained, gains.fatigueAdded)
      }
    })

    setIsTraining(false)
    setSelectedPlayers([])
    onTrainingComplete(updatedPlayers, trainingSession)
  }

  return (
    <div className="space-y-6">
      {/* Training Type Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="w-5 h-5 mr-2" />
            Training Programs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(TRAINING_TYPES).map(([key, training]) => {
              const Icon = getTrainingIcon(key as keyof typeof TRAINING_TYPES)
              const isSelected = selectedTraining === key
              
              return (
                <motion.button
                  key={key}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setSelectedTraining(key as keyof typeof TRAINING_TYPES)}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    isSelected 
                      ? 'border-primary bg-primary/10' 
                      : 'border-border hover:border-primary/50'
                  }`}
                >
                  <Icon className={`w-8 h-8 mx-auto mb-2 ${isSelected ? 'text-primary' : 'text-muted'}`} />
                  <div className="text-sm font-medium">{training.name}</div>
                  <div className="text-xs text-muted mt-1">${training.base_cost}</div>
                </motion.button>
              )
            })}
          </div>
          
          <div className="mt-4 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium mb-2">{trainingType.name}</h4>
            <p className="text-sm text-muted mb-2">{trainingType.description}</p>
            <div className="flex flex-wrap gap-2">
              <span className="text-xs bg-primary/20 text-primary px-2 py-1 rounded">
                Primary: {trainingType.primary_stat}
              </span>
              {trainingType.secondary_stats.map(stat => (
                <span key={stat} className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded">
                  {stat}
                </span>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Intensity Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="w-5 h-5 mr-2" />
            Training Intensity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            {(['light', 'medium', 'high', 'extreme'] as const).map(intensity => {
              const isSelected = selectedIntensity === intensity
              const multiplier = {
                light: 0.7,
                medium: 1.0,
                high: 1.3,
                extreme: 1.6
              }[intensity]
              
              return (
                <motion.button
                  key={intensity}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setSelectedIntensity(intensity)}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    isSelected 
                      ? 'border-primary bg-primary/10' 
                      : 'border-border hover:border-primary/50'
                  }`}
                >
                  <div className="text-sm font-medium capitalize">{intensity}</div>
                  <div className="text-xs text-muted">{Math.round(multiplier * 100)}% effect</div>
                  <div className="text-xs text-muted">+{Math.round(trainingType.base_fatigue * multiplier)} fatigue</div>
                </motion.button>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Player Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Users className="w-5 h-5 mr-2" />
              Select Players ({selectedPlayers.length})
            </span>
            <div className="text-sm text-muted">
              Total Cost: ${totalCost.toLocaleString()}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {players.map(player => {
              const isSelected = selectedPlayers.includes(player.id)
              const isTooFatigued = player.fatigue > 80
              const isInjured = player.injury_status !== 'healthy'
              
              return (
                <motion.div
                  key={player.id}
                  whileHover={{ scale: 1.01 }}
                  className={`p-3 rounded-lg border cursor-pointer transition-all ${
                    isSelected 
                      ? 'border-primary bg-primary/10' 
                      : 'border-border hover:border-primary/50'
                  } ${(isTooFatigued || isInjured) ? 'opacity-50' : ''}`}
                  onClick={() => !isTooFatigued && !isInjured && handlePlayerToggle(player.id)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">{player.name}</div>
                      <div className="text-sm text-muted capitalize">{player.position.replace('-', ' ')}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm">
                        {trainingType.primary_stat}: {player[trainingType.primary_stat as keyof Player] as number}
                      </div>
                      <div className="text-xs text-muted">
                        Fatigue: {player.fatigue}% | Morale: {player.morale}%
                      </div>
                      {(isTooFatigued || isInjured) && (
                        <div className="flex items-center text-xs text-red-500 mt-1">
                          <AlertCircle className="w-3 h-3 mr-1" />
                          {isInjured ? 'Injured' : 'Too Fatigued'}
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Training Summary & Start */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-muted mb-1">Training Summary</div>
              <div className="font-medium">
                {trainingType.name} - {selectedIntensity} intensity
              </div>
              <div className="text-sm text-muted">
                {selectedPlayers.length} players selected
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold">${totalCost.toLocaleString()}</div>
              <div className="text-sm text-muted">
                Available: ${teamCash.toLocaleString()}
              </div>
            </div>
          </div>
          
          <Button
            onClick={handleStartTraining}
            disabled={selectedPlayers.length === 0 || totalCost > teamCash || isTraining}
            className="w-full mt-4"
            size="lg"
          >
            {isTraining ? (
              <>
                <Timer className="w-4 h-4 mr-2 animate-spin" />
                Training in Progress...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Start Training Session
              </>
            )}
          </Button>
          
          {totalCost > teamCash && (
            <div className="flex items-center text-red-500 text-sm mt-2">
              <AlertCircle className="w-4 h-4 mr-1" />
              Insufficient funds for this training session
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
