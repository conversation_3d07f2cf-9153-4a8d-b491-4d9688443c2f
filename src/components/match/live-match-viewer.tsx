'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { RealTimeMatchEngine, LiveMatchState, MatchEventData, MatchAction } from '@/lib/real-time-match-engine'
import { Team, TacticalSetup } from '@/lib/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import {
  Play,
  Pause,
  RotateCcw,
  Users,
  Clock,
  Target,
  Activity,
  Zap,
  AlertTriangle,
  Trophy,
  Timer,
  Settings,
  ArrowUpDown
} from 'lucide-react'

interface LiveMatchViewerProps {
  homeTeam: Team
  awayTeam: Team
  homeTactics: TacticalSetup
  awayTactics: TacticalSetup
  onMatchComplete?: (result: any) => void
}

export default function LiveMatchViewer({
  homeTeam,
  awayTeam,
  homeTactics,
  awayTactics,
  onMatchComplete
}: LiveMatchViewerProps) {
  const [matchState, setMatchState] = useState<LiveMatchState | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [events, setEvents] = useState<MatchEventData[]>([])
  const [simulationSpeed, setSimulationSpeed] = useState(2)
  const [showTactics, setShowTactics] = useState(false)
  const engineRef = useRef<RealTimeMatchEngine | null>(null)
  const eventsEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    initializeMatch()
    return () => {
      if (engineRef.current) {
        engineRef.current.stopSimulation()
      }
    }
  }, [])

  useEffect(() => {
    eventsEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [events])

  const initializeMatch = async () => {
    engineRef.current = new RealTimeMatchEngine()
    
    // Subscribe to match events
    engineRef.current.onEvent((event: MatchEventData) => {
      setEvents(prev => [...prev, event])
    })

    // Initialize the match
    const initialState = await engineRef.current.initializeMatch(
      homeTeam,
      awayTeam,
      homeTactics,
      awayTactics
    )
    
    setMatchState(initialState)
  }

  const toggleSimulation = () => {
    if (!engineRef.current) return

    if (isPlaying) {
      engineRef.current.stopSimulation()
      setIsPlaying(false)
    } else {
      engineRef.current.startSimulation(simulationSpeed)
      setIsPlaying(true)
      
      // Update match state periodically
      const interval = setInterval(() => {
        const currentState = engineRef.current?.getMatchState()
        if (currentState) {
          setMatchState({ ...currentState })
          
          if (currentState.status === 'finished') {
            setIsPlaying(false)
            clearInterval(interval)
            onMatchComplete?.(currentState)
          }
        }
      }, 500)
    }
  }

  const makeSubstitution = async (team: 'home' | 'away', playerOut: string, playerIn: string) => {
    if (!engineRef.current) return

    const action: MatchAction = {
      type: 'substitution',
      team,
      data: { playerOut, playerIn }
    }

    await engineRef.current.applyAction(action)
  }

  const callTimeout = async (team: 'home' | 'away') => {
    if (!engineRef.current) return

    const action: MatchAction = {
      type: 'timeout',
      team,
      data: {}
    }

    await engineRef.current.applyAction(action)
  }

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'goal': return <Target className="w-4 h-4 text-green-500" />
      case 'save': return <Zap className="w-4 h-4 text-blue-500" />
      case 'exclusion': return <AlertTriangle className="w-4 h-4 text-red-500" />
      case 'timeout': return <Timer className="w-4 h-4 text-yellow-500" />
      case 'substitution': return <ArrowUpDown className="w-4 h-4 text-purple-500" />
      default: return <Activity className="w-4 h-4 text-gray-500" />
    }
  }

  const getQuarterProgress = () => {
    if (!matchState) return 0
    const minuteInQuarter = matchState.current_minute % 8
    return (minuteInQuarter / 8) * 100
  }

  if (!matchState) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p>Initializing match...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Match Header */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="text-center flex-1">
              <div className="text-lg font-bold">{homeTeam.name}</div>
              <div className="text-3xl font-bold text-primary mt-2">
                {matchState.home_score}
              </div>
            </div>
            
            <div className="text-center px-8">
              <div className="text-sm text-muted mb-2">
                {matchState.status.replace('_', ' ').toUpperCase()}
              </div>
              <div className="text-2xl font-bold">
                {Math.floor(matchState.current_minute / 8) + 1}Q
              </div>
              <div className="text-sm text-muted">
                {matchState.current_minute % 8 + 1}:00
              </div>
              
              {/* Quarter Progress Bar */}
              <div className="w-24 h-2 bg-gray-200 rounded-full mt-2 mx-auto">
                <div 
                  className="h-full bg-primary rounded-full transition-all duration-1000"
                  style={{ width: `${getQuarterProgress()}%` }}
                />
              </div>
            </div>
            
            <div className="text-center flex-1">
              <div className="text-lg font-bold">{awayTeam.name}</div>
              <div className="text-3xl font-bold text-primary mt-2">
                {matchState.away_score}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Match Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center space-x-4">
            <Button
              onClick={toggleSimulation}
              variant={isPlaying ? "outline" : "default"}
              className="flex items-center space-x-2"
            >
              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              <span>{isPlaying ? 'Pause' : 'Play'}</span>
            </Button>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm">Speed:</span>
              <select
                value={simulationSpeed}
                onChange={(e) => setSimulationSpeed(Number(e.target.value))}
                className="px-2 py-1 border rounded text-sm"
                disabled={isPlaying}
              >
                <option value={0.5}>0.5x</option>
                <option value={1}>1x</option>
                <option value={2}>2x</option>
                <option value={5}>5x</option>
                <option value={10}>10x</option>
              </select>
            </div>
            
            <Button
              onClick={() => setShowTactics(!showTactics)}
              variant="outline"
              size="sm"
            >
              <Settings className="w-4 h-4 mr-2" />
              Tactics
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Live Events Feed */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-5 h-5" />
                <span>Live Match Feed</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-96 overflow-y-auto space-y-3">
                <AnimatePresence>
                  {events.map((event, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className={`flex items-start space-x-3 p-3 rounded-lg ${
                        event.team === 'home' ? 'bg-blue-50 dark:bg-blue-900/20' : 'bg-red-50 dark:bg-red-900/20'
                      }`}
                    >
                      <div className="flex-shrink-0 mt-1">
                        {getEventIcon(event.type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{event.description}</span>
                          <span className="text-xs text-muted">
                            {Math.floor(event.minute / 8) + 1}Q {event.minute % 8 + 1}:00
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
                <div ref={eventsEndRef} />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Match Stats & Actions */}
        <div className="space-y-6">
          {/* Team Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Manager Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">{homeTeam.name}</h4>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => callTimeout('home')}
                    disabled={matchState.timeouts_used.home >= 2}
                  >
                    <Timer className="w-3 h-3 mr-1" />
                    Timeout ({2 - matchState.timeouts_used.home})
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    disabled={matchState.substitutions_used.home >= 6}
                  >
                    <Users className="w-3 h-3 mr-1" />
                    Sub ({6 - matchState.substitutions_used.home})
                  </Button>
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">{awayTeam.name}</h4>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => callTimeout('away')}
                    disabled={matchState.timeouts_used.away >= 2}
                  >
                    <Timer className="w-3 h-3 mr-1" />
                    Timeout ({2 - matchState.timeouts_used.away})
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    disabled={matchState.substitutions_used.away >= 6}
                  >
                    <Users className="w-3 h-3 mr-1" />
                    Sub ({6 - matchState.substitutions_used.away})
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Match Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Match Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span>Possession</span>
                  <span>50% - 50%</span>
                </div>
                <div className="flex justify-between">
                  <span>Shots</span>
                  <span>
                    {events.filter(e => e.type === 'goal' && e.team === 'home').length} - {events.filter(e => e.type === 'goal' && e.team === 'away').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Saves</span>
                  <span>
                    {events.filter(e => e.type === 'save' && e.team === 'home').length} - {events.filter(e => e.type === 'save' && e.team === 'away').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Exclusions</span>
                  <span>
                    {events.filter(e => e.type === 'exclusion' && e.team === 'home').length} - {events.filter(e => e.type === 'exclusion' && e.team === 'away').length}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
