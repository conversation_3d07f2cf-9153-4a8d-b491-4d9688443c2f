'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { MatchNotification, matchNotificationSystem } from '@/lib/match-notification-system'
import { Card, CardContent } from '@/components/ui/card'
import But<PERSON> from '@/components/ui/button'
import { 
  Bell, 
  Clock, 
  X, 
  Play, 
  Timer,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

interface MatchNotificationsProps {
  userId: string
}

export default function MatchNotifications({ userId }: MatchNotificationsProps) {
  const [notifications, setNotifications] = useState<MatchNotification[]>([])
  const [showNotifications, setShowNotifications] = useState(false)

  useEffect(() => {
    loadNotifications()
    
    // Poll for new notifications every minute
    const interval = setInterval(loadNotifications, 60000)
    return () => clearInterval(interval)
  }, [userId])

  const loadNotifications = async () => {
    const unreadNotifications = await matchNotificationSystem.getUnreadNotifications(userId)
    setNotifications(unreadNotifications)
  }

  const handleDismiss = async (notificationId: string) => {
    await matchNotificationSystem.markNotificationAsRead(notificationId)
    setNotifications(prev => prev.filter(n => n.id !== notificationId))
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'pre_match': return <Clock className="w-5 h-5 text-blue-500" />
      case 'match_start': return <Play className="w-5 h-5 text-green-500" />
      case 'half_time': return <Timer className="w-5 h-5 text-orange-500" />
      case 'match_end': return <CheckCircle className="w-5 h-5 text-purple-500" />
      default: return <AlertTriangle className="w-5 h-5 text-gray-500" />
    }
  }

  return (
    <div className="relative">
      {/* Notification Bell */}
      <button
        onClick={() => setShowNotifications(!showNotifications)}
        className="relative p-2 rounded-full hover:bg-gray-100 transition-colors"
      >
        <Bell className="w-5 h-5" />
        {notifications.length > 0 && (
          <span className="absolute top-0 right-0 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
            {notifications.length}
          </span>
        )}
      </button>

      {/* Notification Panel */}
      <AnimatePresence>
        {showNotifications && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-80 max-h-96 overflow-y-auto bg-white rounded-lg shadow-lg z-50"
          >
            <div className="p-3 border-b flex items-center justify-between">
              <h3 className="font-medium">Match Notifications</h3>
              <button 
                onClick={() => setShowNotifications(false)}
                className="p-1 rounded-full hover:bg-gray-100"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            <div className="divide-y">
              {notifications.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <Bell className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                  <p>No new notifications</p>
                </div>
              ) : (
                notifications.map(notification => (
                  <motion.div
                    key={notification.id}
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="p-3 hover:bg-gray-50"
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.notification_type)}
                      </div>
                      <div className="flex-1">
                        <p className="text-sm">{notification.message}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(notification.scheduled_time).toLocaleTimeString()}
                        </p>
                      </div>
                      <button
                        onClick={() => handleDismiss(notification.id)}
                        className="p-1 rounded-full hover:bg-gray-200"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                    <div className="mt-2 flex space-x-2">
                      <Button size="sm" variant="outline" className="w-full" onClick={() => handleDismiss(notification.id)}>
                        Dismiss
                      </Button>
                      <Button size="sm" className="w-full">
                        {notification.notification_type === 'pre_match' ? 'Prepare' :
                         notification.notification_type === 'match_start' ? 'Watch Live' :
                         notification.notification_type === 'half_time' ? 'Adjust Tactics' : 'View Result'}
                      </Button>
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Live Match Notification Banner
export function LiveMatchNotification({ 
  matchId, 
  homeTeam, 
  awayTeam, 
  onDismiss, 
  onWatch 
}: { 
  matchId: string; 
  homeTeam: string; 
  awayTeam: string; 
  onDismiss: () => void; 
  onWatch: () => void; 
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -50 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -50 }}
      className="fixed top-0 left-0 right-0 z-50 p-2 bg-gradient-to-r from-blue-600 to-purple-600"
    >
      <Card className="mx-auto max-w-3xl">
        <CardContent className="p-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-full">
              <Play className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <p className="font-medium">Live Match in Progress!</p>
              <p className="text-sm text-gray-500">{homeTeam} vs {awayTeam}</p>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button size="sm" variant="outline" onClick={onDismiss}>
              Dismiss
            </Button>
            <Button size="sm" onClick={onWatch}>
              Watch Live
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

// Match Reminder Component
export function MatchReminder({
  matchId,
  homeTeam,
  awayTeam,
  matchTime,
  minutesUntil,
  onDismiss,
  onPrepare
}: {
  matchId: string;
  homeTeam: string;
  awayTeam: string;
  matchTime: string;
  minutesUntil: number;
  onDismiss: () => void;
  onPrepare: () => void;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed bottom-4 right-4 z-50 max-w-sm"
    >
      <Card className="border-l-4 border-l-blue-500">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <div className="p-2 bg-blue-100 rounded-full">
              <Clock className="w-5 h-5 text-blue-600" />
            </div>
            <div className="flex-1">
              <p className="font-medium">Upcoming Match</p>
              <p className="text-sm">{homeTeam} vs {awayTeam}</p>
              <p className="text-xs text-gray-500 mt-1">
                Starting in {minutesUntil} minutes
              </p>
            </div>
            <button
              onClick={onDismiss}
              className="p-1 rounded-full hover:bg-gray-200"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          <div className="mt-3">
            <Button size="sm" className="w-full" onClick={onPrepare}>
              Prepare for Match
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
