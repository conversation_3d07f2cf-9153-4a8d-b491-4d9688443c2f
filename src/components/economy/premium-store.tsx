'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { User } from '@supabase/supabase-js'
import { UserCurrency } from '@/lib/types'
import { economyService, SpendingOption } from '@/lib/economy-service'
import { dailyRewardsService } from '@/lib/daily-rewards'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import {
  ShoppingCart,
  Coins,
  Star,
  Zap,
  Heart,
  Shield,
  TrendingUp,
  Users,
  Clock,
  Gift,
  CheckCircle,
  X
} from 'lucide-react'

interface PremiumStoreProps {
  user: User
  onPurchase?: (option: SpendingOption) => void
}

export default function PremiumStore({ user, onPurchase }: PremiumStoreProps) {
  const [spendingOptions, setSpendingOptions] = useState<SpendingOption[]>([])
  const [userCurrency, setUserCurrency] = useState<UserCurrency | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [purchasing, setPurchasing] = useState<string | null>(null)
  const [showConfirmation, setShowConfirmation] = useState<SpendingOption | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadStoreData()
  }, [user.id])

  const loadStoreData = async () => {
    setIsLoading(true)
    try {
      const [options, currency] = await Promise.all([
        economyService.getSpendingOptions(user.id),
        dailyRewardsService.getUserCurrency(user.id)
      ])

      setSpendingOptions(options)
      setUserCurrency(currency)
    } catch (error) {
      console.error('Error loading store data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handlePurchase = async (option: SpendingOption) => {
    if (purchasing) return

    setPurchasing(option.id)
    try {
      const result = await economyService.purchaseOption(user.id, option.id)
      
      if (result.success) {
        // Reload data to reflect changes
        await loadStoreData()
        onPurchase?.(option)
        setShowConfirmation(null)
      } else {
        alert(result.error || 'Purchase failed')
      }
    } catch (error) {
      console.error('Error purchasing option:', error)
      alert('Purchase failed')
    } finally {
      setPurchasing(null)
    }
  }

  const getOptionIcon = (optionId: string) => {
    const icons: Record<string, any> = {
      xp_boost: Zap,
      recovery_boost: Heart,
      injury_heal: Shield,
      premium_scout: Users,
      extra_training_slot: TrendingUp,
      instant_facility_upgrade: Clock,
      coin_package_small: Coins,
      coin_package_large: Coins
    }
    return icons[optionId] || Gift
  }

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, any> = {
      boosters: Zap,
      premium: Star,
      training: TrendingUp,
      facilities: Shield,
      transfers: Users
    }
    return icons[category] || ShoppingCart
  }

  const categories = [
    { id: 'all', name: 'All Items', icon: ShoppingCart },
    { id: 'boosters', name: 'Boosters', icon: Zap },
    { id: 'premium', name: 'Premium', icon: Star },
    { id: 'training', name: 'Training', icon: TrendingUp },
    { id: 'facilities', name: 'Facilities', icon: Shield }
  ]

  const filteredOptions = selectedCategory === 'all' 
    ? spendingOptions 
    : spendingOptions.filter(option => option.category === selectedCategory)

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p>Loading store...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Currency Display */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center space-x-8">
            <div className="flex items-center space-x-2">
              <Coins className="w-6 h-6 text-yellow-500" />
              <div>
                <div className="text-2xl font-bold">{userCurrency?.coins?.toLocaleString() || '0'}</div>
                <div className="text-sm text-muted">Coins</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Star className="w-6 h-6 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">{userCurrency?.tokens || 0}</div>
                <div className="text-sm text-muted">Tokens</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Category Filter */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-2">
            {categories.map(category => {
              const Icon = category.icon
              return (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category.id)}
                  className="flex items-center space-x-2"
                >
                  <Icon className="w-4 h-4" />
                  <span>{category.name}</span>
                </Button>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Store Items */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredOptions.map((option, index) => {
          const Icon = getOptionIcon(option.id)
          
          return (
            <motion.div
              key={option.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={`hover:shadow-lg transition-all h-full ${
                !option.available ? 'opacity-60' : ''
              }`}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Icon className="w-5 h-5 text-primary" />
                      <span className="text-lg">{option.name}</span>
                    </div>
                    <div className="text-xs px-2 py-1 bg-primary/10 text-primary rounded-full capitalize">
                      {option.category}
                    </div>
                  </CardTitle>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted">{option.description}</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      {option.cost.coins && (
                        <div className="flex items-center space-x-1">
                          <Coins className="w-4 h-4 text-yellow-500" />
                          <span className="font-medium">{option.cost.coins.toLocaleString()}</span>
                        </div>
                      )}
                      {option.cost.tokens && (
                        <div className="flex items-center space-x-1">
                          <Star className="w-4 h-4 text-purple-500" />
                          <span className="font-medium">{option.cost.tokens}</span>
                        </div>
                      )}
                    </div>
                    
                    <Button
                      onClick={() => setShowConfirmation(option)}
                      disabled={!option.available || purchasing === option.id}
                      size="sm"
                      variant={option.available ? "default" : "outline"}
                    >
                      {purchasing === option.id ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Buying...</span>
                        </div>
                      ) : option.available ? (
                        'Buy Now'
                      ) : (
                        'Insufficient Funds'
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </div>

      {filteredOptions.length === 0 && (
        <Card>
          <CardContent className="pt-6 text-center py-12">
            <ShoppingCart className="w-12 h-12 mx-auto text-muted mb-4" />
            <h3 className="text-lg font-medium mb-2">No Items Available</h3>
            <p className="text-muted">
              No items found in this category. Check back later for new offers!
            </p>
          </CardContent>
        </Card>
      )}

      {/* Purchase Confirmation Modal */}
      <AnimatePresence>
        {showConfirmation && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowConfirmation(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <Card className="border-0 shadow-none">
                <CardHeader className="relative">
                  <button
                    onClick={() => setShowConfirmation(null)}
                    className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
                  >
                    <X className="w-5 h-5" />
                  </button>
                  
                  <CardTitle className="text-center">Confirm Purchase</CardTitle>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-lg font-medium">{showConfirmation.name}</div>
                    <div className="text-sm text-muted mt-1">{showConfirmation.description}</div>
                  </div>
                  
                  <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
                    <div className="text-center space-y-2">
                      <div className="text-sm text-muted">Cost:</div>
                      {showConfirmation.cost.coins && (
                        <div className="flex items-center justify-center space-x-1">
                          <Coins className="w-4 h-4 text-yellow-500" />
                          <span className="font-medium">{showConfirmation.cost.coins.toLocaleString()} Coins</span>
                        </div>
                      )}
                      {showConfirmation.cost.tokens && (
                        <div className="flex items-center justify-center space-x-1">
                          <Star className="w-4 h-4 text-purple-500" />
                          <span className="font-medium">{showConfirmation.cost.tokens} Tokens</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => setShowConfirmation(null)}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={() => handlePurchase(showConfirmation)}
                      disabled={purchasing === showConfirmation.id}
                      className="flex-1"
                    >
                      {purchasing === showConfirmation.id ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          <span>Purchasing...</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4" />
                          <span>Confirm Purchase</span>
                        </div>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
