'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { User } from '@supabase/supabase-js'
import { Profile, TransferListing } from '@/lib/types'
import { useAuth } from '@/lib/auth-context'
import { useCashBalance } from '@/hooks/use-cash-balance'
import { unifiedCurrencyService } from '@/lib/unified-currency-service'
import { gameService } from '@/lib/game-services'
import TransferMarket from '@/components/game/transfer-market'
import { Card, CardContent, CardHeader, CardTitle, StatCard } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { LoadingCard } from '@/components/ui/loading-states'
import { NoTransfersEmpty, EmptyCard } from '@/components/ui/empty-states'
import { ArrowLeft, Users, DollarSign, TrendingUp, Plus, Search, Filter, Target } from 'lucide-react'
import Link from 'next/link'
import { useCurrency } from '@/lib/currency-context'
import { useRating } from '@/lib/rating-context'

interface TransfersPageContentProps {
  user: User
  profile: Profile | null
}

export default function TransfersPageContent({ user, profile }: TransfersPageContentProps) {
  const { refreshCashBalance } = useCashBalance()
  const { formatBudgetStat } = useCurrency()
  const { refreshRatings } = useRating()
  const [isLoading, setIsLoading] = useState(true)
  const [listings, setListings] = useState<TransferListing[]>([])


  useEffect(() => {
    loadTransferMarket()
  }, [])

  const loadTransferMarket = async () => {
    try {
      setIsLoading(true)
      const marketListings = await gameService.generateTransferMarket()
      setListings(marketListings)
    } catch (error) {
      console.error('Error loading transfer market:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handlePlaceBid = async (listing: TransferListing, bidAmount: number) => {
    try {
      // In a real implementation, this would create a bid in the database
      console.log(`Placed bid of $${bidAmount} for ${listing.player?.name}`)
      
      // For demo purposes, simulate successful bid
      alert(`Bid placed successfully for $${bidAmount.toLocaleString()}!`)
    } catch (error) {
      console.error('Error placing bid:', error)
      alert('Failed to place bid. Please try again.')
    }
  }

  const handleBuyNow = async (listing: TransferListing) => {
    if (!listing.player || !profile?.team) return

    try {
      // Check if user has sufficient funds using unified currency
      const hasFunds = await unifiedCurrencyService.hasSufficientCoins(user.id, listing.asking_price)
      if (!hasFunds) {
        alert('Insufficient funds!')
        return
      }

      // Spend coins for the transfer
      const spendResult = await unifiedCurrencyService.spendCoins(
        user.id,
        listing.asking_price,
        `Player transfer: ${listing.player.name}`,
        'transfer'
      )

      if (!spendResult.success) {
        alert(`Transfer failed: ${spendResult.error}`)
        return
      }

      // In a real implementation, this would transfer the player and update the database
      console.log(`Bought ${listing.player.name} for $${listing.asking_price}`)

      // Update local state
      setListings(prev => prev.filter(l => l.id !== listing.id))

      // Refresh the global profile to update cash balance across all components
      await refreshCashBalance()

      alert(`Successfully signed ${listing.player.name}!`)
    } catch (error) {
      console.error('Error buying player:', error)
      alert('Failed to complete transfer. Please try again.')
    }
  }

  if (!profile?.team) {
    return (
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <Card>
            <CardContent className="pt-6 text-center py-12">
              <Users className="w-12 h-12 mx-auto text-muted mb-4" />
              <h3 className="text-lg font-medium mb-2">No Team Found</h3>
              <p className="text-muted mb-4">
                You need to create a team before you can access the transfer market.
              </p>
              <Link href="/team-builder">
                <Button>Create Your Team</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-muted-bg via-background to-muted-bg/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8"
        >
          {/* Page Header */}
          <motion.div variants={itemVariants} className="text-center mb-8">
            <div className="bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 rounded-2xl p-8 border border-primary/20">
              <motion.h1
                className="text-3xl md:text-4xl font-bold text-foreground mb-2 flex items-center justify-center gap-3"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <Users className="w-8 h-8 text-primary" />
                Transfer Market
              </motion.h1>
              <motion.p
                className="text-muted text-lg"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                Scout and sign new players for your team
              </motion.p>
            </div>
          </motion.div>

          {/* Transfer Stats */}
          <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatCard
              title="Available Players"
              value={listings.length}
              icon={<Users className="w-6 h-6" />}
              color="blue"
            />
            <StatCard
              title="Squad Size"
              value={`${profile.team.players?.length || 0}/14`}
              icon={<TrendingUp className="w-6 h-6" />}
              color="green"
            />
            <StatCard
              title="Budget"
              value={formatBudgetStat()}
              icon={<DollarSign className="w-6 h-6" />}
              color="purple"
            />
            <StatCard
              title="Free Agents"
              value={listings.filter(l => l.transfer_type === 'free').length}
              icon={<Plus className="w-6 h-6" />}
              color="yellow"
            />
          </motion.div>

          {/* Transfer Market Component */}
          <motion.div variants={itemVariants}>
            {isLoading ? (
              <LoadingCard title="Loading transfer market..." description="Finding the best players for your team..." />
            ) : listings.length > 0 ? (
              <Card variant="elevated" hover>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Search className="w-5 h-5" />
                      Transfer Market
                    </span>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Filter className="w-4 h-4 mr-2" />
                        Filter
                      </Button>
                      <Button variant="outline" size="sm" onClick={loadTransferMarket}>
                        <TrendingUp className="w-4 h-4 mr-2" />
                        Refresh
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <TransferMarket
                    listings={listings}
                    userTeam={profile.team}
                    userCash={profile?.userCurrency?.coins || 0}
                    onPlaceBid={handlePlaceBid}
                    onBuyNow={handleBuyNow}
                  />
                </CardContent>
              </Card>
            ) : (
              <NoTransfersEmpty onRefresh={loadTransferMarket} />
            )}
          </motion.div>

          {/* Current Squad Overview */}
          <motion.div variants={itemVariants}>
            <Card variant="elevated" hover>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Current Squad
                  </span>
                  <Link href="/team-builder">
                    <Button variant="outline" size="sm" className="hover:bg-primary hover:text-white transition-colors duration-200">
                      <Users className="w-4 h-4 mr-2" />
                      Manage Squad
                    </Button>
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {profile.team.players && profile.team.players.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <AnimatePresence>
                      {profile.team.players.map((player, index) => (
                        <motion.div
                          key={player.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.9 }}
                          transition={{ delay: index * 0.05 }}
                          whileHover={{ scale: 1.02 }}
                          className="p-4 border border-card-border rounded-xl bg-card-bg hover:bg-hover-bg hover:border-primary/30 transition-all duration-200 cursor-pointer"
                        >
                          <div className="flex items-center justify-between mb-3">
                            <div className="font-semibold text-foreground">{player.name}</div>
                            <div className="text-sm text-muted capitalize font-medium">
                              {player.position.replace('-', ' ')}
                            </div>
                          </div>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-muted">Age:</span>
                              <span className="font-medium">{player.age}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted">Value:</span>
                              <span className="font-medium text-green-600">${player.market_value.toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted">Salary:</span>
                              <span className="font-medium">${player.contract_salary.toLocaleString()}/mo</span>
                            </div>
                            <div className="flex justify-between items-center pt-1 border-t border-card-border/50">
                              <span className="text-muted">Overall:</span>
                              <div className="flex items-center gap-2">
                                <div className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden">
                                  <div
                                    className="h-full bg-gradient-to-r from-primary to-secondary transition-all duration-300"
                                    style={{ width: `${Math.round((player.shooting + player.speed + player.passing + player.defense) / 4)}%` }}
                                  />
                                </div>
                                <span className="font-bold text-primary">
                                  {Math.round((player.shooting + player.speed + player.passing + player.defense) / 4)}
                                </span>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </AnimatePresence>
                  </div>
                ) : (
                  <EmptyCard
                    icon={<Users className="w-12 h-12" />}
                    title="No Players in Squad"
                    description="Sign some players from the transfer market to build your team!"
                    action={{
                      label: "Browse Market",
                      onClick: loadTransferMarket,
                      variant: "outline"
                    }}
                  />
                )}
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
