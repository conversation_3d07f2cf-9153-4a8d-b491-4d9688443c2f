'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  TeamAnalytics, 
  PlayerAnalytics, 
  WeeklyWrapUp,
  leagueAnalytics 
} from '@/lib/league-analytics'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import But<PERSON> from '@/components/ui/button'
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  BarChart,
  Award,
  Users,
  Star,
  Shield,
  Trophy,
  Calendar,
  Activity,
  Zap,
  Target,
  ArrowUp,
  ArrowDown,
  Flame
} from 'lucide-react'

interface LeagueAnalyticsDashboardProps {
  teamId: string
  leagueId: string
}

export default function LeagueAnalyticsDashboard({ teamId, leagueId }: LeagueAnalyticsDashboardProps) {
  const [teamStats, setTeamStats] = useState<TeamAnalytics | null>(null)
  const [weeklyWrapUp, setWeeklyWrapUp] = useState<WeeklyWrapUp | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadAnalytics()
  }, [teamId, leagueId])

  const loadAnalytics = async () => {
    try {
      setIsLoading(true)

      // Create mock analytics data for the simplified system
      console.log('Loading simplified analytics for team:', teamId, 'league:', leagueId)

      // Mock team analytics
      const mockTeamStats: TeamAnalytics = {
        teamId: teamId,
        leagueId: leagueId,
        currentPosition: 1,
        positionChange: 0,
        points: 0,
        matchesPlayed: 0,
        wins: 0,
        draws: 0,
        losses: 0,
        goalsFor: 0,
        goalsAgainst: 0,
        goalDifference: 0,
        form: [],
        averageRating: 50,
        ratingChange: 0,
        strengthAreas: ['Team Chemistry', 'Training Dedication'],
        weaknessAreas: ['Match Experience', 'League Competition'],
        upcomingFixtures: [],
        recentResults: [],
        playerContributions: [],
        tacticalInsights: {
          preferredFormation: '4-3-3',
          strongestPhase: 'Midfield',
          weakestPhase: 'Defense',
          keyTactics: ['Possession Play', 'Quick Transitions']
        }
      }

      // Mock weekly wrap-up
      const mockWeeklyWrapUp: WeeklyWrapUp = {
        leagueId: leagueId,
        weekNumber: 1,
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        totalMatches: 0,
        totalGoals: 0,
        averageGoalsPerMatch: 0,
        topScorers: [],
        teamOfTheWeek: [],
        biggestWin: null,
        upsetOfTheWeek: null,
        standingsMovement: [],
        weeklyHighlights: [
          'Season has just begun!',
          'Teams are preparing for their first matches',
          'Exciting league action coming soon'
        ],
        nextWeekPreview: [
          'First round of matches scheduled',
          'Teams eager to start their league campaigns'
        ]
      }

      setTeamStats(mockTeamStats)
      setWeeklyWrapUp(mockWeeklyWrapUp)
    } catch (error) {
      console.error('Error loading analytics:', error)
      // Set minimal fallback data to prevent crashes
      setTeamStats(null)
      setWeeklyWrapUp(null)
    } finally {
      setIsLoading(false)
    }
  }

  const getTrendIcon = (trend: 'improving' | 'declining' | 'stable') => {
    switch (trend) {
      case 'improving': return <TrendingUp className="w-4 h-4 text-green-500" />
      case 'declining': return <TrendingDown className="w-4 h-4 text-red-500" />
      default: return <Minus className="w-4 h-4 text-gray-500" />
    }
  }

  const getFormBadge = (result: string) => {
    switch (result) {
      case 'W': return <span className="w-6 h-6 rounded-full bg-green-500 text-white flex items-center justify-center text-xs font-bold">W</span>
      case 'D': return <span className="w-6 h-6 rounded-full bg-yellow-500 text-white flex items-center justify-center text-xs font-bold">D</span>
      case 'L': return <span className="w-6 h-6 rounded-full bg-red-500 text-white flex items-center justify-center text-xs font-bold">L</span>
      default: return null
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="animate-pulse space-y-4">
              <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              <div className="space-y-2">
                <div className="h-16 bg-gray-200 rounded"></div>
                <div className="h-16 bg-gray-200 rounded"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show fallback message if no analytics data
  if (!teamStats && !weeklyWrapUp) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
                <BarChart className="w-8 h-8 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Analytics Coming Soon</h3>
                <p className="text-muted-foreground">
                  League analytics will be available once you start playing matches.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Team Performance Overview */}
      {teamStats && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart className="w-5 h-5" />
                <span>Team Performance</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="space-y-2">
                  <div className="text-sm text-muted">Form</div>
                  <div className="flex space-x-1">
                    {teamStats.form.map((result, index) => (
                      <div key={index}>
                        {getFormBadge(result)}
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center space-x-1 text-sm">
                    {getTrendIcon(teamStats.performance_trend)}
                    <span className="capitalize">{teamStats.performance_trend}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm text-muted">Record</div>
                  <div className="text-xl font-bold">
                    {teamStats.wins}W - {teamStats.draws}D - {teamStats.losses}L
                  </div>
                  <div className="text-sm text-muted">
                    {teamStats.win_percentage.toFixed(1)}% Win Rate
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm text-muted">Goals</div>
                  <div className="text-xl font-bold">
                    {teamStats.goals_scored} - {teamStats.goals_conceded}
                  </div>
                  <div className="text-sm text-muted">
                    {teamStats.goal_difference >= 0 ? '+' : ''}{teamStats.goal_difference} Difference
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm text-muted">Power Ranking</div>
                  <div className="text-xl font-bold">
                    {teamStats.power_ranking}/100
                  </div>
                  <div className="flex items-center space-x-1 text-sm">
                    {teamStats.power_ranking_change > 0 ? (
                      <ArrowUp className="w-4 h-4 text-green-500" />
                    ) : teamStats.power_ranking_change < 0 ? (
                      <ArrowDown className="w-4 h-4 text-red-500" />
                    ) : (
                      <Minus className="w-4 h-4 text-gray-500" />
                    )}
                    <span>{Math.abs(teamStats.power_ranking_change)} points</span>
                  </div>
                </div>
              </div>
              
              {/* Performance Metrics */}
              <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm text-muted">Scoring Rate</div>
                        <div className="text-xl font-bold">{teamStats.scoring_rate.toFixed(1)}</div>
                        <div className="text-xs text-muted">Goals per match</div>
                      </div>
                      <div className="p-3 bg-blue-100 rounded-full">
                        <Target className="w-5 h-5 text-blue-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm text-muted">Defense</div>
                        <div className="text-xl font-bold">{teamStats.conceding_rate.toFixed(1)}</div>
                        <div className="text-xs text-muted">Goals conceded per match</div>
                      </div>
                      <div className="p-3 bg-red-100 rounded-full">
                        <Shield className="w-5 h-5 text-red-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-sm text-muted">Clean Sheets</div>
                        <div className="text-xl font-bold">{teamStats.clean_sheets}</div>
                        <div className="text-xs text-muted">
                          {teamStats.matches_played > 0 
                            ? ((teamStats.clean_sheets / teamStats.matches_played) * 100).toFixed(1) + '%' 
                            : '0%'} of matches
                        </div>
                      </div>
                      <div className="p-3 bg-green-100 rounded-full">
                        <Award className="w-5 h-5 text-green-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Weekly Wrap-Up */}
      {weeklyWrapUp && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="w-5 h-5" />
                <span>Weekly Wrap-Up</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Top Performers */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Top Performers</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Top Scorers */}
                    <div>
                      <h4 className="text-sm font-medium flex items-center space-x-2 mb-2">
                        <Target className="w-4 h-4" />
                        <span>Top Scorers</span>
                      </h4>
                      <div className="space-y-2">
                        {weeklyWrapUp.top_scorers.slice(0, 3).map((scorer, index) => (
                          <div key={scorer.player_id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div className="flex items-center space-x-2">
                              <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center text-xs font-bold text-blue-600">
                                {index + 1}
                              </div>
                              <span>{scorer.player_name}</span>
                              <span className="text-xs text-muted">({scorer.team_name})</span>
                            </div>
                            <div className="font-bold">{scorer.goals} goals</div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    {/* Best Goalkeepers */}
                    <div>
                      <h4 className="text-sm font-medium flex items-center space-x-2 mb-2">
                        <Shield className="w-4 h-4" />
                        <span>Best Goalkeepers</span>
                      </h4>
                      <div className="space-y-2">
                        {weeklyWrapUp.best_goalkeepers.slice(0, 3).map((gk, index) => (
                          <div key={gk.player_id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div className="flex items-center space-x-2">
                              <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center text-xs font-bold text-green-600">
                                {index + 1}
                              </div>
                              <span>{gk.player_name}</span>
                              <span className="text-xs text-muted">({gk.team_name})</span>
                            </div>
                            <div className="font-bold">{gk.saves} saves</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Team of the Week */}
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center space-x-2">
                    <Star className="w-5 h-5 text-yellow-500" />
                    <span>Team of the Week</span>
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
                    {weeklyWrapUp.team_of_the_week.map(player => (
                      <div key={player.player_id} className="p-3 border rounded-lg text-center">
                        <div className="text-xs font-bold bg-gray-100 rounded-full py-1 mb-2">
                          {player.position}
                        </div>
                        <div className="font-medium text-sm truncate">{player.player_name}</div>
                        <div className="text-xs text-muted truncate">{player.team_name}</div>
                        <div className="mt-1 text-sm font-bold text-yellow-500">{player.rating.toFixed(1)}</div>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Power Rankings */}
                <div>
                  <h3 className="text-lg font-semibold mb-3 flex items-center space-x-2">
                    <Activity className="w-5 h-5 text-purple-500" />
                    <span>Power Rankings</span>
                  </h3>
                  <div className="space-y-2">
                    {weeklyWrapUp.power_rankings.map(team => (
                      <div key={team.team_id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center text-xs font-bold">
                            {team.ranking}
                          </div>
                          <span className="font-medium">{team.team_name}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {team.change > 0 ? (
                            <span className="text-green-500 flex items-center">
                              <ArrowUp className="w-4 h-4 mr-1" />
                              {team.change}
                            </span>
                          ) : team.change < 0 ? (
                            <span className="text-red-500 flex items-center">
                              <ArrowDown className="w-4 h-4 mr-1" />
                              {Math.abs(team.change)}
                            </span>
                          ) : (
                            <span className="text-gray-500 flex items-center">
                              <Minus className="w-4 h-4 mr-1" />
                              0
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
