'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { LeagueStanding, enhancedLeagueSystem } from '@/lib/league-system'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Trophy,
  TrendingUp,
  TrendingDown,
  Minus,
  Target,
  Users,
  Calendar,
  Award,
  ArrowUp,
  ArrowDown,
  Equal
} from 'lucide-react'

interface LeagueStandingsProps {
  leagueId: string
  userTeamId?: string
}

export default function LeagueStandings({ leagueId, userTeamId }: LeagueStandingsProps) {
  const [standings, setStandings] = useState<LeagueStanding[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadStandings()
  }, [leagueId])

  const loadStandings = async () => {
    setIsLoading(true)
    try {
      const data = await enhancedLeagueSystem.getLeagueStandings(leagueId)
      setStandings(data)
    } catch (error) {
      console.error('Error loading standings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getPositionColor = (position: number) => {
    if (position <= 2) return 'text-green-600 bg-green-50 dark:bg-green-900/20'
    if (position <= 6) return 'text-blue-600 bg-blue-50 dark:bg-blue-900/20'
    if (position >= standings.length - 1) return 'text-red-600 bg-red-50 dark:bg-red-900/20'
    return 'text-gray-600 bg-gray-50 dark:bg-gray-900/20'
  }

  const getPositionIcon = (position: number) => {
    if (position <= 2) return <ArrowUp className="w-3 h-3" />
    if (position >= standings.length - 1) return <ArrowDown className="w-3 h-3" />
    return <Equal className="w-3 h-3" />
  }

  const getFormIcon = (result: string) => {
    switch (result) {
      case 'W': return <div className="w-2 h-2 bg-green-500 rounded-full" />
      case 'D': return <div className="w-2 h-2 bg-yellow-500 rounded-full" />
      case 'L': return <div className="w-2 h-2 bg-red-500 rounded-full" />
      default: return <div className="w-2 h-2 bg-gray-300 rounded-full" />
    }
  }

  const getPositionDescription = (position: number) => {
    if (position <= 2) return 'Promotion Zone'
    if (position <= 6) return 'European Competition'
    if (position >= standings.length - 1) return 'Relegation Zone'
    return 'Mid Table'
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4" />
              <p>Loading standings...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Trophy className="w-5 h-5 text-yellow-500" />
          <span>League Standings</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {/* Legend */}
          <div className="flex flex-wrap gap-4 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded" />
              <span>Promotion</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded" />
              <span>European Competition</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded" />
              <span>Relegation</span>
            </div>
          </div>

          {/* Standings Table */}
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 px-1">#</th>
                  <th className="text-left py-2 px-2">Team</th>
                  <th className="text-center py-2 px-1">P</th>
                  <th className="text-center py-2 px-1">W</th>
                  <th className="text-center py-2 px-1">D</th>
                  <th className="text-center py-2 px-1">L</th>
                  <th className="text-center py-2 px-1">GF</th>
                  <th className="text-center py-2 px-1">GA</th>
                  <th className="text-center py-2 px-1">GD</th>
                  <th className="text-center py-2 px-1">Pts</th>
                  <th className="text-center py-2 px-1">Form</th>
                </tr>
              </thead>
              <tbody>
                {standings.map((standing, index) => (
                  <motion.tr
                    key={standing.team.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className={`border-b hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
                      standing.team.id === userTeamId ? 'bg-primary/5 border-primary/20' : ''
                    }`}
                  >
                    {/* Position */}
                    <td className="py-3 px-1">
                      <div className={`flex items-center justify-center w-6 h-6 rounded text-xs font-bold ${getPositionColor(standing.position)}`}>
                        {standing.position}
                      </div>
                    </td>
                    
                    {/* Team */}
                    <td className="py-3 px-2">
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center space-x-1">
                          {getPositionIcon(standing.position)}
                          <span className={`font-medium ${standing.team.id === userTeamId ? 'text-primary' : ''}`}>
                            {standing.team.name}
                          </span>
                        </div>
                        {standing.team.id === userTeamId && (
                          <Badge variant="outline" className="text-xs">You</Badge>
                        )}
                      </div>
                    </td>
                    
                    {/* Stats */}
                    <td className="text-center py-3 px-1">{standing.matches_played}</td>
                    <td className="text-center py-3 px-1 text-green-600">{standing.wins}</td>
                    <td className="text-center py-3 px-1 text-yellow-600">{standing.draws}</td>
                    <td className="text-center py-3 px-1 text-red-600">{standing.losses}</td>
                    <td className="text-center py-3 px-1">{standing.goals_for}</td>
                    <td className="text-center py-3 px-1">{standing.goals_against}</td>
                    <td className={`text-center py-3 px-1 font-medium ${
                      standing.goal_difference > 0 ? 'text-green-600' : 
                      standing.goal_difference < 0 ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {standing.goal_difference > 0 ? '+' : ''}{standing.goal_difference}
                    </td>
                    <td className="text-center py-3 px-1 font-bold">{standing.points}</td>
                    
                    {/* Form */}
                    <td className="py-3 px-1">
                      <div className="flex items-center justify-center space-x-1">
                        {standing.form.slice(0, 5).map((result, i) => (
                          <div key={i} title={result === 'W' ? 'Win' : result === 'D' ? 'Draw' : 'Loss'}>
                            {getFormIcon(result)}
                          </div>
                        ))}
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Position Zones */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <Card className="border-green-200 dark:border-green-800">
              <CardContent className="pt-4">
                <div className="flex items-center space-x-2 text-green-600">
                  <TrendingUp className="w-4 h-4" />
                  <span className="font-medium">Promotion Zone</span>
                </div>
                <div className="text-sm text-muted mt-1">
                  Top 2 teams promoted to higher division
                </div>
              </CardContent>
            </Card>

            <Card className="border-blue-200 dark:border-blue-800">
              <CardContent className="pt-4">
                <div className="flex items-center space-x-2 text-blue-600">
                  <Award className="w-4 h-4" />
                  <span className="font-medium">European Competition</span>
                </div>
                <div className="text-sm text-muted mt-1">
                  Positions 3-6 qualify for cup competitions
                </div>
              </CardContent>
            </Card>

            <Card className="border-red-200 dark:border-red-800">
              <CardContent className="pt-4">
                <div className="flex items-center space-x-2 text-red-600">
                  <TrendingDown className="w-4 h-4" />
                  <span className="font-medium">Relegation Zone</span>
                </div>
                <div className="text-sm text-muted mt-1">
                  Bottom 2 teams relegated to lower division
                </div>
              </CardContent>
            </Card>
          </div>

          {/* User Team Highlight */}
          {userTeamId && standings.find(s => s.team.id === userTeamId) && (
            <Card className="border-primary/20 bg-primary/5">
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Your Team Position</div>
                    <div className="text-sm text-muted">
                      {getPositionDescription(standings.find(s => s.team.id === userTeamId)!.position)}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary">
                      #{standings.find(s => s.team.id === userTeamId)!.position}
                    </div>
                    <div className="text-sm text-muted">
                      {standings.find(s => s.team.id === userTeamId)!.points} points
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
