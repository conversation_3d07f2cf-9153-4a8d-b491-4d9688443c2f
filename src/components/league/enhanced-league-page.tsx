'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { User } from '@supabase/supabase-js'
import { Profile, Team } from '@/lib/types'
import { leagueTierSystem, LeagueTier, Season, League } from '@/lib/league-tier-system'
import { seasonManager, SeasonStats, TeamSeasonPerformance } from '@/lib/season-management'
import { Card, CardContent, CardHeader, CardTitle, StatCard } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { LoadingCard } from '@/components/ui/loading-states'
import { 
  Trophy, 
  Calendar, 
  Clock, 
  Target, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  Crown,
  Medal,
  Award,
  Users,
  Timer,
  Play,
  Pause
} from 'lucide-react'

interface EnhancedLeaguePageProps {
  user: User
  profile: Profile & { team: Team }
}

export default function EnhancedLeaguePage({ user, profile }: EnhancedLeaguePageProps) {
  const [currentLeague, setCurrentLeague] = useState<League | null>(null)
  const [currentTier, setCurrentTier] = useState<LeagueTier | null>(null)
  const [currentSeason, setCurrentSeason] = useState<Season | null>(null)
  const [seasonStats, setSeasonStats] = useState<SeasonStats | null>(null)
  const [teamPerformance, setTeamPerformance] = useState<TeamSeasonPerformance | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadLeagueData()
  }, [profile.team.id])

  const loadLeagueData = async () => {
    try {
      setIsLoading(true)

      if (!profile.team?.league_id) {
        console.log('Team has no league assigned')
        setIsLoading(false)
        return
      }

      // Use basic league data approach (skip advanced system that requires missing tables)
      console.log('Using basic league data approach for compatibility')

      // Get basic league data
      const supabase = (await import('@/lib/supabase-client')).createClient()
      const { data: league } = await supabase
        .from('leagues')
        .select('*')
        .eq('id', profile.team.league_id)
        .single()

      if (league) {
        // Create mock league object
        setCurrentLeague({
          id: league.id,
          name: league.name,
          level: league.level || 1,
          max_teams: league.max_teams || 14,
          season_start_date: league.season_start_date || new Date().toISOString(),
          season_end_date: league.season_end_date || new Date().toISOString(),
          current_matchday: league.current_matchday || 1,
          status: league.status || 'active',
          prize_money: league.prize_money || 3000,
          created_at: league.created_at || new Date().toISOString(),
          updated_at: league.updated_at || new Date().toISOString(),
          teams: []
        })

        // Set mock tier and season data
        setCurrentTier({
          id: 'bronze-5',
          name: 'Bronze',
          level: 1,
          division: 5,
          prize_money_base: 3000,
          promotion_bonus: 1200,
          relegation_penalty: 300,
          tactics_bonus_enabled: false,
          scouting_quality_multiplier: 0.8,
          fanbase_growth_multiplier: 0.8,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

        setCurrentSeason({
          id: 'current-season',
          name: 'Current Season',
          start_date: league.season_start_date || new Date().toISOString(),
          end_date: league.season_end_date || new Date().toISOString(),
          current_matchday: league.current_matchday || 1,
          total_matchdays: 26,
          status: 'active',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })

        setSeasonStats({
          total_teams: league.max_teams || 14,
          total_matches: 0,
          completed_matches: 0,
          current_matchday: league.current_matchday || 1,
          average_goals_per_match: 0
        })

        setTeamPerformance({
          position: 1,
          points: profile.team.league_points || 0,
          matches_played: profile.team.matches_played || 0,
          wins: profile.team.matches_won || 0,
          draws: profile.team.matches_drawn || 0,
          losses: profile.team.matches_lost || 0,
          goals_for: profile.team.goals_for || 0,
          goals_against: profile.team.goals_against || 0,
          form: [],
          recent_results: []
        })
      }
    } catch (error) {
      console.error('Error loading league data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getTierDisplayInfo = (tier: LeagueTier) => {
    return leagueTierSystem.getTierDisplayInfo(tier)
  }

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-500" />
      case 'down': return <TrendingDown className="w-4 h-4 text-red-500" />
      default: return <Minus className="w-4 h-4 text-gray-500" />
    }
  }

  const getPositionColor = (position: number) => {
    if (position <= 3) return 'text-green-600'
    if (position >= 12) return 'text-red-600'
    return 'text-gray-600'
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <LoadingCard title="Loading league information..." description="Fetching your team's league data and season progress..." />
      </div>
    )
  }

  if (!currentLeague || !currentTier || !currentSeason) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
              <Trophy className="w-8 h-8 text-muted-foreground" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">League Assignment in Progress</h3>
              <p className="text-muted-foreground">
                Your team is being assigned to a league. This usually takes a few moments.
              </p>
            </div>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="mt-4"
            >
              Refresh Page
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  const tierInfo = getTierDisplayInfo(currentTier)

  return (
    <div className="space-y-6">
      {/* League Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="relative overflow-hidden rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white"
      >
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <span className="text-2xl">{tierInfo.icon}</span>
                <h1 className="text-2xl font-bold">{tierInfo.displayName}</h1>
              </div>
              <p className="text-blue-100">{tierInfo.description}</p>
              <p className="text-sm text-blue-200 mt-1">{currentSeason.name}</p>
            </div>
            <div className="text-right">
              <div className="text-sm text-blue-200">Your Position</div>
              <div className={`text-3xl font-bold ${getPositionColor(teamPerformance?.position || 0)}`}>
                #{teamPerformance?.position || '-'}
              </div>
            </div>
          </div>
        </div>
        <div className="absolute inset-0 bg-black/20" />
      </motion.div>

      {/* Season Progress */}
      {seasonStats && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Timer className="w-5 h-5" />
                <span>Season Progress</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Progress Bar */}
                <div>
                  <div className="flex justify-between text-sm text-muted mb-2">
                    <span>Matchday {seasonStats.current_matchday} of {seasonStats.total_matchdays}</span>
                    <span>{seasonStats.days_remaining} days remaining</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${seasonStats.progress_percentage}%` }}
                    />
                  </div>
                </div>

                {/* Stats Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{seasonStats.completed_matches}</div>
                    <div className="text-sm text-muted">Matches Played</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{seasonStats.total_matches - seasonStats.completed_matches}</div>
                    <div className="text-sm text-muted">Remaining</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{Math.round(seasonStats.progress_percentage)}%</div>
                    <div className="text-sm text-muted">Complete</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{seasonStats.days_remaining}</div>
                    <div className="text-sm text-muted">Days Left</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Team Performance Stats */}
      {teamPerformance && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-4 gap-6"
        >
          <StatCard
            title="League Position"
            value={`#${teamPerformance.position}`}
            icon={<Trophy className="w-6 h-6" />}
            color="yellow"
            trend={{ 
              value: teamPerformance.trend === 'up' ? 2 : teamPerformance.trend === 'down' ? -2 : 0, 
              isPositive: teamPerformance.trend === 'up' 
            }}
          />
          <StatCard
            title="Points"
            value={teamPerformance.points}
            icon={<Target className="w-6 h-6" />}
            color="green"
          />
          <StatCard
            title="Goal Difference"
            value={teamPerformance.goal_difference >= 0 ? `+${teamPerformance.goal_difference}` : teamPerformance.goal_difference}
            icon={<Award className="w-6 h-6" />}
            color={teamPerformance.goal_difference >= 0 ? 'green' : 'red'}
          />
          <StatCard
            title="Form"
            value={`${teamPerformance.wins}W ${teamPerformance.draws}D ${teamPerformance.losses}L`}
            icon={getTrendIcon(teamPerformance.trend)}
            color="blue"
          />
        </motion.div>
      )}

      {/* Recent Form */}
      {teamPerformance?.form && teamPerformance.form.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5" />
                <span>Recent Form</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted">Last 5 matches:</span>
                <div className="flex space-x-1">
                  {teamPerformance.form.slice(-5).map((result, index) => (
                    <div
                      key={index}
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                        result === 'W' ? 'bg-green-500' :
                        result === 'D' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                    >
                      {result}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-1">
          <Calendar className="w-5 h-5" />
          <span>View Schedule</span>
        </Button>
        <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-1">
          <Trophy className="w-5 h-5" />
          <span>League Table</span>
        </Button>
        <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-1">
          <Users className="w-5 h-5" />
          <span>Team Stats</span>
        </Button>
      </motion.div>

      {/* Tier Information */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Crown className="w-5 h-5" />
              <span>League Information</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Promotion Zone</h4>
                <p className="text-sm text-muted mb-2">Top 3 teams advance to higher division</p>
                <div className="text-lg font-bold text-green-600">
                  ${currentTier.promotion_bonus.toLocaleString()} bonus
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Prize Money</h4>
                <p className="text-sm text-muted mb-2">Base reward for season completion</p>
                <div className="text-lg font-bold text-blue-600">
                  ${currentTier.prize_money_base.toLocaleString()}
                </div>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Relegation Zone</h4>
                <p className="text-sm text-muted mb-2">Bottom 3 teams face relegation</p>
                <div className="text-lg font-bold text-red-600">
                  -${currentTier.relegation_penalty.toLocaleString()} penalty
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
