'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ScheduledMatch, MatchDay } from '@/lib/match-scheduler'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import But<PERSON> from '@/components/ui/button'
import { 
  Calendar, 
  Clock, 
  Play, 
  Eye, 
  MapPin,
  Users,
  Timer,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

interface MatchScheduleProps {
  leagueId: string
  teamId: string
  seasonId: string
}

export default function MatchSchedule({ leagueId, teamId, seasonId }: MatchScheduleProps) {
  const [matchdays, setMatchdays] = useState<MatchDay[]>([])
  const [currentMatchday, setCurrentMatchday] = useState(1)
  const [selectedDate, setSelectedDate] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadMatchSchedule()
  }, [leagueId, seasonId])

  const loadMatchSchedule = async () => {
    try {
      setIsLoading(true)
      // This would fetch the match schedule from the database
      // For now, we'll create mock data
      const mockMatchdays: MatchDay[] = []
      
      for (let i = 1; i <= 26; i++) {
        const date = new Date()
        date.setDate(date.getDate() + i)
        
        mockMatchdays.push({
          matchday: i,
          date: date.toISOString(),
          matches: [
            {
              id: `match-${i}-1`,
              home_team: { id: 'team1', name: 'Team A' } as any,
              away_team: { id: 'team2', name: 'Team B' } as any,
              scheduled_time: date.toISOString(),
              competition: 'league',
              matchday: i,
              status: i <= 5 ? 'completed' : i === 6 ? 'live' : 'scheduled'
            }
          ],
          time_slots: ['00:00', '06:00', '12:00', '18:00']
        })
      }
      
      setMatchdays(mockMatchdays)
    } catch (error) {
      console.error('Error loading match schedule:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getMatchStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200'
      case 'live': return 'bg-red-100 text-red-800 border-red-200'
      case 'scheduled': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getMatchStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <Eye className="w-4 h-4" />
      case 'live': return <Play className="w-4 h-4" />
      case 'scheduled': return <Timer className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const isTeamMatch = (match: ScheduledMatch) => {
    return match.home_team.id === teamId || match.away_team.id === teamId
  }

  const formatMatchTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }

  const formatMatchDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', { 
      weekday: 'short',
      month: 'short', 
      day: 'numeric' 
    })
  }

  const currentMatchdayData = matchdays.find(md => md.matchday === currentMatchday)

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-2">
              <div className="h-16 bg-gray-200 rounded"></div>
              <div className="h-16 bg-gray-200 rounded"></div>
              <div className="h-16 bg-gray-200 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Matchday Navigation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Calendar className="w-5 h-5" />
              <span>Match Schedule</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentMatchday(Math.max(1, currentMatchday - 1))}
                disabled={currentMatchday === 1}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <span className="text-sm font-medium px-3">
                Matchday {currentMatchday}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentMatchday(Math.min(26, currentMatchday + 1))}
                disabled={currentMatchday === 26}
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {currentMatchdayData && (
            <div className="space-y-4">
              <div className="text-sm text-muted">
                {formatMatchDate(currentMatchdayData.date)}
              </div>
              
              {/* Time Slots */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {currentMatchdayData.time_slots.map((timeSlot, index) => {
                  const matchesInSlot = currentMatchdayData.matches.filter((_, i) => 
                    i % currentMatchdayData.time_slots.length === index
                  )
                  
                  return (
                    <div key={timeSlot} className="space-y-2">
                      <div className="text-sm font-medium text-center py-2 bg-gray-50 rounded">
                        {timeSlot}
                      </div>
                      {matchesInSlot.map((match) => (
                        <motion.div
                          key={match.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className={`p-3 rounded-lg border ${
                            isTeamMatch(match) 
                              ? 'border-blue-300 bg-blue-50' 
                              : 'border-gray-200 bg-white'
                          }`}
                        >
                          <div className="space-y-2">
                            {/* Teams */}
                            <div className="text-sm">
                              <div className="flex items-center justify-between">
                                <span className={isTeamMatch(match) && match.home_team.id === teamId ? 'font-bold' : ''}>
                                  {match.home_team.name}
                                </span>
                                <span className="text-xs text-muted">vs</span>
                                <span className={isTeamMatch(match) && match.away_team.id === teamId ? 'font-bold' : ''}>
                                  {match.away_team.name}
                                </span>
                              </div>
                            </div>
                            
                            {/* Status */}
                            <div className="flex items-center justify-between">
                              <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs border ${getMatchStatusColor(match.status)}`}>
                                {getMatchStatusIcon(match.status)}
                                <span className="capitalize">{match.status}</span>
                              </div>
                              <div className="text-xs text-muted">
                                {formatMatchTime(match.scheduled_time)}
                              </div>
                            </div>
                            
                            {/* Action Button */}
                            {isTeamMatch(match) && (
                              <Button 
                                size="sm" 
                                variant={match.status === 'live' ? 'default' : 'outline'}
                                className="w-full"
                              >
                                {match.status === 'live' ? 'Watch Live' : 
                                 match.status === 'completed' ? 'View Result' : 'Set Tactics'}
                              </Button>
                            )}
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )
                })}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upcoming Team Matches */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="w-5 h-5" />
            <span>Your Upcoming Matches</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {matchdays
              .flatMap(md => md.matches)
              .filter(match => isTeamMatch(match) && match.status === 'scheduled')
              .slice(0, 5)
              .map((match) => (
                <motion.div
                  key={match.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-4">
                    <div className="text-center">
                      <div className="text-sm font-medium">
                        {formatMatchDate(match.scheduled_time)}
                      </div>
                      <div className="text-xs text-muted">
                        {formatMatchTime(match.scheduled_time)}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={match.home_team.id === teamId ? 'font-bold' : ''}>
                        {match.home_team.name}
                      </span>
                      <span className="text-muted">vs</span>
                      <span className={match.away_team.id === teamId ? 'font-bold' : ''}>
                        {match.away_team.name}
                      </span>
                    </div>
                    {match.home_team.id !== teamId && match.away_team.id !== teamId && (
                      <MapPin className="w-4 h-4 text-muted" />
                    )}
                  </div>
                  <Button size="sm" variant="outline">
                    Prepare
                  </Button>
                </motion.div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
