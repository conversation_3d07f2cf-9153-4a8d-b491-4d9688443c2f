'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { matchSchedulingService, ScheduledMatch } from '@/lib/match-scheduling-service'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { 
  Calendar, 
  Clock, 
  Play, 
  Eye, 
  MapPin,
  Users,
  Timer,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

interface MatchScheduleProps {
  leagueId: string
  teamId: string
  seasonId: string
}

export default function MatchSchedule({ leagueId, teamId, seasonId }: MatchScheduleProps) {
  const [upcomingMatches, setUpcomingMatches] = useState<ScheduledMatch[]>([])
  const [recentMatches, setRecentMatches] = useState<ScheduledMatch[]>([])
  const [allMatches, setAllMatches] = useState<ScheduledMatch[]>([])
  const [selectedFilter, setSelectedFilter] = useState<'upcoming' | 'recent' | 'all'>('upcoming')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadMatchSchedule()
  }, [leagueId, teamId, seasonId])

  const loadMatchSchedule = async () => {
    try {
      setIsLoading(true)

      // Auto-generate schedules if needed
      await matchSchedulingService.autoGenerateSchedules()

      // Load matches
      const [upcoming, recent, all] = await Promise.all([
        matchSchedulingService.getTeamUpcomingMatches(teamId, 10),
        matchSchedulingService.getTeamRecentMatches(teamId, 10),
        matchSchedulingService.getLeagueMatches(leagueId, seasonId)
      ])

      setUpcomingMatches(upcoming)
      setRecentMatches(recent)
      setAllMatches(all)
    } catch (error) {
      console.error('Error loading match schedule:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSimulateMatch = async (matchId: string) => {
    try {
      const result = await matchSchedulingService.simulateMatch(matchId)
      console.log('Match simulated:', result)
      // Reload matches to show updated results
      await loadMatchSchedule()
    } catch (error) {
      console.error('Error simulating match:', error)
    }
  }

  const getMatchStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200'
      case 'live': return 'bg-red-100 text-red-800 border-red-200'
      case 'scheduled': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getMatchStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <Eye className="w-4 h-4" />
      case 'live': return <Play className="w-4 h-4" />
      case 'scheduled': return <Timer className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const isTeamMatch = (match: ScheduledMatch) => {
    return match.home_team.id === teamId || match.away_team.id === teamId
  }

  const formatMatchTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }

  const formatMatchDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    })
  }

  const getDisplayMatches = () => {
    switch (selectedFilter) {
      case 'upcoming': return upcomingMatches
      case 'recent': return recentMatches
      case 'all': return allMatches
      default: return upcomingMatches
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-2">
              <div className="h-16 bg-gray-200 rounded"></div>
              <div className="h-16 bg-gray-200 rounded"></div>
              <div className="h-16 bg-gray-200 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Filter Navigation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Calendar className="w-5 h-5" />
              <span>Match Schedule</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant={selectedFilter === 'upcoming' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedFilter('upcoming')}
              >
                Upcoming
              </Button>
              <Button
                variant={selectedFilter === 'recent' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedFilter('recent')}
              >
                Recent
              </Button>
              <Button
                variant={selectedFilter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedFilter('all')}
              >
                All Matches
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {getDisplayMatches().length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Matches Found</h3>
                <p className="text-muted-foreground">
                  {selectedFilter === 'upcoming' ? 'No upcoming matches scheduled.' :
                   selectedFilter === 'recent' ? 'No recent matches found.' :
                   'No matches in this league yet.'}
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {getDisplayMatches().map((match, index) => (
                  <motion.div
                    key={match.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-4 rounded-lg border ${
                      isTeamMatch(match)
                        ? 'border-blue-300 bg-blue-50'
                        : 'border-gray-200 bg-white'
                    }`}
                  >
                    <div className="space-y-3">
                      {/* Match Header */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-full text-xs border ${getMatchStatusColor(match.status)}`}>
                            {getMatchStatusIcon(match.status)}
                            <span className="ml-1 capitalize">{match.status}</span>
                          </span>
                          <span className="text-xs text-muted-foreground">
                            Matchday {match.matchday}
                          </span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatMatchDate(match.match_date)} • {formatMatchTime(match.match_date)}
                        </div>
                      </div>

                      {/* Teams */}
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <span className={`text-sm ${isTeamMatch(match) && match.home_team.id === teamId ? 'font-bold text-blue-600' : ''}`}>
                            {match.home_team.name}
                          </span>
                        </div>
                        <div className="flex items-center space-x-3 px-4">
                          {match.status === 'completed' ? (
                            <div className="text-lg font-bold">
                              {match.home_score} - {match.away_score}
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground">vs</div>
                          )}
                        </div>
                        <div className="flex-1 text-right">
                          <span className={`text-sm ${isTeamMatch(match) && match.away_team.id === teamId ? 'font-bold text-blue-600' : ''}`}>
                            {match.away_team.name}
                          </span>
                        </div>
                      </div>

                      {/* Actions */}
                      {isTeamMatch(match) && match.status === 'scheduled' && (
                        <div className="flex justify-center pt-2">
                          <Button
                            size="sm"
                            onClick={() => handleSimulateMatch(match.id)}
                            className="flex items-center space-x-1"
                          >
                            <Play className="w-4 h-4" />
                            <span>Simulate Match</span>
                          </Button>
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
