'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { User } from '@supabase/supabase-js'
import { Profile, League, Team } from '@/lib/types'
import { generateFreeAgent } from '@/lib/transfer-system'
import LeagueTableComponent from '@/components/game/league-table'
import { Card, CardContent, CardHeader, CardTitle, StatCard } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { LoadingCard } from '@/components/ui/loading-states'
import { NoLeagueEmpty, EmptyCard } from '@/components/ui/empty-states'
import { ArrowLeft, Trophy, Calendar, Play, Users, Target, Clock, Award, TrendingUp, Shield } from 'lucide-react'
import Link from 'next/link'

interface LeaguePageContentProps {
  user: User
  profile: Profile | null
}

export default function LeaguePageContent({ user, profile }: LeaguePageContentProps) {
  const [league, setLeague] = useState<League | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    generateMockLeague()
  }, [profile])

  const generateMockLeague = () => {
    if (!profile?.team) {
      setIsLoading(false)
      return
    }

    // Generate mock league with AI teams
    const mockTeams: Team[] = [
      profile.team,
      ...Array.from({ length: 13 }, (_, i) => ({
        id: `ai-team-${i}`,
        user_id: `ai-user-${i}`,
        name: `${['Aqua', 'Wave', 'Tide', 'Storm', 'Current', 'Rapids', 'Splash', 'Flow', 'Surge', 'Reef', 'Deep', 'Blue', 'Ocean'][i]} ${['Sharks', 'Dolphins', 'Waves', 'Thunder', 'Lightning', 'Titans', 'Warriors', 'Eagles', 'Lions', 'Bears', 'Wolves', 'Hawks', 'Falcons'][i]}`,
        logo_url: null,
        formation: 'standard-7v7',
        team_rating: Math.floor(Math.random() * 40) + 40, // 40-80 rating
        league_position: i + 2,
        league_points: Math.floor(Math.random() * 30) + 10,
        matches_played: Math.floor(Math.random() * 10) + 5,
        matches_won: Math.floor(Math.random() * 8),
        matches_drawn: Math.floor(Math.random() * 3),
        matches_lost: Math.floor(Math.random() * 5),
        goals_for: Math.floor(Math.random() * 20) + 10,
        goals_against: Math.floor(Math.random() * 15) + 5,
        cash_balance: Math.floor(Math.random() * 50000) + 25000,
        fan_base: Math.floor(Math.random() * 5000) + 1000,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))
    ]

    // Sort teams by points for realistic league table
    mockTeams.sort((a, b) => b.league_points - a.league_points)
    mockTeams.forEach((team, index) => {
      team.league_position = index + 1
    })

    const mockLeague: League = {
      id: 'mock-league-1',
      name: 'Premier Water Polo League',
      level: 1,
      max_teams: 14,
      season_start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
      season_end_date: new Date(Date.now() + 150 * 24 * 60 * 60 * 1000).toISOString(), // 150 days from now
      current_matchday: 8,
      status: 'active',
      prize_money: 100000,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      teams: mockTeams
    }

    setLeague(mockLeague)
    setIsLoading(false)
  }

  const handlePlayMatch = () => {
    // In a real implementation, this would navigate to match simulation
    alert('Match simulation coming soon! This would start a match against your next opponent.')
  }

  const handleSimulateMatchday = () => {
    if (!league) return
    
    // Simulate a matchday by updating some results
    const updatedLeague = { ...league }
    updatedLeague.current_matchday += 1
    
    // Randomly update some team stats
    updatedLeague.teams?.forEach(team => {
      if (Math.random() < 0.5) { // 50% chance to play a match
        team.matches_played += 1
        if (Math.random() < 0.4) { // 40% chance to win
          team.matches_won += 1
          team.league_points += 3
          team.goals_for += Math.floor(Math.random() * 3) + 1
        } else if (Math.random() < 0.3) { // 30% chance to draw
          team.matches_drawn += 1
          team.league_points += 1
          team.goals_for += Math.floor(Math.random() * 2) + 1
        } else { // 30% chance to lose
          team.matches_lost += 1
          team.goals_against += Math.floor(Math.random() * 3) + 1
        }
      }
    })

    // Re-sort teams by points
    updatedLeague.teams?.sort((a, b) => b.league_points - a.league_points)
    updatedLeague.teams?.forEach((team, index) => {
      team.league_position = index + 1
    })

    setLeague(updatedLeague)
  }

  if (!profile?.team) {
    return (
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <Card>
            <CardContent className="pt-6 text-center py-12">
              <Trophy className="w-12 h-12 mx-auto text-muted mb-4" />
              <h3 className="text-lg font-medium mb-2">No Team Found</h3>
              <p className="text-muted mb-4">
                You need to create a team before you can access the league.
              </p>
              <Link href="/team-builder">
                <Button>Create Your Team</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-muted-bg via-background to-muted-bg/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8"
        >
          {/* Page Header */}
          <motion.div variants={itemVariants} className="text-center mb-8">
            <div className="bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 rounded-2xl p-8 border border-primary/20">
              <motion.h1
                className="text-3xl md:text-4xl font-bold text-foreground mb-2 flex items-center justify-center gap-3"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <Trophy className="w-8 h-8 text-primary" />
                League Competition
              </motion.h1>
              <motion.p
                className="text-muted text-lg mb-6"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                Compete against other teams and climb the rankings
              </motion.p>
              <motion.div
                className="flex flex-col sm:flex-row justify-center gap-4"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
              >
                <Button
                  variant="outline"
                  onClick={handleSimulateMatchday}
                  className="hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 transition-colors duration-200"
                >
                  <Clock className="w-4 h-4 mr-2" />
                  Simulate Matchday
                </Button>
                <Button
                  onClick={handlePlayMatch}
                  className="bg-gradient-to-r from-primary to-secondary hover:from-primary-dark hover:to-secondary-dark"
                >
                  <Play className="w-4 h-4 mr-2" />
                  Play Next Match
                </Button>
              </motion.div>
            </div>
          </motion.div>

          {/* League Stats */}
          <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatCard
              title="League Position"
              value={`#${profile.team.league_position}`}
              icon={<Trophy className="w-6 h-6" />}
              color="yellow"
              trend={profile.team.league_position <= 3 ? { value: 5, isPositive: true } : undefined}
            />
            <StatCard
              title="Points"
              value={profile.team.league_points}
              icon={<Target className="w-6 h-6" />}
              color="green"
            />
            <StatCard
              title="Matches Played"
              value={profile.team.matches_played || 0}
              icon={<Play className="w-6 h-6" />}
              color="blue"
            />
            <StatCard
              title="Goal Difference"
              value={`${profile.team.goals_for - profile.team.goals_against >= 0 ? '+' : ''}${profile.team.goals_for - profile.team.goals_against}`}
              icon={<TrendingUp className="w-6 h-6" />}
              color="purple"
            />
          </motion.div>

          {/* League Table */}
          <motion.div variants={itemVariants}>
            {isLoading ? (
              <LoadingCard title="Loading league table..." description="Fetching the latest standings and match results..." />
            ) : league ? (
              <Card variant="elevated" hover>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Trophy className="w-5 h-5" />
                      League Standings
                    </span>
                    <div className="flex items-center gap-2 text-sm text-muted">
                      <Calendar className="w-4 h-4" />
                      Season 2024/25
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <LeagueTableComponent
                    league={league}
                    userTeamId={profile.team.id}
                    showPromotionRelegation={true}
                  />
                </CardContent>
              </Card>
            ) : (
              <EmptyCard
                icon={<Trophy className="w-12 h-12" />}
                title="No League Data"
                description="Unable to load league information. Please try again later."
                action={{
                  label: "Retry",
                  onClick: () => window.location.reload(),
                  variant: "outline"
                }}
              />
            )}
          </motion.div>

          {/* Next Fixtures */}
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Fixtures</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Calendar className="w-12 h-12 mx-auto text-muted mb-4" />
                <p className="text-muted">No upcoming fixtures scheduled</p>
                <p className="text-sm text-muted mt-2">
                  Check back later for your next match!
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
