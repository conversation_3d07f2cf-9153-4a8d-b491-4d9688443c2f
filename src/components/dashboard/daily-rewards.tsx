'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { User } from '@supabase/supabase-js'
import { dailyRewardsService, DAILY_REWARDS, DailyReward } from '@/lib/daily-rewards'
import { UserCurrency } from '@/lib/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import {
  Gift,
  Coins,
  Zap,
  Calendar,
  Star,
  Trophy,
  X,
  CheckCircle
} from 'lucide-react'

interface DailyRewardsProps {
  user: User
  onCurrencyUpdate?: (currency: UserCurrency) => void
}

export default function DailyRewards({ user, onCurrencyUpdate }: DailyRewardsProps) {
  const [showModal, setShowModal] = useState(false)
  const [loginStatus, setLoginStatus] = useState<{
    canClaim: boolean
    streak: number
    reward?: DailyReward
    lastLogin?: string
  } | null>(null)
  const [claiming, setClaiming] = useState(false)
  const [claimed, setClaimed] = useState(false)

  useEffect(() => {
    checkDailyLogin()
  }, [user.id])

  const checkDailyLogin = async () => {
    try {
      const status = await dailyRewardsService.checkDailyLogin(user.id)
      setLoginStatus(status)
      
      // Auto-show modal if can claim
      if (status.canClaim) {
        setShowModal(true)
      }
    } catch (error) {
      console.error('Error checking daily login:', error)
    }
  }

  const claimReward = async () => {
    if (!loginStatus?.canClaim) return

    setClaiming(true)
    try {
      const result = await dailyRewardsService.claimDailyReward(user.id)
      
      if (result.success) {
        setClaimed(true)
        setLoginStatus(prev => prev ? { ...prev, canClaim: false } : null)
        
        // Update currency in parent component
        const currency = await dailyRewardsService.getUserCurrency(user.id)
        if (currency && onCurrencyUpdate) {
          onCurrencyUpdate(currency)
        }

        // Auto-close modal after 2 seconds
        setTimeout(() => {
          setShowModal(false)
          setClaimed(false)
        }, 2000)
      }
    } catch (error) {
      console.error('Error claiming reward:', error)
    } finally {
      setClaiming(false)
    }
  }

  const getStreakIcon = (streak: number) => {
    if (streak >= 100) return <Trophy className="w-5 h-5 text-yellow-500" />
    if (streak >= 30) return <Star className="w-5 h-5 text-purple-500" />
    if (streak >= 7) return <Gift className="w-5 h-5 text-blue-500" />
    return <Calendar className="w-5 h-5 text-green-500" />
  }

  const getBoosterIcon = (type: string) => {
    switch (type) {
      case 'xp_boost':
        return <Zap className="w-4 h-4 text-yellow-500" />
      case 'recovery_boost':
        return <Star className="w-4 h-4 text-blue-500" />
      case 'injury_heal':
        return <Trophy className="w-4 h-4 text-green-500" />
      default:
        return <Gift className="w-4 h-4" />
    }
  }

  if (!loginStatus) return null

  return (
    <>
      {/* Daily Login Button */}
      <Button
        onClick={() => setShowModal(true)}
        variant={loginStatus.canClaim ? "default" : "outline"}
        className={`relative ${loginStatus.canClaim ? 'animate-pulse' : ''}`}
      >
        <Gift className="w-4 h-4 mr-2" />
        Daily Reward
        {loginStatus.canClaim && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping" />
        )}
      </Button>

      {/* Daily Rewards Modal */}
      <AnimatePresence>
        {showModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <Card className="border-0 shadow-none">
                <CardHeader className="text-center relative">
                  <button
                    onClick={() => setShowModal(false)}
                    className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
                  >
                    <X className="w-5 h-5" />
                  </button>
                  
                  <div className="flex items-center justify-center mb-2">
                    {getStreakIcon(loginStatus.streak)}
                  </div>
                  
                  <CardTitle className="text-xl">
                    {loginStatus.canClaim ? 'Daily Reward!' : 'Login Streak'}
                  </CardTitle>
                  
                  <div className="flex items-center justify-center space-x-2 text-sm text-muted">
                    <Calendar className="w-4 h-4" />
                    <span>{loginStatus.streak} day streak</span>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {loginStatus.canClaim && loginStatus.reward && (
                    <motion.div
                      initial={{ scale: 0.8 }}
                      animate={{ scale: 1 }}
                      className="text-center space-y-4"
                    >
                      <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4">
                        <h3 className="font-semibold mb-3">Today's Reward</h3>
                        
                        <div className="space-y-2">
                          {loginStatus.reward.coins > 0 && (
                            <div className="flex items-center justify-center space-x-2">
                              <Coins className="w-5 h-5 text-yellow-500" />
                              <span className="font-medium">{loginStatus.reward.coins.toLocaleString()} Coins</span>
                            </div>
                          )}
                          
                          {loginStatus.reward.tokens > 0 && (
                            <div className="flex items-center justify-center space-x-2">
                              <Star className="w-5 h-5 text-purple-500" />
                              <span className="font-medium">{loginStatus.reward.tokens} Tokens</span>
                            </div>
                          )}
                          
                          {loginStatus.reward.boosters && (
                            <div className="flex items-center justify-center space-x-2">
                              {getBoosterIcon(loginStatus.reward.boosters.type)}
                              <span className="font-medium">
                                {loginStatus.reward.boosters.amount}x {loginStatus.reward.boosters.type.replace('_', ' ')}
                              </span>
                            </div>
                          )}
                        </div>
                        
                        {loginStatus.reward.special && (
                          <div className="mt-3 text-xs text-purple-600 dark:text-purple-400 font-medium">
                            ✨ Special Weekly Bonus!
                          </div>
                        )}
                      </div>

                      {!claimed ? (
                        <Button
                          onClick={claimReward}
                          disabled={claiming}
                          className="w-full"
                        >
                          {claiming ? (
                            <div className="flex items-center space-x-2">
                              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                              <span>Claiming...</span>
                            </div>
                          ) : (
                            <div className="flex items-center space-x-2">
                              <Gift className="w-4 h-4" />
                              <span>Claim Reward</span>
                            </div>
                          )}
                        </Button>
                      ) : (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="flex items-center justify-center space-x-2 text-green-600 dark:text-green-400"
                        >
                          <CheckCircle className="w-5 h-5" />
                          <span className="font-medium">Reward Claimed!</span>
                        </motion.div>
                      )}
                    </motion.div>
                  )}

                  {!loginStatus.canClaim && (
                    <div className="text-center space-y-3">
                      <div className="text-muted">
                        You've already claimed today's reward!
                      </div>
                      <div className="text-sm text-muted">
                        Come back tomorrow for day {loginStatus.streak + 1}
                      </div>
                    </div>
                  )}

                  {/* Reward Preview */}
                  <div className="border-t pt-4">
                    <h4 className="font-medium mb-3 text-center">Weekly Rewards</h4>
                    <div className="grid grid-cols-7 gap-1">
                      {DAILY_REWARDS.map((reward, index) => {
                        const dayNumber = index + 1
                        const isCurrentDay = (loginStatus.streak % 7) === dayNumber || 
                                           (loginStatus.streak % 7 === 0 && dayNumber === 7)
                        const isPastDay = loginStatus.streak > dayNumber || 
                                         (loginStatus.streak >= 7 && loginStatus.streak % 7 > dayNumber)
                        
                        return (
                          <div
                            key={dayNumber}
                            className={`text-center p-2 rounded text-xs ${
                              isCurrentDay 
                                ? 'bg-blue-100 dark:bg-blue-900 border-2 border-blue-500' 
                                : isPastDay
                                ? 'bg-green-100 dark:bg-green-900'
                                : 'bg-gray-100 dark:bg-gray-700'
                            }`}
                          >
                            <div className="font-medium">Day {dayNumber}</div>
                            <div className="text-xs">
                              {reward.coins > 0 && <div>{reward.coins}</div>}
                              {reward.tokens > 0 && <div className="text-purple-600">+{reward.tokens}T</div>}
                              {reward.special && <div className="text-yellow-600">✨</div>}
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
