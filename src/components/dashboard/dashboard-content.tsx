'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { User } from '@supabase/supabase-js'
import { Profile, UserCurrency } from '@/lib/types'
import { calculatePlayerOverall } from '@/lib/game-engine'
import { calculateTeamRating, getExperienceForNextLevel } from '@/lib/progression-system'
import { dailyRewardsService } from '@/lib/daily-rewards'
import { defaultTeamService, DEFAULT_ROSTER } from '@/lib/default-team-service'
import { Card, CardContent, CardHeader, CardTitle, StatCard } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { LoadingCard, LoadingSkeleton } from '@/components/ui/loading-states'
import { NoPlayersEmpty, EmptyCard } from '@/components/ui/empty-states'
import DailyRewards from './daily-rewards'
import WelcomeMessage from '@/components/onboarding/welcome-message'
import {
  Trophy,
  Users,
  TrendingUp,
  Calendar,
  Star,
  Target,
  Award,
  Plus,
  DollarSign,
  Zap,
  Shield,
  Clock,
  Play,
  Coins
} from 'lucide-react'
import Link from 'next/link'
import { unifiedCurrencyService } from '@/lib/unified-currency-service'
import { useCurrency } from '@/lib/currency-context'
import { calculateRatingsFromPlayers, calculateTeamRatingFromTeam, getRatingColor } from '@/lib/rating-utils'
import CurrencySyncTest from '@/components/test/currency-sync-test'

interface DashboardContentProps {
  user: User
  profile: Profile | null
}

export default function DashboardContent({ user, profile }: DashboardContentProps) {
  const [gameData, setGameData] = useState({
    teamRating: 0,
    cashBalance: 0,
    nextMatch: null as any,
    trainingAvailable: true
  })
  const [userCurrency, setUserCurrency] = useState<UserCurrency | null>(null)
  const [isCreatingTeam, setIsCreatingTeam] = useState(false)
  const [showWelcome, setShowWelcome] = useState(false)
  const [isNewTeam, setIsNewTeam] = useState(false)
  const { currency, formatBudgetStat } = useCurrency()

  // Calculate ratings directly from players
  const players = profile?.team?.players || []
  const { avgRating } = calculateRatingsFromPlayers(players)
  const teamRating = calculateTeamRatingFromTeam(profile?.team || null)

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  // Calculate game stats
  useEffect(() => {
    if (profile?.team) {
      const teamRating = profile.team.players ? calculateTeamRating(profile.team) : 0
      setGameData({
        teamRating,
        cashBalance: profile.team.cash_balance || 50000,
        nextMatch: null, // Would be fetched from database
        trainingAvailable: true
      })
    }
  }, [profile])

  // Load user currency and check for default team
  useEffect(() => {
    const loadUserData = async () => {
      const currency = await dailyRewardsService.getUserCurrency(user.id)
      setUserCurrency(currency)

      // Check if user needs a default team
      if (!profile?.team) {
        setIsCreatingTeam(true)
        const teamResult = await defaultTeamService.checkAndCreateDefaultTeam(user.id)
        setIsCreatingTeam(false)

        if (teamResult.hasTeam && teamResult.team) {
          setIsNewTeam(true)
          setShowWelcome(true)
          // Refresh the page to load the new team data
          setTimeout(() => {
            window.location.reload()
          }, 1000)
        }
      }
    }
    loadUserData()
  }, [user.id, profile?.team])

  // Check if team was recently created (show welcome for new teams)
  useEffect(() => {
    if (profile?.team && !showWelcome) {
      const teamCreatedAt = new Date(profile.team.created_at)
      const now = new Date()
      const hoursSinceCreation = (now.getTime() - teamCreatedAt.getTime()) / (1000 * 60 * 60)

      // Check if welcome message has been dismissed for this team
      const welcomeDismissed = localStorage.getItem(`welcome-dismissed-${profile.team.id}`)

      // Show welcome message if team was created within the last hour and hasn't been dismissed
      if (hoursSinceCreation < 1 && !welcomeDismissed) {
        setIsNewTeam(true)
        setShowWelcome(true)
      }
    }
  }, [profile?.team, showWelcome])

  const handleCurrencyUpdate = (currency: UserCurrency) => {
    setUserCurrency(currency)
  }

  // Show loading state while creating default team
  if (isCreatingTeam) {
    return (
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Setting up your team...</h3>
              <p className="text-muted">
                We're creating your default roster with 10 talented players!
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const experienceProgress = profile ? getExperienceForNextLevel(profile.experience_points) : { current: 0, required: 100, progress: 0 }

  const stats = [
    {
      title: 'Manager Level',
      value: profile?.manager_level || 1,
      icon: <Star className="w-6 h-6" />,
      color: 'yellow' as const,
      trend: experienceProgress.progress > 0.5 ? { value: Math.round(experienceProgress.progress * 100), isPositive: true } : undefined
    },
    {
      title: 'Avg Rating',
      value: avgRating.toString(),
      icon: <TrendingUp className="w-6 h-6" />,
      color: 'green' as const,
      trend: avgRating > 70 ? { value: 12, isPositive: true } : undefined
    },
    {
      title: 'Budget',
      value: formatBudgetStat(),
      icon: <DollarSign className="w-6 h-6" />,
      color: 'yellow' as const
    },
    {
      title: 'Tokens',
      value: userCurrency?.tokens || 0,
      icon: <Star className="w-6 h-6" />,
      color: 'purple' as const
    },
  ]

  // Mock recent matches and events for now
  const recentMatches: Array<{ opponent: string; result: string; score: string; date: string }> = []
  const upcomingEvents: Array<{ title: string; date: string; type: string }> = []

  return (
    <div className="min-h-screen bg-gradient-to-br from-muted-bg via-background to-muted-bg/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8"
        >
          {/* Welcome Message */}
          <motion.div variants={itemVariants} className="text-center mb-8">
            <div className="relative">
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6 }}
                className="bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 rounded-2xl p-8 border border-primary/20"
              >
                <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-6">
                  <div className="text-center md:text-left">
                    <motion.h1
                      className="text-3xl md:text-4xl font-bold text-foreground mb-2"
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      Welcome back, {profile?.full_name || 'Manager'}!
                    </motion.h1>
                    <motion.p
                      className="text-muted text-lg"
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.3 }}
                    >
                      Ready to lead your team to victory?
                    </motion.p>
                  </div>
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                  >
                    <DailyRewards user={user} onCurrencyUpdate={handleCurrencyUpdate} />
                  </motion.div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Stats Grid */}
          <motion.div
            variants={itemVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            <AnimatePresence>
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <StatCard
                    title={stat.title}
                    value={typeof stat.value === 'string' ? stat.value : stat.value.toLocaleString()}
                    icon={stat.icon}
                    color={stat.color}
                    trend={stat.trend}
                    className="hover:scale-105 transition-transform duration-200"
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>

          {/* Quick Actions */}
          <motion.div variants={itemVariants}>
            <Card variant="gradient">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {[
                    { href: '/training', icon: Zap, label: 'Training', color: 'text-blue-500', bgColor: 'hover:bg-blue-50' },
                    { href: '/transfers', icon: Users, label: 'Transfers', color: 'text-green-500', bgColor: 'hover:bg-green-50' },
                    { href: '/league', icon: Trophy, label: 'League', color: 'text-yellow-500', bgColor: 'hover:bg-yellow-50' },
                    { href: '/facilities', icon: Shield, label: 'Facilities', color: 'text-purple-500', bgColor: 'hover:bg-purple-50' }
                  ].map((action, index) => (
                    <motion.div
                      key={action.href}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Link href={action.href}>
                        <div className={`w-full h-24 flex flex-col items-center justify-center space-y-2 rounded-xl border-2 border-card-border bg-card-bg transition-all duration-300 cursor-pointer ${action.bgColor} hover:border-primary/30 hover:shadow-md group`}>
                          <action.icon className={`w-7 h-7 ${action.color} group-hover:scale-110 transition-transform duration-200`} />
                          <span className="text-sm font-medium text-foreground group-hover:text-primary transition-colors duration-200">
                            {action.label}
                          </span>
                        </div>
                      </Link>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Team Overview */}
            <motion.div variants={itemVariants} className="lg:col-span-2">
              <Card variant="elevated" hover>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Users className="w-5 h-5" />
                      Your Team
                    </span>
                    <Link href="/team-builder">
                      <Button variant="outline" size="sm" className="hover:bg-primary hover:text-white transition-colors duration-200">
                        <Plus className="w-4 h-4 mr-2" />
                        Manage Team
                      </Button>
                    </Link>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {profile?.team ? (
                    <div className="space-y-6">
                      <motion.div
                        className="flex items-center justify-between p-6 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-xl border border-primary/20"
                        whileHover={{ scale: 1.02 }}
                        transition={{ duration: 0.2 }}
                      >
                        <div>
                          <h3 className="font-bold text-xl text-foreground mb-1">{profile.team.name}</h3>
                          <p className="text-muted flex items-center gap-4">
                            <span className="flex items-center gap-1">
                              <Users className="w-4 h-4" />
                              {profile.team.players?.length || 0} players
                            </span>
                            <span className="flex items-center gap-1">
                              <Target className="w-4 h-4" />
                              {profile.team.formation}
                            </span>
                          </p>
                        </div>
                        <div className="text-right">
                          <div className={`text-3xl font-bold mb-1 ${getRatingColor(avgRating)}`}>{avgRating}</div>
                          <div className="text-sm text-muted font-medium">Avg Rating</div>
                        </div>
                      </motion.div>

                      {profile.team.players && profile.team.players.length > 0 ? (
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <h4 className="font-semibold text-foreground">Squad Overview</h4>
                            <span className="text-sm text-muted">
                              Showing {Math.min(6, profile.team.players.length)} of {profile.team.players.length} players
                            </span>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {profile.team.players.slice(0, 6).map((player, index) => (
                              <motion.div
                                key={player.id}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                whileHover={{ scale: 1.02 }}
                                className="flex items-center space-x-4 p-4 border border-card-border rounded-xl bg-card-bg hover:bg-hover-bg hover:border-primary/30 transition-all duration-200 cursor-pointer"
                              >
                                <div className="relative">
                                  <div className="w-12 h-12 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center shadow-md">
                                    <span className="text-white font-bold text-sm">
                                      {player.name.split(' ').map(n => n[0]).join('')}
                                    </span>
                                  </div>
                                  {/* Status indicators */}
                                  <div className="absolute -top-1 -right-1 flex flex-col space-y-1">
                                    {player.fatigue > 70 && (
                                      <div className="w-3 h-3 bg-red-500 rounded-full border-2 border-white" title="High Fatigue" />
                                    )}
                                    {player.injury_status !== 'healthy' && (
                                      <div className="w-3 h-3 bg-yellow-500 rounded-full border-2 border-white" title="Injured" />
                                    )}
                                    {player.morale > 80 && (
                                      <div className="w-3 h-3 bg-green-500 rounded-full border-2 border-white" title="High Morale" />
                                    )}
                                  </div>
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="font-semibold text-foreground truncate">{player.name}</div>
                                  <div className="text-sm text-muted capitalize">
                                    {player.position.replace('-', ' ')} • Age {player.age}
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="text-lg font-bold text-primary">
                                    {calculatePlayerOverall(player)}
                                  </div>
                                  <div className="text-xs text-muted font-medium">OVR</div>
                                </div>
                              </motion.div>
                            ))}
                          </div>
                          {profile.team.players.length > 6 && (
                            <div className="text-center pt-2">
                              <Link href="/team-builder">
                                <Button variant="ghost" size="sm">
                                  View All Players ({profile.team.players.length})
                                </Button>
                              </Link>
                            </div>
                          )}
                        </div>
                      ) : (
                        <NoPlayersEmpty onCreateTeam={() => window.location.href = '/team-builder'} />
                      )}
                    </div>
                  ) : (
                    <NoPlayersEmpty onCreateTeam={handleCreateDefaultTeam} />
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* Sidebar */}
            <motion.div variants={itemVariants} className="space-y-6">
              {/* Recent Matches */}
              <Card variant="elevated" hover>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Trophy className="w-5 h-5" />
                    Recent Matches
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {recentMatches.length > 0 ? (
                    <div className="space-y-3">
                      {recentMatches.map((match, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="flex items-center justify-between p-4 border border-card-border rounded-xl hover:bg-hover-bg transition-colors duration-200"
                        >
                          <div>
                            <div className="font-semibold text-foreground">{match.opponent}</div>
                            <div className="text-sm text-muted flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {match.date}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`font-bold text-lg flex items-center justify-end space-x-2 ${match.result === 'W' ? 'text-success' : 'text-error'}`}>
                              <span>{match.result}</span>
                              <div className={`w-3 h-3 rounded-full ${match.result === 'W' ? 'bg-success' : 'bg-error'}`}></div>
                            </div>
                            <div className="text-sm text-muted font-medium">{match.score}</div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <EmptyCard
                      icon={<Trophy className="w-12 h-12" />}
                      title="No Matches Yet"
                      description="Schedule your first match to start competing!"
                      action={{
                        label: "View League",
                        onClick: () => window.location.href = '/league',
                        variant: "outline"
                      }}
                    />
                  )}
                </CardContent>
              </Card>

              {/* Upcoming Events */}
              <Card variant="elevated" hover>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Upcoming Events
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {upcomingEvents.length > 0 ? (
                    <div className="space-y-3">
                      {upcomingEvents.map((event, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="flex items-start space-x-4 p-4 border border-card-border rounded-xl hover:bg-hover-bg transition-colors duration-200"
                        >
                          <div className="flex flex-col items-center space-y-1 mt-1">
                            <div className={`w-4 h-4 rounded-full ${
                              event.type === 'match' ? 'bg-red-500' :
                              event.type === 'training' ? 'bg-blue-500' : 'bg-yellow-500'
                            }`} />
                            <span className={`text-xs font-bold uppercase tracking-wider ${
                              event.type === 'match' ? 'text-red-600' :
                              event.type === 'training' ? 'text-blue-600' : 'text-yellow-600'
                            }`}>
                              {event.type}
                            </span>
                          </div>
                          <div className="flex-1">
                            <div className="font-semibold text-foreground">{event.title}</div>
                            <div className="text-sm text-muted flex items-center gap-1 mt-1">
                              <Clock className="w-3 h-3" />
                              {event.date}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <EmptyCard
                      icon={<Calendar className="w-12 h-12" />}
                      title="No Events Scheduled"
                      description="Schedule training sessions or matches to stay active!"
                      action={{
                        label: "Start Training",
                        onClick: () => window.location.href = '/training',
                        variant: "outline"
                      }}
                    />
                  )}
                </CardContent>
              </Card>

              {/* Achievements */}
              <Card variant="elevated" hover>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    Recent Achievements
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <EmptyCard
                    icon={<Award className="w-12 h-12" />}
                    title="No Achievements Yet"
                    description="Complete matches and training to earn your first achievements!"
                  />
                </CardContent>
              </Card>

              {/* Development Test Components */}
              {process.env.NODE_ENV === 'development' && (
                <CurrencySyncTest />
              )}
            </motion.div>
          </div>
        </motion.div>
      </div>

      {/* Welcome Message for New Teams */}
      {showWelcome && isNewTeam && (
        <WelcomeMessage
          playerNames={DEFAULT_ROSTER.map(p => p.name)}
          onClose={() => {
            setShowWelcome(false)
            // Persist dismissal in localStorage
            if (profile?.team?.id) {
              localStorage.setItem(`welcome-dismissed-${profile.team.id}`, 'true')
            }
          }}
        />
      )}
    </div>
  )
}
