'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { User } from '@supabase/supabase-js'
import { Profile, TrainingSession } from '@/lib/types'
import { useAuth } from '@/lib/auth-context'
import { useCashBalance } from '@/hooks/use-cash-balance'
import Enhanced<PERSON>rainingCenter from './enhanced-training-center'
import { Card, CardContent, CardHeader, CardTitle, StatCard } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { NoPlayersEmpty, EmptyCard } from '@/components/ui/empty-states'
import { ArrowLeft, Zap, TrendingUp, Users, Activity, Coins, Target, Clock, Award, DollarSign } from 'lucide-react'
import Link from 'next/link'
import { unifiedCurrencyService } from '@/lib/unified-currency-service'
import { useCurrency } from '@/lib/currency-context'
import { useRating } from '@/lib/rating-context'

interface TrainingPageContentProps {
  user: User
  profile: Profile | null
}

export default function TrainingPageContent({ user, profile }: TrainingPageContentProps) {
  const { refreshCashBalance } = useCashBalance()
  const { formatBudgetStat } = useCurrency()
  const { refreshRatings } = useRating()
  const [players, setPlayers] = useState(profile?.team?.players || [])
  const handleTrainingComplete = async (updatedPlayers: any[], session: TrainingSession) => {
    try {
      // Update local state
      setPlayers(updatedPlayers)

      // Refresh the global profile to update cash balance across all components
      await refreshCashBalance()

      // Refresh ratings after training
      await refreshRatings()

      // Show success message or notification here
      console.log('Training completed successfully!')
    } catch (error) {
      console.error('Error completing training:', error)
    }
  }

  const facilityBonus = profile?.team?.facilities?.find(f => f.facility_type === 'training_pool')?.bonus_percentage || 0

  if (!profile?.team) {
    return (
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <Card>
            <CardContent className="pt-6 text-center py-12">
              <Users className="w-12 h-12 mx-auto text-muted mb-4" />
              <h3 className="text-lg font-medium mb-2">No Team Found</h3>
              <p className="text-muted mb-4">
                You need to create a team before you can access the training center.
              </p>
              <Link href="/team-builder">
                <Button>Create Your Team</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-muted-bg via-background to-muted-bg/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-8"
        >
          {/* Page Header */}
          <motion.div variants={itemVariants} className="text-center mb-8">
            <div className="bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 rounded-2xl p-8 border border-primary/20">
              <motion.h1
                className="text-3xl md:text-4xl font-bold text-foreground mb-2 flex items-center justify-center gap-3"
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                <Zap className="w-8 h-8 text-primary" />
                Training Center
              </motion.h1>
              <motion.p
                className="text-muted text-lg"
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
              >
                Develop your players' skills and unlock their potential
              </motion.p>
            </div>
          </motion.div>

          {/* Training Stats */}
          <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatCard
              title="Training Pool Level"
              value={profile.team.facilities?.find(f => f.facility_type === 'training_pool')?.level || 1}
              icon={<Zap className="w-6 h-6" />}
              color="blue"
            />
            <StatCard
              title="Training Bonus"
              value={`+${facilityBonus}%`}
              icon={<TrendingUp className="w-6 h-6" />}
              color="green"
            />
            <StatCard
              title="Available Players"
              value={players.filter(p => p.fatigue < 80 && p.injury_status === 'healthy').length}
              icon={<Users className="w-6 h-6" />}
              color="purple"
            />
            <StatCard
              title="Budget"
              value={formatBudgetStat()}
              icon={<DollarSign className="w-6 h-6" />}
              color="yellow"
            />
          </motion.div>

          {/* Enhanced Training Center Component */}
          <motion.div variants={itemVariants}>
            <EnhancedTrainingCenter
              teamId={profile.team.id}
              players={players}
              teamCash={profile?.userCurrency?.coins || 0}
              onTrainingComplete={handleTrainingComplete}
            />
          </motion.div>

          {/* Player Status Overview */}
          {players.length > 0 && (
            <motion.div variants={itemVariants}>
              <Card variant="elevated" hover>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="w-5 h-5" />
                    Player Status Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {players.map((player, index) => {
                      const isFatigued = (player.fatigue || 0) > 70
                      const isInjured = player.injury_status !== 'healthy'
                      const hasHighMorale = (player.morale || 0) > 80

                      return (
                        <motion.div
                          key={player.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.05 }}
                          className={`p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-md ${
                            isInjured ? 'border-red-300 bg-red-50 hover:bg-red-100' :
                            isFatigued ? 'border-yellow-300 bg-yellow-50 hover:bg-yellow-100' :
                            hasHighMorale ? 'border-green-300 bg-green-50 hover:bg-green-100' :
                            'border-card-border bg-card-bg hover:bg-hover-bg'
                          }`}
                        >
                          <div className="flex items-center justify-between mb-3">
                            <div className="font-semibold text-foreground">{player.name}</div>
                            <div className="text-sm text-muted capitalize font-medium">
                              {player.position.replace('-', ' ')}
                            </div>
                          </div>

                          <div className="space-y-3 text-sm">
                            <div className="flex justify-between items-center">
                              <span className="text-muted">Fatigue:</span>
                              <div className="flex items-center gap-2">
                                <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                                  <div
                                    className={`h-full transition-all duration-300 ${
                                      (player.fatigue || 0) > 70 ? 'bg-red-500' :
                                      (player.fatigue || 0) > 40 ? 'bg-yellow-500' : 'bg-green-500'
                                    }`}
                                    style={{ width: `${Math.round(player.fatigue || 0)}%` }}
                                  />
                                </div>
                                <span className={`font-medium ${isFatigued ? 'text-red-600' : 'text-green-600'}`}>
                                  {Math.round(player.fatigue || 0)}%
                                </span>
                              </div>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-muted">Morale:</span>
                              <div className="flex items-center gap-2">
                                <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                                  <div
                                    className={`h-full transition-all duration-300 ${
                                      (player.morale || 0) > 80 ? 'bg-green-500' :
                                      (player.morale || 0) > 50 ? 'bg-yellow-500' : 'bg-red-500'
                                    }`}
                                    style={{ width: `${Math.round(player.morale || 0)}%` }}
                                  />
                                </div>
                                <span className={`font-medium ${hasHighMorale ? 'text-green-600' : 'text-muted'}`}>
                                  {Math.round(player.morale || 0)}%
                                </span>
                              </div>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted">Status:</span>
                              <span className={`font-medium ${isInjured ? 'text-red-600' : 'text-green-600'}`}>
                                {isInjured ? player.injury_status : 'Healthy'}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted">Focus:</span>
                              <span className="capitalize font-medium text-foreground">{player.training_focus}</span>
                            </div>
                          </div>
                        </motion.div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  )
}
