'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Player, PlayerPerk } from '@/lib/types'
import { trainingService, TrainingDrill } from '@/lib/training-service'
import { calculatePlayerOverall } from '@/lib/game-engine'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import {
  Zap,
  Users,
  TrendingUp,
  Clock,
  Target,
  Star,
  AlertTriangle,
  CheckCircle,
  Play,
  Pause,
  Plus,
  Coins,
  Award,
  Activity,
  BarChart3,
  X
} from 'lucide-react'

interface EnhancedTrainingCenterProps {
  teamId: string
  players: Player[]
  teamCash: number
  onTrainingComplete: (updatedPlayers: Player[], session: any) => void
}

export default function EnhancedTrainingCenter({
  teamId,
  players,
  teamCash,
  onTrainingComplete
}: EnhancedTrainingCenterProps) {
  const [availableDrills, setAvailableDrills] = useState<TrainingDrill[]>([])
  const [selectedDrill, setSelectedDrill] = useState<TrainingDrill | null>(null)
  const [selectedPlayers, setSelectedPlayers] = useState<string[]>([])
  const [isTraining, setIsTraining] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [trainingResults, setTrainingResults] = useState<any>(null)
  const [isLoadingDrills, setIsLoadingDrills] = useState(true)

  useEffect(() => {
    loadAvailableDrills()
  }, [teamId, teamCash])

  const loadAvailableDrills = async () => {
    try {
      setIsLoadingDrills(true)
      const drills = await trainingService.getAvailableTrainingDrills(teamId)
      setAvailableDrills(drills || [])
    } catch (error) {
      console.error('Error loading training drills:', error)
      setAvailableDrills([])
    } finally {
      setIsLoadingDrills(false)
    }
  }

  const handleDrillSelect = (drill: TrainingDrill) => {
    setSelectedDrill(drill)
    setSelectedPlayers([])
  }

  const handlePlayerToggle = (playerId: string) => {
    setSelectedPlayers(prev => 
      prev.includes(playerId) 
        ? prev.filter(id => id !== playerId)
        : [...prev, playerId]
    )
  }

  const handleStartTraining = async () => {
    if (!selectedDrill || selectedPlayers.length === 0) return

    setIsTraining(true)
    try {
      const drill = {
        ...selectedDrill,
        player_ids: selectedPlayers
      }

      const result = await trainingService.createTrainingSession(teamId, drill)

      if (result.success) {
        setTrainingResults(result)
        setShowResults(true)
        // Extract the updated players from the result and pass session separately
        const updatedPlayers = result.playerResults?.map(pr => pr.player) || []
        onTrainingComplete(updatedPlayers, result.session)
      } else {
        alert(result.error || 'Training failed')
      }
    } catch (error) {
      console.error('Training error:', error)
      alert(`Training failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsTraining(false)
    }
  }

  const getPlayerStatusColor = (player: Player) => {
    if (player.injury_status !== 'healthy') return 'border-red-500 bg-red-50 dark:bg-red-900/20'
    if (player.fatigue > 70) return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
    if (player.morale > 80) return 'border-green-500 bg-green-50 dark:bg-green-900/20'
    return 'border-border'
  }

  const getIntensityColor = (intensity: string) => {
    switch (intensity) {
      case 'light': return 'text-green-500'
      case 'medium': return 'text-blue-500'
      case 'high': return 'text-orange-500'
      case 'extreme': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const availablePlayers = players.filter(p => 
    p.injury_status === 'healthy' && p.fatigue < 90
  )

  return (
    <div className="space-y-6">
      {/* Training Drills Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5" />
            <span>Available Training Drills</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingDrills ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2 text-muted">Loading training drills...</span>
            </div>
          ) : availableDrills.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted">No training drills available</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {availableDrills.map(drill => (
                <motion.div
                  key={drill.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`p-4 rounded-lg border cursor-pointer transition-all ${
                    selectedDrill?.id === drill.id
                      ? 'border-primary bg-primary/10'
                      : 'border-border hover:border-primary/50'
                  }`}
                  onClick={() => handleDrillSelect(drill)}
                >
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{drill.name}</h3>
                      <div className="flex items-center space-x-1">
                        <Coins className="w-4 h-4 text-yellow-500" />
                        <span className="text-sm font-medium">{drill.cost || 0}</span>
                      </div>
                    </div>
                  
                  <p className="text-sm text-muted">{drill.description}</p>
                  
                  <div className="flex items-center justify-between text-xs">
                    <div className="flex items-center space-x-2">
                      <Clock className="w-3 h-3" />
                      <span>{drill.duration_minutes}m</span>
                    </div>
                    <div className={`font-medium ${getIntensityColor(drill.intensity)}`}>
                      {drill.intensity.toUpperCase()}
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="w-3 h-3 text-yellow-500" />
                      <span>{drill.xp_multiplier}x XP</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Player Selection */}
      {selectedDrill && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="w-5 h-5" />
              <span>Select Players for {selectedDrill.name}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {availablePlayers.map(player => (
                <motion.div
                  key={player.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`p-4 rounded-lg border cursor-pointer transition-all ${
                    selectedPlayers.includes(player.id)
                      ? 'border-primary bg-primary/10'
                      : getPlayerStatusColor(player)
                  }`}
                  onClick={() => handlePlayerToggle(player.id)}
                >
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="font-medium">{player.name}</div>
                      <div className="text-lg font-bold text-primary">
                        {calculatePlayerOverall(player)}
                      </div>
                    </div>
                    
                    <div className="text-sm text-muted capitalize">
                      {player.position.replace('-', ' ')}
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="flex justify-between">
                        <span>Fatigue:</span>
                        <span className={(player.fatigue || 0) > 70 ? 'text-red-500' : 'text-green-500'}>
                          {Math.round(player.fatigue || 0)}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Morale:</span>
                        <span className={(player.morale || 0) > 80 ? 'text-green-500' : 'text-muted'}>
                          {Math.round(player.morale || 0)}%
                        </span>
                      </div>
                    </div>
                    
                    {selectedPlayers.includes(player.id) && (
                      <div className="flex items-center justify-center mt-2">
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
            
            {selectedPlayers.length > 0 && (
              <div className="mt-6 flex items-center justify-between">
                <div className="text-sm text-muted">
                  {selectedPlayers.length} player(s) selected
                </div>
                <Button
                  onClick={handleStartTraining}
                  disabled={isTraining}
                  className="flex items-center space-x-2"
                >
                  {isTraining ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Training...</span>
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4" />
                      <span>Start Training</span>
                    </>
                  )}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Training Results Modal */}
      <AnimatePresence>
        {showResults && trainingResults && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowResults(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <Card className="border-0 shadow-none">
                <CardHeader className="relative">
                  <button
                    onClick={() => setShowResults(false)}
                    className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
                  >
                    <X className="w-5 h-5" />
                  </button>
                  
                  <CardTitle className="text-center">Training Complete!</CardTitle>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {trainingResults.playerResults?.map((result: any, index: number) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-medium">{result.player.name}</h3>
                        <div className="text-sm text-muted">
                          +{result.experienceGained} XP
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        {Object.entries(result.statGains).map(([stat, gain]) => (
                          <div key={stat} className="flex justify-between">
                            <span className="capitalize">{stat}:</span>
                            <span className="text-green-500">+{gain}</span>
                          </div>
                        ))}
                      </div>
                      
                      {result.newPerks.length > 0 && (
                        <div className="mt-3 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded">
                          <div className="flex items-center space-x-2 text-yellow-700 dark:text-yellow-300">
                            <Award className="w-4 h-4" />
                            <span className="font-medium">New Perk Unlocked!</span>
                          </div>
                          {result.newPerks.map((perk: PlayerPerk) => (
                            <div key={perk.id} className="mt-1 text-sm">
                              <div className="font-medium">{perk.name}</div>
                              <div className="text-xs text-muted">{perk.description}</div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
