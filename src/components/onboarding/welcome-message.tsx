'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import But<PERSON> from '@/components/ui/button'
import {
  Trophy,
  Users,
  Target,
  Zap,
  Star,
  CheckCircle,
  X,
  ArrowRight
} from 'lucide-react'

interface WelcomeMessageProps {
  playerNames: string[]
  onClose: () => void
}

export default function WelcomeMessage({ playerNames, onClose }: WelcomeMessageProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [showPlayers, setShowPlayers] = useState(false)

  const steps = [
    {
      title: "Welcome to Aqua Eleven!",
      description: "Your water polo management journey begins now.",
      icon: Trophy,
      color: "text-yellow-500"
    },
    {
      title: "Meet Your Team",
      description: "We've assembled a talented roster of 10 players for you.",
      icon: Users,
      color: "text-blue-500"
    },
    {
      title: "Ready to Play",
      description: "Train your players, manage tactics, and lead your team to victory!",
      icon: Target,
      color: "text-green-500"
    }
  ]

  useEffect(() => {
    if (currentStep === 1) {
      setTimeout(() => setShowPlayers(true), 500)
    }
  }, [currentStep])

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
      setShowPlayers(false)
    } else {
      onClose()
    }
  }

  const currentStepData = steps[currentStep]
  const Icon = currentStepData.icon

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto"
        >
          <Card className="border-0 shadow-none">
            <CardHeader className="text-center relative">
              <button
                onClick={onClose}
                className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
              >
                <X className="w-5 h-5" />
              </button>
              
              <motion.div
                key={currentStep}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className={`w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mx-auto mb-4`}
              >
                <Icon className={`w-8 h-8 ${currentStepData.color}`} />
              </motion.div>
              
              <CardTitle className="text-2xl mb-2">
                {currentStepData.title}
              </CardTitle>
              
              <p className="text-muted">
                {currentStepData.description}
              </p>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* Progress Indicator */}
              <div className="flex justify-center space-x-2">
                {steps.map((_, index) => (
                  <div
                    key={index}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index <= currentStep ? 'bg-primary' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>

              {/* Step Content */}
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="text-center"
              >
                {currentStep === 0 && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <Zap className="w-8 h-8 text-blue-500 mx-auto mb-2" />
                        <h4 className="font-medium">Train Players</h4>
                        <p className="text-sm text-muted">Improve skills and unlock perks</p>
                      </div>
                      <div className="text-center">
                        <Target className="w-8 h-8 text-green-500 mx-auto mb-2" />
                        <h4 className="font-medium">Tactical Matches</h4>
                        <p className="text-sm text-muted">Real-time simulation with decisions</p>
                      </div>
                      <div className="text-center">
                        <Trophy className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
                        <h4 className="font-medium">Win Leagues</h4>
                        <p className="text-sm text-muted">Climb divisions and earn rewards</p>
                      </div>
                    </div>
                  </div>
                )}

                {currentStep === 1 && (
                  <div className="space-y-4">
                    <p className="text-lg font-medium">Your Starting Roster</p>
                    <AnimatePresence>
                      {showPlayers && (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          className="grid grid-cols-2 gap-3"
                        >
                          {playerNames.map((name, index) => (
                            <motion.div
                              key={name}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                            >
                              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                <span className="text-sm font-bold text-primary">
                                  {index + 1}
                                </span>
                              </div>
                              <span className="font-medium">{name}</span>
                            </motion.div>
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                )}

                {currentStep === 2 && (
                  <div className="space-y-4">
                    <div className="bg-gradient-to-r from-blue-50 to-green-50 dark:from-blue-900/20 dark:to-green-900/20 rounded-lg p-6">
                      <h4 className="font-bold text-lg mb-3">You're all set!</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>50,000 coins to start</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>5 premium tokens</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>Basic facilities</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>League placement</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <p className="text-muted mb-4">
                        Start by training your players and setting up your formation!
                      </p>
                    </div>
                  </div>
                )}
              </motion.div>

              {/* Navigation */}
              <div className="flex justify-between items-center pt-4">
                <div className="text-sm text-muted">
                  Step {currentStep + 1} of {steps.length}
                </div>
                
                <Button onClick={handleNext} className="flex items-center space-x-2">
                  <span>{currentStep === steps.length - 1 ? 'Get Started' : 'Next'}</span>
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
