'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { User } from '@supabase/supabase-js'
import { Profile, Facility, FACILITY_TYPES, UserCurrency } from '@/lib/types'
import { useAuth } from '@/lib/auth-context'
import { useCashBalance } from '@/hooks/use-cash-balance'
import { facilitiesService, FacilityUpgrade } from '@/lib/facilities-service'
import { dailyRewardsService } from '@/lib/daily-rewards'
import { unifiedCurrencyService } from '@/lib/unified-currency-service'
import { useCurrency } from '@/lib/currency-context'
import { useRating } from '@/lib/rating-context'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import {
  Shield,
  TrendingUp,
  DollarSign,
  Zap,
  Users,
  Heart,
  Building,
  Coins,
  Star,
  CheckCircle,
  AlertTriangle,
  Info,
  Wrench
} from 'lucide-react'

interface FacilitiesPageContentProps {
  user: User
  profile: Profile | null
}

export default function FacilitiesPageContent({ user, profile }: FacilitiesPageContentProps) {
  const { refreshProfile } = useAuth()
  const { refreshCashBalance } = useCashBalance()
  const { currency, formatBudgetStat } = useCurrency()
  const { refreshRatings } = useRating()
  const [facilityUpgrades, setFacilityUpgrades] = useState<FacilityUpgrade[]>([])
  const [userCurrency, setUserCurrency] = useState<UserCurrency | null>(null)
  const [recommendations, setRecommendations] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [upgrading, setUpgrading] = useState<string | null>(null)

  useEffect(() => {
    if (profile?.team) {
      loadFacilityData()
    }
  }, [profile?.team])

  const loadFacilityData = async () => {
    if (!profile?.team) return

    setIsLoading(true)
    try {
      const [upgrades, currency, recs] = await Promise.all([
        facilitiesService.getFacilityUpgradeInfo(profile.team.id),
        dailyRewardsService.getUserCurrency(user.id),
        facilitiesService.getFacilityRecommendations(profile.team.id)
      ])

      setFacilityUpgrades(upgrades)
      setUserCurrency(currency)
      setRecommendations(recs)
    } catch (error) {
      console.error('Error loading facility data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpgradeFacility = async (facilityType: string) => {
    if (!profile?.team || upgrading) return

    setUpgrading(facilityType)
    try {
      const result = await facilitiesService.upgradeFacility(
        profile.team.id,
        facilityType,
        user.id
      )

      if (result.success) {
        // Reload data to reflect changes
        await loadFacilityData()
        // Refresh the global profile to update cash balance across all components
        await refreshCashBalance()
        // Refresh ratings after facility upgrade
        await refreshRatings()
      } else {
        alert(result.error || 'Upgrade failed')
      }
    } catch (error) {
      console.error('Error upgrading facility:', error)
      alert('Upgrade failed')
    } finally {
      setUpgrading(null)
    }
  }

  const getFacilityIcon = (type: string) => {
    const icons = {
      training_pool: Zap,
      recovery_pool: Heart,
      youth_academy: Users,
      aquatic_arena: Building,
      medical_center: TrendingUp
    }
    return icons[type as keyof typeof icons] || Shield
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-500 bg-red-50 dark:bg-red-900/20'
      case 'medium': return 'text-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
      case 'low': return 'text-green-500 bg-green-50 dark:bg-green-900/20'
      default: return 'text-gray-500 bg-gray-50 dark:bg-gray-900/20'
    }
  }

  if (!profile?.team) {
    return (
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <Card>
            <CardContent className="pt-6 text-center py-12">
              <Shield className="w-12 h-12 mx-auto text-muted mb-4" />
              <h3 className="text-lg font-medium mb-2">No Team Found</h3>
              <p className="text-muted mb-4">
                You need to create a team before you can manage facilities.
              </p>
              <Button onClick={() => window.location.href = '/team-builder'}>
                Create Your Team
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4" />
              <p>Loading facilities...</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-8"
        >
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">Facilities</h1>
              <p className="text-muted mt-2">
                Upgrade your facilities to improve team performance
              </p>
            </div>
            <div className="text-right">
              <div className="flex items-center space-x-2 text-2xl font-bold">
                <Coins className="w-6 h-6 text-yellow-500" />
                <span>{formatBudgetStat().replace('$', '')}</span>
              </div>
              <div className="text-sm text-muted">Available Coins</div>
            </div>
          </div>

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Star className="w-5 h-5 text-yellow-500" />
                  <span>Upgrade Recommendations</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recommendations.slice(0, 3).map((rec, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(rec.priority)}`}>
                          {rec.priority.toUpperCase()}
                        </div>
                        <div>
                          <div className="font-medium capitalize">
                            {rec.facilityType.replace('_', ' ')}
                          </div>
                          <div className="text-sm text-muted">{rec.reason}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Facilities Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {facilityUpgrades.map((upgrade, index) => {
              const Icon = getFacilityIcon(upgrade.facility.facility_type)
              const benefits = facilitiesService.getFacilityBenefits(
                upgrade.facility.facility_type,
                upgrade.currentLevel
              )

              return (
                <motion.div
                  key={upgrade.facility.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="hover:shadow-lg transition-shadow h-full">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Icon className="w-6 h-6 text-primary" />
                          <span className="capitalize">
                            {upgrade.facility.facility_type.replace('_', ' ')}
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold">Level {upgrade.currentLevel}</div>
                          <div className="text-xs text-muted">Max {upgrade.maxLevel}</div>
                        </div>
                      </CardTitle>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      <div className="text-sm text-muted">
                        {upgrade.description}
                      </div>

                      {/* Current Benefits */}
                      <div className="space-y-2">
                        <h4 className="font-medium text-sm">Current Benefits:</h4>
                        <div className="space-y-1">
                          {benefits.map((benefit, i) => (
                            <div key={i} className="flex items-center space-x-2 text-sm">
                              <CheckCircle className="w-3 h-3 text-green-500" />
                              <span>{benefit}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Upgrade Section */}
                      {upgrade.currentLevel < upgrade.maxLevel ? (
                        <div className="border-t pt-4">
                          <div className="flex items-center justify-between mb-3">
                            <span className="font-medium">Upgrade to Level {upgrade.currentLevel + 1}</span>
                            <div className="flex items-center space-x-1">
                              <Coins className="w-4 h-4 text-yellow-500" />
                              <span className="font-bold">{upgrade.upgradeCost.toLocaleString()}</span>
                            </div>
                          </div>

                          <div className="text-sm text-green-600 mb-3">
                            Next: +{upgrade.nextLevelBonus - upgrade.currentBonus}% bonus
                          </div>

                          <Button
                            onClick={() => handleUpgradeFacility(upgrade.facility.facility_type)}
                            disabled={!upgrade.canUpgrade || upgrading === upgrade.facility.facility_type}
                            className="w-full"
                            variant={upgrade.canUpgrade ? "default" : "outline"}
                          >
                            {upgrading === upgrade.facility.facility_type ? (
                              <div className="flex items-center space-x-2">
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                <span>Upgrading...</span>
                              </div>
                            ) : upgrade.canUpgrade ? (
                              <div className="flex items-center space-x-2">
                                <TrendingUp className="w-4 h-4" />
                                <span>Upgrade</span>
                              </div>
                            ) : (
                              <div className="flex items-center space-x-2">
                                <AlertTriangle className="w-4 h-4" />
                                <span>Insufficient Funds</span>
                              </div>
                            )}
                          </Button>
                        </div>
                      ) : (
                        <div className="border-t pt-4 text-center">
                          <div className="flex items-center justify-center space-x-2 text-green-600">
                            <CheckCircle className="w-5 h-5" />
                            <span className="font-medium">Maximum Level Reached</span>
                          </div>
                        </div>
                      )}

                      {/* Maintenance Cost */}
                      <div className="text-xs text-muted border-t pt-2">
                        Daily maintenance: {upgrade.maintenanceCost} coins
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </motion.div>
      </div>
    </div>
  )
}
