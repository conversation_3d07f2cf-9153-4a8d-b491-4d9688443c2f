'use client'

import { Player } from '@/lib/types'
import { getPlayerPositionColor, calculatePlayerOverall } from '@/lib/utils'
import { POSITION_LABELS } from '@/lib/types'
import { TrendingUp, Calendar, Award } from 'lucide-react'

interface PlayerStatsProps {
  player: Player
}

export default function PlayerStats({ player }: PlayerStatsProps) {
  const overall = calculatePlayerOverall(player)
  
  const stats = [
    { label: 'Shooting', value: player.shooting || 0, color: 'bg-red-500' },
    { label: 'Speed', value: player.speed || 0, color: 'bg-blue-500' },
    { label: 'Passing', value: player.passing || 0, color: 'bg-green-500' },
    { label: 'Defense', value: player.defense || 0, color: 'bg-purple-500' },
  ]

  const getStatBarWidth = (value: number) => `${Math.round(value)}%`
  
  const getStatColor = (value: number) => {
    if (value >= 85) return 'text-success'
    if (value >= 75) return 'text-primary'
    if (value >= 65) return 'text-warning'
    return 'text-muted'
  }

  return (
    <div className="space-y-6">
      {/* Player Header */}
      <div className="text-center">
        <div className={`w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto mb-3 ${getPlayerPositionColor(player.position)}`}>
          {player.name.split(' ').map(n => n[0]).join('')}
        </div>
        <h3 className="font-bold text-lg text-foreground">{player.name}</h3>
        <p className="text-muted">{POSITION_LABELS[player.position]}</p>
        <div className={`text-2xl font-bold mt-2 ${getStatColor(overall)}`}>
          {overall} <span className="text-sm text-muted-light">OVR</span>
        </div>
      </div>

      {/* Basic Info */}
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <Calendar className="w-5 h-5 text-gray-600 mx-auto mb-1" />
          <div className="font-semibold text-gray-900">{player.age}</div>
          <div className="text-xs text-gray-600">Age</div>
        </div>
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <TrendingUp className="w-5 h-5 text-gray-600 mx-auto mb-1" />
          <div className="font-semibold text-gray-900">{player.experience.toLocaleString()}</div>
          <div className="text-xs text-gray-600">XP</div>
        </div>
      </div>

      {/* Stats */}
      <div className="space-y-4">
        <h4 className="font-semibold text-gray-900">Player Stats</h4>
        {stats.map((stat) => (
          <div key={stat.label} className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">{stat.label}</span>
              <span className={`font-bold ${getStatColor(stat.value)}`}>{Math.round(stat.value)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${stat.color}`}
                style={{ width: getStatBarWidth(stat.value) }}
              />
            </div>
          </div>
        ))}
      </div>

      {/* Performance Indicators */}
      <div className="space-y-3">
        <h4 className="font-semibold text-gray-900">Performance</h4>
        
        <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
          <span className="text-sm text-gray-600">Potential</span>
          <div className="flex items-center space-x-1">
            {[...Array(5)].map((_, i) => (
              <div
                key={i}
                className={`w-2 h-2 rounded-full ${
                  i < Math.floor(overall / 20) ? 'bg-yellow-400' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>
        </div>
        
        <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
          <span className="text-sm text-gray-600">Form</span>
          <span className="text-sm font-medium text-green-600">Excellent</span>
        </div>
        
        <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
          <span className="text-sm text-gray-600">Fitness</span>
          <span className="text-sm font-medium text-blue-600">100%</span>
        </div>
      </div>

      {/* Strengths & Weaknesses */}
      <div className="space-y-3">
        <h4 className="font-semibold text-gray-900">Analysis</h4>
        
        <div>
          <div className="text-sm font-medium text-green-600 mb-1">Strengths</div>
          <div className="text-xs text-gray-600">
            {stats
              .filter(s => s.value >= 80)
              .map(s => s.label)
              .join(', ') || 'Developing player'}
          </div>
        </div>
        
        <div>
          <div className="text-sm font-medium text-red-600 mb-1">Areas to Improve</div>
          <div className="text-xs text-gray-600">
            {stats
              .filter(s => s.value < 70)
              .map(s => s.label)
              .join(', ') || 'Well-rounded player'}
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="space-y-2 pt-4 border-t">
        <button className="w-full bg-primary text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-primary-dark transition-colors">
          Train Player
        </button>
        <button className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
          View Details
        </button>
      </div>
    </div>
  )
}
