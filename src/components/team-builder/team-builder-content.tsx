'use client'

import React, { useState, useEffect } from 'react'

import { User } from '@supabase/supabase-js'
import { Profile, Player, PlayerPosition, DEFAULT_FORMATION } from '@/lib/types'
import { defaultTeamService } from '@/lib/default-team-service'
import { calculateRatingsFromPlayers, calculateTeamRatingFromTeam, getRatingColor } from '@/lib/rating-utils'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import FormationField from '@/components/team-builder/formation-field'
import PlayerList from '@/components/team-builder/player-list'
import PlayerStats from '@/components/team-builder/player-stats'
import {
  Save,
  RotateCcw,
  Users,
  Target,
  Plus,
  Settings,
  TrendingUp,
  Shield,
  Zap,
  Activity
} from 'lucide-react'

interface TeamBuilderContentProps {
  user: User
  profile: Profile | null
}

export default function TeamBuilderContent({ user, profile }: Team<PERSON>uilderContentProps) {
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null)
  const [formation, setFormation] = useState(DEFAULT_FORMATION)
  const [players, setPlayers] = useState<Player[]>(profile?.team?.players || [])
  const [isCreatingTeam, setIsCreatingTeam] = useState(false)

  // Use profile team players for consistency with rating context
  const displayPlayers = profile?.team?.players || players

  // Calculate ratings directly from players
  const { avgRating } = calculateRatingsFromPlayers(displayPlayers)

  // Calculate team rating from profile team data
  const teamRating = profile?.team ? calculateTeamRatingFromTeam(profile.team) : 0

  // Update local players when profile changes
  useEffect(() => {
    if (profile?.team?.players) {
      setPlayers(profile.team.players)
    }
  }, [profile?.team?.players])

  const handleCreateDefaultTeam = async () => {
    setIsCreatingTeam(true)
    try {
      const result = await defaultTeamService.createDefaultTeam(user.id)
      if (result.success) {
        // Refresh the page to load the new team
        window.location.reload()
      } else {
        alert(result.error || 'Failed to create team')
      }
    } catch (error) {
      console.error('Error creating default team:', error)
      alert('Failed to create team')
    } finally {
      setIsCreatingTeam(false)
    }
  }

  const handlePlayerSelect = (player: Player) => {
    setSelectedPlayer(player)
  }

  const handleSaveFormation = () => {
    // TODO: Implement save functionality
    console.log('Saving formation:', formation)
  }

  const handleResetFormation = () => {
    setFormation(DEFAULT_FORMATION)
  }



  return (
    <div className="min-h-screen bg-muted-bg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="relative">
            <div className="bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 rounded-2xl p-8 border border-primary/20">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-foreground mb-2 flex items-center gap-3">
                    <Target className="w-8 h-8 text-primary" />
                    Team Builder
                  </h1>
                  <p className="text-muted text-lg">
                    Create your perfect formation and manage your squad
                  </p>
                </div>
                <div className="flex space-x-4 mt-6 md:mt-0">
                  <Button
                    variant="outline"
                    onClick={handleResetFormation}
                    className="hover:bg-red-50 hover:border-red-300 hover:text-red-600 transition-colors duration-200"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    Reset
                  </Button>
                  <Button
                    onClick={handleSaveFormation}
                    className="bg-gradient-to-r from-primary to-secondary hover:from-primary-dark hover:to-secondary-dark"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Save Formation
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Team Stats */}
          {displayPlayers.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted">Squad Size</p>
                      <p className="text-2xl font-bold text-foreground">{displayPlayers.length}/14</p>
                    </div>
                    <Users className="w-8 h-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted">Avg Rating</p>
                      <p className={`text-2xl font-bold ${getRatingColor(avgRating)}`}>
                        {avgRating}
                      </p>
                    </div>
                    <TrendingUp className="w-8 h-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted">Formation</p>
                      <p className="text-2xl font-bold text-foreground">{formation.name}</p>
                    </div>
                    <Target className="w-8 h-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted">Team Morale</p>
                      <p className="text-2xl font-bold text-foreground">
                        {displayPlayers.length > 0 ? Math.round(displayPlayers.reduce((sum, p) => sum + p.morale, 0) / displayPlayers.length) : 0}
                      </p>
                    </div>
                    <Activity className="w-8 h-8 text-yellow-500" />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Formation Field */}
            <div className="lg:col-span-2">
              <Card variant="elevated" hover>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      Formation: {formation.name}
                    </span>
                    <div className="flex items-center gap-2 text-sm text-muted">
                      <Shield className="w-4 h-4" />
                      Tactical View
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <FormationField
                      formation={formation}
                      players={displayPlayers}
                      onPlayerSelect={handlePlayerSelect}
                      selectedPlayer={selectedPlayer}
                    />
                    {displayPlayers.length === 0 && (
                      <div className="absolute inset-0 bg-muted-bg/80 backdrop-blur-sm rounded-lg flex items-center justify-center">
                        <div className="text-center">
                          <Target className="w-12 h-12 text-muted mx-auto mb-4" />
                          <p className="text-muted font-medium">Create a team to see your formation</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Player List */}
            <div className="lg:col-span-1">
              <Card variant="elevated" className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center gap-2">
                      <Users className="w-5 h-5" />
                      Squad ({displayPlayers.length}/14)
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      className="hover:bg-green-50 hover:border-green-300 hover:text-green-600 transition-colors duration-200"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add Player
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {displayPlayers.length === 0 ? (
                    <div className="text-center py-8">
                      <Users className="w-12 h-12 text-muted mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Players Yet</h3>
                      <p className="text-muted mb-4">
                        Start with our default roster of 10 talented players!
                      </p>
                      <Button
                        onClick={handleCreateDefaultTeam}
                        disabled={isCreatingTeam}
                        className="w-full"
                      >
                        {isCreatingTeam ? 'Creating Team...' : 'Create Default Team'}
                      </Button>
                    </div>
                  ) : (
                    <PlayerList
                      players={displayPlayers}
                      onPlayerSelect={handlePlayerSelect}
                      selectedPlayer={selectedPlayer}
                    />
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Player Stats */}
            <div className="lg:col-span-1">
              <Card variant="elevated" className="h-full">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Player Details
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedPlayer ? (
                    <PlayerStats player={selectedPlayer} />
                  ) : (
                    <div className="text-center py-8">
                      <Users className="w-12 h-12 text-muted mx-auto mb-4" />
                      <p className="text-muted">
                        Select a player from your squad to view their detailed stats and information
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Team Statistics */}
          {displayPlayers.length > 0 && (
            <div>
              <Card variant="gradient" hover>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Team Performance Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                    {[
                      {
                        label: 'Team Rating',
                        value: teamRating.toString(),
                        icon: <TrendingUp className="w-5 h-5" />,
                        color: getRatingColor(teamRating),
                        bgColor: 'bg-primary/10'
                      },
                      {
                        label: 'Avg Shooting',
                        value: Math.round(displayPlayers.reduce((acc, p) => acc + p.shooting, 0) / displayPlayers.length),
                        icon: <Target className="w-5 h-5" />,
                        color: 'text-green-600',
                        bgColor: 'bg-green-100'
                      },
                      {
                        label: 'Avg Speed',
                        value: Math.round(displayPlayers.reduce((acc, p) => acc + p.speed, 0) / displayPlayers.length),
                        icon: <Zap className="w-5 h-5" />,
                        color: 'text-blue-600',
                        bgColor: 'bg-blue-100'
                      },
                      {
                        label: 'Avg Defense',
                        value: Math.round(displayPlayers.reduce((acc, p) => acc + p.defense, 0) / displayPlayers.length),
                        icon: <Shield className="w-5 h-5" />,
                        color: 'text-purple-600',
                        bgColor: 'bg-purple-100'
                      }
                    ].map((stat, index) => (
                      <div
                        key={stat.label}
                        className="text-center p-4 rounded-xl border border-card-border hover:shadow-md transition-all duration-200"
                      >
                        <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full ${stat.bgColor} mb-3`}>
                          <div className={stat.color}>
                            {stat.icon}
                          </div>
                        </div>
                        <div className={`text-2xl font-bold ${stat.color} mb-1`}>
                          {stat.value}
                        </div>
                        <div className="text-sm text-muted font-medium">{stat.label}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
