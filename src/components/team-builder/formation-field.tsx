'use client'

import { Player, Formation, PlayerPosition } from '@/lib/types'
import { getPlayerPositionColor } from '@/lib/utils'

interface FormationFieldProps {
  formation: Formation
  players: Player[]
  onPlayerSelect: (player: Player) => void
  selectedPlayer: Player | null
}

export default function FormationField({ 
  formation, 
  players, 
  onPlayerSelect, 
  selectedPlayer 
}: FormationFieldProps) {
  const getPlayerForPosition = (position: PlayerPosition) => {
    return players.find(p => p.position === position)
  }

  const renderPlayer = (position: PlayerPosition, x: number, y: number) => {
    const player = getPlayerForPosition(position)
    const isSelected = selectedPlayer?.id === player?.id
    
    return (
      <div
        key={position}
        className={`absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer transition-all duration-200 ${
          isSelected ? 'scale-110 z-10' : 'hover:scale-105'
        }`}
        style={{ left: `${x}%`, top: `${y}%` }}
        onClick={() => player && onPlayerSelect(player)}
      >
        <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-semibold text-sm shadow-lg ${
          isSelected ? 'ring-4 ring-yellow-400' : ''
        } ${getPlayerPositionColor(position)}`}>
          {player ? player.name.split(' ').map(n => n[0]).join('') : position.charAt(0).toUpperCase()}
        </div>
        <div className="text-center mt-1">
          <div className="text-xs font-medium text-foreground truncate max-w-16">
            {player ? player.name.split(' ')[0] : position.split('-')[0]}
          </div>
          {player && (
            <div className="text-xs text-muted-light">
              {Math.round((player.shooting + player.speed + player.passing + player.defense) / 4)}
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className="relative w-full h-96 bg-gradient-to-b from-blue-400 to-blue-600 rounded-lg overflow-hidden">
      {/* Pool background pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `repeating-linear-gradient(
            0deg,
            transparent,
            transparent 35px,
            rgba(255,255,255,0.1) 35px,
            rgba(255,255,255,0.1) 37px
          )`
        }} />
      </div>
      
      {/* Pool lanes */}
      <div className="absolute inset-0">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute h-full border-l border-white/20"
            style={{ left: `${(i + 1) * 12.5}%` }}
          />
        ))}
      </div>

      {/* Goals */}
      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-4 h-20 bg-white/30 rounded-r-lg" />
      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-4 h-20 bg-white/30 rounded-l-lg" />

      {/* Center line */}
      <div className="absolute left-1/2 top-0 bottom-0 w-0.5 bg-white/40 transform -translate-x-1/2" />

      {/* Players */}
      {Object.entries(formation.positions).map(([position, coords]) => 
        renderPlayer(position as PlayerPosition, coords.x, coords.y)
      )}

      {/* Formation info overlay */}
      <div className="absolute top-4 left-4 bg-black/20 backdrop-blur-sm rounded-lg px-3 py-2">
        <div className="text-white text-sm font-medium">{formation.name}</div>
        <div className="text-white/80 text-xs">7v7 Formation</div>
      </div>

      {/* Legend */}
      <div className="absolute bottom-4 right-4 bg-black/20 backdrop-blur-sm rounded-lg px-3 py-2">
        <div className="text-white text-xs space-y-1">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-500 rounded-full" />
            <span>Goalkeeper</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full" />
            <span>Wings</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full" />
            <span>Drivers</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full" />
            <span>Center</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-purple-500 rounded-full" />
            <span>Point</span>
          </div>
        </div>
      </div>
    </div>
  )
}
