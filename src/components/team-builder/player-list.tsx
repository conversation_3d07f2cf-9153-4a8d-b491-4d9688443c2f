'use client'

import { Player } from '@/lib/types'
import { getPlayerPositionColor, calculatePlayerOverall } from '@/lib/utils'
import { POSITION_LABELS } from '@/lib/types'

interface PlayerListProps {
  players: Player[]
  onPlayerSelect: (player: Player) => void
  selectedPlayer: Player | null
}

export default function PlayerList({ players, onPlayerSelect, selectedPlayer }: PlayerListProps) {
  const sortedPlayers = [...players].sort((a, b) => {
    // Sort by position order, then by overall rating
    const positionOrder = ['goalkeeper', 'left-wing', 'right-wing', 'left-driver', 'right-driver', 'center-forward', 'point']
    const aIndex = positionOrder.indexOf(a.position)
    const bIndex = positionOrder.indexOf(b.position)
    
    if (aIndex !== bIndex) {
      return aIndex - bIndex
    }
    
    return calculatePlayerOverall(b) - calculatePlayerOverall(a)
  })

  return (
    <div className="space-y-2 max-h-96 overflow-y-auto">
      {sortedPlayers.map((player) => {
        const isSelected = selectedPlayer?.id === player.id
        const overall = calculatePlayerOverall(player)
        
        return (
          <div
            key={player.id}
            className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 ${
              isSelected
                ? 'border-primary bg-primary/5 shadow-md'
                : 'border-card-border hover:border-primary/50 hover:bg-hover-bg'
            }`}
            onClick={() => onPlayerSelect(player)}
          >
            <div className="flex items-center space-x-3">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold text-sm ${getPlayerPositionColor(player.position)}`}>
                {player.name.split(' ').map(n => n[0]).join('')}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="font-medium text-foreground truncate">
                  {player.name}
                </div>
                <div className="text-sm text-muted">
                  {POSITION_LABELS[player.position]} • Age {player.age}
                </div>
              </div>
              
              <div className="text-right">
                <div className={`text-lg font-bold ${
                  overall >= 85 ? 'text-success' :
                  overall >= 75 ? 'text-primary' :
                  overall >= 65 ? 'text-warning' :
                  'text-muted'
                }`}>
                  {overall}
                </div>
                <div className="text-xs text-muted-light">OVR</div>
              </div>
            </div>
            
            {/* Quick stats bar */}
            <div className="mt-2 grid grid-cols-4 gap-1">
              <div className="text-center">
                <div className="text-xs font-medium text-foreground">{player.shooting}</div>
                <div className="text-xs text-muted-light">SHO</div>
              </div>
              <div className="text-center">
                <div className="text-xs font-medium text-foreground">{player.speed}</div>
                <div className="text-xs text-muted-light">SPD</div>
              </div>
              <div className="text-center">
                <div className="text-xs font-medium text-foreground">{player.passing}</div>
                <div className="text-xs text-muted-light">PAS</div>
              </div>
              <div className="text-center">
                <div className="text-xs font-medium text-foreground">{player.defense}</div>
                <div className="text-xs text-muted-light">DEF</div>
              </div>
            </div>
          </div>
        )
      })}
      
      {players.length === 0 && (
        <div className="text-center py-8">
          <div className="text-muted-light mb-2">No players in squad</div>
          <div className="text-sm text-muted">Add players to start building your team</div>
        </div>
      )}
    </div>
  )
}
