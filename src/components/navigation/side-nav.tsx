'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Trophy, 
  Target, 
  Zap, 
  Users, 
  Shield,
  ChevronLeft,
  ChevronRight,
  Home,
  Settings,
  DollarSign
} from 'lucide-react'
import { unifiedCurrencyService } from '@/lib/unified-currency-service'
import { useCurrency } from '@/lib/currency-context'
import { calculateRatingsFromPlayers, getRatingColor } from '@/lib/rating-utils'

export default function SideNav() {
  const [isCollapsed, setIsCollapsed] = useState(true) // Start collapsed
  const [isHovered, setIsHovered] = useState(false)
  const { user, profile } = useAuth()
  const { formatBudgetStat, formatBudgetSideNav } = useCurrency()

  // Calculate average rating directly
  const players = profile?.team?.players || []
  const { avgRating } = calculateRatingsFromPlayers(players)
  const pathname = usePathname()

  // Only show side nav for authenticated users
  if (!user) return null

  // Determine if sidebar should be expanded (either pinned open or hovered)
  const isExpanded = !isCollapsed || isHovered

  const gameNavigation = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Team Builder', href: '/team-builder', icon: Target },
    { name: 'Training', href: '/training', icon: Zap },
    { name: 'Transfers', href: '/transfers', icon: Users },
    { name: 'League', href: '/league', icon: Trophy },
    { name: 'Facilities', href: '/facilities', icon: Shield },
  ]

  const isActive = (href: string) => pathname === href

  return (
    <motion.div
      initial={{ x: -280 }}
      animate={{ x: 0 }}
      className={`fixed left-0 top-16 h-[calc(100vh-4rem)] bg-card-bg border-r border-card-border z-40 transition-all duration-300 ${
        isExpanded ? 'w-64' : 'w-16'
      } hidden md:block`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >


      {/* Toggle Button */}
      <div className="p-2 pr-4">
        <div className="flex items-center justify-end">
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-1 h-8 w-8 bg-transparent hover:bg-hover-bg rounded-lg transition-colors"
            title={isCollapsed ? "Pin sidebar open" : "Pin sidebar closed"}
          >
            {isCollapsed ? (
              <ChevronRight className="w-4 h-4" />
            ) : (
              <ChevronLeft className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      <div className="flex flex-col h-full">
        {/* User Info */}
        <div className={`p-4 ${isExpanded ? '' : 'px-2'}`}>
          {isExpanded ? (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-sm">
                  {profile?.full_name?.charAt(0) || 'M'}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-foreground truncate">
                  {profile?.full_name || 'Manager'}
                </div>
                <div className="text-sm text-muted">
                  Level {profile?.manager_level || 1}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex justify-center">
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-xs">
                  {profile?.full_name?.charAt(0) || 'M'}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Team Info */}
        {profile?.team && (
          <div className={`p-4 ${isExpanded ? '' : 'px-2'}`}>
            {isExpanded ? (
              <div>
                <div className="text-sm text-muted mb-1">Your Team</div>
                <div className="font-medium text-foreground truncate">
                  {profile.team.name}
                </div>
                <div className="flex items-center justify-between mt-2 text-sm">
                  <span className="text-muted">Cash:</span>
                  <span className="font-medium">{formatBudgetStat()}</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted">Rating:</span>
                  <span className={`font-medium ${getRatingColor(avgRating)}`}>{avgRating}</span>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center space-y-1">
                <DollarSign className="w-4 h-4 text-muted" />
                <span className="text-xs font-medium">{formatBudgetSideNav(true)}</span>
              </div>
            )}
          </div>
        )}

        {/* Navigation Links */}
        <nav className="flex-1 p-2">
          <div className="space-y-1">
            {gameNavigation.map((item) => {
              const active = isActive(item.href)
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`relative flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 group ${
                    active
                      ? 'bg-primary text-white shadow-sm'
                      : 'text-foreground hover:bg-hover-bg hover:text-primary'
                  } ${isExpanded ? '' : 'justify-center'}`}
                >
                  <item.icon className={`w-5 h-5 ${active ? 'text-white' : ''}`} />
                  
                  <AnimatePresence>
                    {isExpanded && (
                      <motion.span
                        initial={{ opacity: 0, width: 0 }}
                        animate={{ opacity: 1, width: 'auto' }}
                        exit={{ opacity: 0, width: 0 }}
                        className="font-medium whitespace-nowrap"
                      >
                        {item.name}
                      </motion.span>
                    )}
                  </AnimatePresence>

                  {/* Tooltip for collapsed state */}
                  {!isExpanded && (
                    <div className="absolute left-16 bg-gray-900 text-white px-2 py-1 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                      {item.name}
                    </div>
                  )}
                </Link>
              )
            })}
          </div>
        </nav>

        {/* Settings */}
        <div className="p-2">
          <Link
            href="/settings"
            className={`relative flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 text-foreground hover:bg-hover-bg hover:text-primary group ${
              isExpanded ? '' : 'justify-center'
            }`}
          >
            <Settings className="w-5 h-5" />
            
            <AnimatePresence>
              {isExpanded && (
                <motion.span
                  initial={{ opacity: 0, width: 0 }}
                  animate={{ opacity: 1, width: 'auto' }}
                  exit={{ opacity: 0, width: 0 }}
                  className="font-medium whitespace-nowrap"
                >
                  Settings
                </motion.span>
              )}
            </AnimatePresence>

            {/* Tooltip for collapsed state */}
            {!isExpanded && (
              <div className="absolute left-16 bg-gray-900 text-white px-2 py-1 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50">
                Settings
              </div>
            )}
          </Link>
        </div>
      </div>
    </motion.div>
  )
}
