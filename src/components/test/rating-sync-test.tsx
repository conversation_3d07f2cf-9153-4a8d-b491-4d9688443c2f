'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/auth-context'
import { useRating } from '@/lib/rating-context'
import { ratingService } from '@/lib/rating-service'
import { calculatePlayerOverall } from '@/lib/game-engine'
import { calculateTeamRating } from '@/lib/progression-system'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { TrendingUp, RefreshCw, Users, Target } from 'lucide-react'

export default function RatingSyncTest() {
  const { user } = useAuth()
  const { ratings, refreshRatings, formatRatingStat, getRatingColor, getRatingDescription } = useRating()
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const handleRefreshRatings = async () => {
    if (!user?.id) return
    
    setIsRefreshing(true)
    try {
      await refreshRatings()
    } catch (error) {
      console.error('Error refreshing ratings:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleRecalculateRatings = async () => {
    if (!user?.id) return

    setIsRefreshing(true)
    try {
      await ratingService.updateTeamRating(user.id)
    } catch (error) {
      console.error('Error recalculating ratings:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleDebugTeamData = async () => {
    if (!user?.id) return

    console.log('🔍 Debug: Fetching team data...')
    const team = await ratingService.getTeamWithPlayers(user.id)
    console.log('🔍 Debug: Team data:', team)

    if (team?.players) {
      console.log('🔍 Debug: Players:', team.players.length)
      team.players.forEach((player, index) => {
        const overall = calculatePlayerOverall(player)
        console.log(`🔍 Debug: Player ${index + 1}:`, {
          name: player.name,
          position: player.position,
          shooting: player.shooting,
          speed: player.speed,
          passing: player.passing,
          defense: player.defense,
          endurance: player.endurance,
          awareness: player.awareness,
          goalkeeping: player.goalkeeping,
          morale: player.morale,
          age: player.age,
          overall: overall
        })
      })

      // Test team rating calculation
      const teamRating = calculateTeamRating(team)
      console.log('🔍 Debug: Calculated team rating:', teamRating)
      console.log('🔍 Debug: Database team rating:', team.team_rating)
    }
  }

  return (
    <Card className="border-orange-200 bg-orange-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-800">
          <TrendingUp className="w-5 h-5" />
          Rating Sync Test (Dev Only)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Ratings Display */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-3 bg-white rounded-lg border">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Target className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-gray-600">Team Rating</span>
            </div>
            <div className={`text-2xl font-bold ${getRatingColor('team')}`}>
              {formatRatingStat('team')}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {getRatingDescription('team')}
            </div>
          </div>
          
          <div className="text-center p-3 bg-white rounded-lg border">
            <div className="flex items-center justify-center gap-2 mb-2">
              <TrendingUp className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-gray-600">Avg Rating</span>
            </div>
            <div className={`text-2xl font-bold ${getRatingColor('avg')}`}>
              {formatRatingStat('avg')}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {getRatingDescription('avg')}
            </div>
          </div>
          
          <div className="text-center p-3 bg-white rounded-lg border">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Users className="w-4 h-4 text-purple-600" />
              <span className="text-sm font-medium text-gray-600">Players</span>
            </div>
            <div className="text-2xl font-bold text-purple-600">
              {ratings?.playerCount || 0}
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Squad Size
            </div>
          </div>
        </div>

        {/* Test Actions */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={handleRefreshRatings}
            disabled={isRefreshing}
            size="sm"
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh Ratings
          </Button>
          
          <Button
            onClick={handleRecalculateRatings}
            disabled={isRefreshing}
            size="sm"
            variant="outline"
            className="flex items-center gap-2"
          >
            <TrendingUp className="w-4 h-4" />
            Recalculate
          </Button>

          <Button
            onClick={handleDebugTeamData}
            disabled={isRefreshing}
            size="sm"
            variant="outline"
            className="flex items-center gap-2"
          >
            <Users className="w-4 h-4" />
            Debug Team
          </Button>
        </div>

        {/* Debug Info */}
        <div className="text-xs text-gray-500 bg-white p-2 rounded border">
          <div className="font-medium mb-1">Debug Info:</div>
          <div>Team Rating: {ratings?.teamRating || 'N/A'}</div>
          <div>Avg Rating: {ratings?.avgRating || 'N/A'}</div>
          <div>Player Count: {ratings?.playerCount || 'N/A'}</div>
          <div>User ID: {user?.id || 'N/A'}</div>
        </div>

        <div className="text-xs text-orange-600">
          This component helps test real-time rating synchronization across all pages.
          Changes should appear instantly in the header, dashboard, team builder, and side navigation.
        </div>
      </CardContent>
    </Card>
  )
}
