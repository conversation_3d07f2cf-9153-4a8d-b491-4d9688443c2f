'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/auth-context'
import { useCurrency } from '@/lib/currency-context'
import { unifiedCurrencyService } from '@/lib/unified-currency-service'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Button from '@/components/ui/button'
import { DollarSign, Plus, Minus, RefreshCw } from 'lucide-react'

/**
 * Test component to verify real-time currency synchronization
 * This component allows testing currency changes and verifies that
 * all displays update simultaneously across the application
 */
export default function CurrencySyncTest() {
  const { user } = useAuth()
  const { currency, refreshCurrency, formatBudgetStat, formatBudgetHeader } = useCurrency()
  const [isLoading, setIsLoading] = useState(false)
  const [lastAction, setLastAction] = useState<string>('')

  if (!user) return null

  const testAwardCoins = async (amount: number) => {
    setIsLoading(true)
    setLastAction(`Adding ${amount} coins...`)
    
    try {
      const result = await unifiedCurrencyService.awardCoins(
        user.id,
        amount,
        `Test award: +${amount} coins`,
        'test'
      )
      
      if (result.success) {
        setLastAction(`✅ Successfully added ${amount} coins`)
      } else {
        setLastAction(`❌ Failed to add coins: ${result.error}`)
      }
    } catch (error) {
      setLastAction(`❌ Error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testSpendCoins = async (amount: number) => {
    setIsLoading(true)
    setLastAction(`Spending ${amount} coins...`)
    
    try {
      const result = await unifiedCurrencyService.spendCoins(
        user.id,
        amount,
        `Test spend: -${amount} coins`,
        'test'
      )
      
      if (result.success) {
        setLastAction(`✅ Successfully spent ${amount} coins`)
      } else {
        setLastAction(`❌ Failed to spend coins: ${result.error}`)
      }
    } catch (error) {
      setLastAction(`❌ Error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testRefresh = async () => {
    setIsLoading(true)
    setLastAction('Refreshing currency...')
    
    try {
      await refreshCurrency()
      setLastAction('✅ Currency refreshed')
    } catch (error) {
      setLastAction(`❌ Refresh error: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <DollarSign className="w-5 h-5" />
          <span>Currency Sync Test</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Currency Display */}
        <div className="bg-muted p-3 rounded-lg">
          <div className="text-sm text-muted-foreground mb-1">Current Balance:</div>
          <div className="text-lg font-bold">{formatBudgetStat()}</div>
          <div className="text-xs text-muted-foreground mt-1">
            Raw amount: {currency?.coins || 0}
          </div>
        </div>

        {/* Test Actions */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            onClick={() => testAwardCoins(1000)}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <Plus className="w-4 h-4 mr-1" />
            +1,000
          </Button>
          
          <Button
            onClick={() => testSpendCoins(500)}
            disabled={isLoading || (currency?.coins || 0) < 500}
            variant="outline"
            size="sm"
          >
            <Minus className="w-4 h-4 mr-1" />
            -500
          </Button>
          
          <Button
            onClick={() => testAwardCoins(5000)}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <Plus className="w-4 h-4 mr-1" />
            +5,000
          </Button>
          
          <Button
            onClick={testRefresh}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            <RefreshCw className="w-4 h-4 mr-1" />
            Refresh
          </Button>
        </div>

        {/* Last Action Status */}
        {lastAction && (
          <div className="bg-muted p-2 rounded text-sm">
            <div className="font-medium">Last Action:</div>
            <div>{lastAction}</div>
          </div>
        )}

        {/* Instructions */}
        <div className="text-xs text-muted-foreground border-t pt-3">
          <strong>Test Instructions:</strong>
          <ul className="mt-1 space-y-1">
            <li>• Use buttons above to change currency</li>
            <li>• Check that header bar updates instantly</li>
            <li>• Navigate to other pages (dashboard, training, etc.)</li>
            <li>• Verify all budget displays show same amount</li>
            <li>• Test daily rewards for additional sync verification</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
