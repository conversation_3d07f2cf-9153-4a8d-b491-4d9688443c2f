'use client'

import Navbar from '@/components/navigation/navbar'
import Footer from '@/components/navigation/footer'
import SideNav from '@/components/navigation/side-nav'
import { useAuth } from '@/lib/auth-context'

interface MainLayoutProps {
  children: React.ReactNode
  showFooter?: boolean
}

export default function MainLayout({ children, showFooter = true }: MainLayoutProps) {
  const { user } = useAuth()

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex flex-1">
        <SideNav />
        <main className={`flex-1 transition-all duration-300 ${user ? 'md:ml-16' : ''}`}>
          {children}
        </main>
      </div>
      {showFooter && <Footer />}
    </div>
  )
}
