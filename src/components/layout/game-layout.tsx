'use client'

import { useAuth } from '@/lib/auth-context'
import { useCurrency } from '@/lib/currency-context'
import { calculateRatingsFromPlayers, getRatingColor } from '@/lib/rating-utils'
import Navbar from '@/components/navigation/navbar'
import SideNav from '@/components/navigation/side-nav'

interface GameLayoutProps {
  children: React.ReactNode
}

export default function GameLayout({ children }: GameLayoutProps) {
  const { user, profile } = useAuth()
  const { formatBudgetHeader } = useCurrency()

  // Calculate average rating directly
  const players = profile?.team?.players || []
  const { avgRating } = calculateRatingsFromPlayers(players)

  return (
    <div className="min-h-screen bg-muted-bg flex flex-col">
      <Navbar />
      <div className="flex flex-1">
        <SideNav />
        <main className={`flex-1 transition-all duration-300 bg-muted-bg ${user ? 'md:ml-16' : ''}`}>
          {/* Top Bar */}
          <div className="px-6 py-4 flex items-center justify-between bg-card-bg border-b border-card-border">
            <div className="flex items-center space-x-4">
              {profile?.team && (
                <div>
                  <p className="text-sm text-muted">
                    {formatBudgetHeader(profile.team.name)}
                  </p>
                </div>
              )}
            </div>

            {/* Quick Stats */}
            {profile?.team && (
              <div className="hidden md:flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <div className="font-semibold text-foreground">
                    {profile.team.league_position || '-'}
                  </div>
                  <div className="text-muted">Position</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-foreground">
                    {profile.team.league_points || 0}
                  </div>
                  <div className="text-muted">Points</div>
                </div>
                <div className="text-center">
                  <div className={`font-semibold ${getRatingColor(avgRating)}`}>
                    {avgRating}
                  </div>
                  <div className="text-muted">Rating</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-foreground">
                    {profile.manager_level || 1}
                  </div>
                  <div className="text-muted">Level</div>
                </div>
              </div>
            )}
          </div>

          {/* Page Content */}
          <div className="flex-1 overflow-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
