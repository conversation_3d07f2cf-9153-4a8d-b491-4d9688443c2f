'use client'

import { useCallback, useState, useEffect } from 'react'
import { useAuth } from '@/lib/auth-context'
import { unifiedCurrencyService } from '@/lib/unified-currency-service'

/**
 * Custom hook for managing unified user currency
 * Provides utilities to refresh currency data after cash-changing operations
 */
export function useCashBalance() {
  const { user, profile, refreshProfile } = useAuth()
  const [currentCash, setCurrentCash] = useState(0)

  // Get current cash from user_currencies instead of team.cash_balance
  useEffect(() => {
    const fetchCurrency = async () => {
      if (user) {
        const currency = await unifiedCurrencyService.getUserCurrency(user.id)
        setCurrentCash(currency?.coins || 0)
      }
    }
    fetchCurrency()
  }, [user, profile]) // Refresh when profile changes

  /**
   * Call this after any operation that changes user currency
   * (training, transfers, facility upgrades, etc.)
   */
  const refreshCashBalance = useCallback(async () => {
    try {
      await refreshProfile()
      // Also refresh local currency state
      if (user) {
        const currency = await unifiedCurrencyService.getUserCurrency(user.id)
        setCurrentCash(currency?.coins || 0)
      }
    } catch (error) {
      console.error('Error refreshing cash balance:', error)
    }
  }, [refreshProfile, user])

  /**
   * Check if team has sufficient funds for a purchase
   */
  const hasSufficientFunds = useCallback((amount: number) => {
    return currentCash >= amount
  }, [currentCash])

  /**
   * Format cash amount for display
   */
  const formatCash = useCallback((amount: number) => {
    return `$${amount.toLocaleString()}`
  }, [])

  return {
    currentCash,
    refreshCashBalance,
    hasSufficientFunds,
    formatCash,
    teamName: profile?.team?.name || 'Your Team'
  }
}
