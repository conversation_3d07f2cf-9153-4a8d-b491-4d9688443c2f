/**
 * Currency Migration Script
 * Run this script to migrate from team.cash_balance to unified user_currencies system
 */

import { createClient } from '@/lib/supabase-client'

async function migrateCurrency() {
  const supabase = createClient()
  
  console.log('Starting currency migration...')
  
  try {
    // Step 1: Get all teams with their cash balances
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('id, user_id, cash_balance')
    
    if (teamsError) {
      console.error('Error fetching teams:', teamsError)
      return
    }
    
    console.log(`Found ${teams?.length || 0} teams`)
    
    // Step 2: For each team, ensure user has currency record
    for (const team of teams || []) {
      console.log(`Processing team ${team.id} for user ${team.user_id}`)
      
      // Check if user already has currency
      const { data: existingCurrency } = await supabase
        .from('user_currencies')
        .select('*')
        .eq('user_id', team.user_id)
        .single()
      
      if (existingCurrency) {
        // User has currency, add team cash to it
        const newCoins = existingCurrency.coins + (team.cash_balance || 0)
        
        const { error: updateError } = await supabase
          .from('user_currencies')
          .update({ coins: newCoins })
          .eq('user_id', team.user_id)
        
        if (updateError) {
          console.error(`Error updating currency for user ${team.user_id}:`, updateError)
        } else {
          console.log(`Updated user ${team.user_id}: ${existingCurrency.coins} + ${team.cash_balance} = ${newCoins}`)
        }
      } else {
        // User doesn't have currency, create it
        const { error: insertError } = await supabase
          .from('user_currencies')
          .insert({
            user_id: team.user_id,
            coins: Math.max(team.cash_balance || 0, 50000), // Ensure at least 50k
            tokens: 0,
            boosters: { xp_boost: 0, recovery_boost: 0, injury_heal: 0 }
          })
        
        if (insertError) {
          console.error(`Error creating currency for user ${team.user_id}:`, insertError)
        } else {
          console.log(`Created currency for user ${team.user_id}: ${Math.max(team.cash_balance || 0, 50000)} coins`)
        }
      }
    }
    
    // Step 3: Ensure all users have at least 50k coins
    const { error: updateMinError } = await supabase
      .from('user_currencies')
      .update({ coins: 50000 })
      .lt('coins', 50000)
    
    if (updateMinError) {
      console.error('Error updating minimum coins:', updateMinError)
    } else {
      console.log('Ensured all users have at least 50,000 coins')
    }
    
    console.log('Currency migration completed successfully!')
    
    // Step 4: Show summary
    const { data: currencySummary } = await supabase
      .from('user_currencies')
      .select('coins')
    
    if (currencySummary) {
      const totalUsers = currencySummary.length
      const totalCoins = currencySummary.reduce((sum, c) => sum + c.coins, 0)
      const avgCoins = totalCoins / totalUsers
      
      console.log(`Migration Summary:`)
      console.log(`- Total users with currency: ${totalUsers}`)
      console.log(`- Total coins in system: ${totalCoins.toLocaleString()}`)
      console.log(`- Average coins per user: ${Math.round(avgCoins).toLocaleString()}`)
    }
    
  } catch (error) {
    console.error('Migration failed:', error)
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateCurrency()
}

export { migrateCurrency }
