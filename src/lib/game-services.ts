import { createClient } from '@/lib/supabase-client'
import { 
  Team, 
  Player, 
  League, 
  Match, 
  TrainingSession, 
  TransferListing, 
  Facility,
  Profile
} from './types'
import { 
  simulateMatch, 
  generateAITactics, 
  calculatePlayerPerformances 
} from './match-engine'
import { 
  generateLeagueFixtures, 
  simulateLeagueMatchday, 
  calculatePromotionRelegation 
} from './league-system'
import { 
  calculateTrainingGains, 
  applyTrainingToPlayer, 
  calculateFatigueRecovery, 
  calculateInjuryRecovery 
} from './game-engine'
import { 
  generateFreeAgentPool, 
  generateYouthPlayer, 
  calculateMarketValue 
} from './transfer-system'
import {
  awardManagerExperience,
  calculateTeamRating,
  processDailyFinancialUpdate
} from './progression-system'
import { DEFAULT_FORMATION } from './types'

// Game State Management

export class GameService {
  private supabase = createClient()

  // Team Management
  async createTeam(userId: string, teamName: string): Promise<Team> {
    const { data, error } = await this.supabase
      .from('teams')
      .insert({
        user_id: userId,
        name: teamName,
        formation: 'standard-7v7',
        team_rating: 50,
        league_position: 1,
        league_points: 0,
        matches_played: 0,
        matches_won: 0,
        matches_drawn: 0,
        matches_lost: 0,
        goals_for: 0,
        goals_against: 0,
        cash_balance: 50000,
        fan_base: 1000
      })
      .select()
      .single()

    if (error) throw error

    // Create default facilities
    await this.createDefaultFacilities(data.id)

    // Generate starting squad
    await this.generateStartingSquad(data.id)

    return data
  }

  async createDefaultFacilities(teamId: string): Promise<void> {
    const facilities = [
      { facility_type: 'training_pool', level: 1 },
      { facility_type: 'recovery_pool', level: 1 },
      { facility_type: 'youth_academy', level: 1 },
      { facility_type: 'aquatic_arena', level: 1 },
      { facility_type: 'medical_center', level: 1 }
    ]

    const { error } = await this.supabase
      .from('facilities')
      .insert(
        facilities.map(facility => ({
          team_id: teamId,
          ...facility,
          upgrade_cost: 5000,
          maintenance_cost: 100,
          bonus_percentage: 10
        }))
      )

    if (error) throw error
  }

  async generateStartingSquad(teamId: string): Promise<void> {
    const positions = [
      'goalkeeper',
      'left-wing', 'right-wing',
      'left-driver', 'right-driver',
      'center-forward',
      'point'
    ]

    // Generate 14 players (7 starters + 7 subs)
    const players = []
    
    // Generate 2 players for each position
    for (const position of positions) {
      for (let i = 0; i < 2; i++) {
        const player = generateYouthPlayer(1, teamId)
        player.position = position as any
        players.push(player)
      }
    }

    const { error } = await this.supabase
      .from('players')
      .insert(players.map(player => ({
        team_id: player.team_id,
        name: player.name,
        position: player.position,
        shooting: player.shooting,
        speed: player.speed,
        passing: player.passing,
        defense: player.defense,
        endurance: player.endurance,
        awareness: player.awareness,
        goalkeeping: player.goalkeeping,
        experience: player.experience,
        age: player.age,
        morale: player.morale,
        fatigue: player.fatigue,
        injury_status: player.injury_status,
        injury_days: player.injury_days,
        contract_salary: player.contract_salary,
        market_value: player.market_value,
        potential: player.potential,
        special_abilities: player.special_abilities,
        training_focus: player.training_focus
      })))

    if (error) throw error
  }

  // Training System
  async conductTraining(
    teamId: string,
    playerIds: string[],
    trainingType: string,
    intensity: string
  ): Promise<{ session: TrainingSession; updatedPlayers: Player[] }> {
    // Get team and players
    const { data: team } = await this.supabase
      .from('teams')
      .select('*, facilities(*)')
      .eq('id', teamId)
      .single()

    const { data: players } = await this.supabase
      .from('players')
      .select('*')
      .in('id', playerIds)

    if (!team || !players) throw new Error('Team or players not found')

    // Calculate facility bonus
    const trainingPool = team.facilities?.find((f: Facility) => f.facility_type === 'training_pool')
    const facilityBonus = trainingPool ? trainingPool.bonus_percentage : 0

    // Create training session
    const session: TrainingSession = {
      id: crypto.randomUUID(),
      team_id: teamId,
      training_type: trainingType as any,
      intensity: intensity as any,
      duration_minutes: 90,
      cost: 100 * players.length,
      experience_gained: 0,
      fatigue_added: 0,
      completed_at: new Date().toISOString(),
      created_at: new Date().toISOString()
    }

    // Apply training to players
    const updatedPlayers = players.map(player => {
      const gains = calculateTrainingGains(
        player, 
        trainingType as any, 
        intensity as any, 
        facilityBonus
      )
      return applyTrainingToPlayer(player, gains.statGains, gains.experienceGained, gains.fatigueAdded)
    })

    // Save to database
    await this.supabase.from('training_sessions').insert(session)
    
    for (const player of updatedPlayers) {
      await this.supabase
        .from('players')
        .update({
          shooting: player.shooting,
          speed: player.speed,
          passing: player.passing,
          defense: player.defense,
          endurance: player.endurance,
          awareness: player.awareness,
          goalkeeping: player.goalkeeping,
          experience: player.experience,
          fatigue: player.fatigue,
          special_abilities: player.special_abilities
        })
        .eq('id', player.id)
    }

    // Update team cash
    await this.supabase
      .from('teams')
      .update({ cash_balance: team.cash_balance - session.cost })
      .eq('id', teamId)

    return { session, updatedPlayers }
  }

  // Match System
  async simulateUserMatch(
    userTeamId: string,
    opponentTeamId: string,
    competition: string = 'league'
  ): Promise<Match> {
    // Get teams with players
    const { data: userTeam } = await this.supabase
      .from('teams')
      .select('*, players(*)')
      .eq('id', userTeamId)
      .single()

    const { data: opponentTeam } = await this.supabase
      .from('teams')
      .select('*, players(*)')
      .eq('id', opponentTeamId)
      .single()

    if (!userTeam || !opponentTeam) throw new Error('Teams not found')

    // Generate tactics
    const userTactics = { formation: DEFAULT_FORMATION, ...generateAITactics(userTeam) }
    const opponentTactics = { formation: DEFAULT_FORMATION, ...generateAITactics(opponentTeam) }

    // Simulate match
    const simulation = simulateMatch(userTeam, opponentTeam, userTactics, opponentTactics)
    const match = simulation.match

    // Save match to database
    const { data: savedMatch } = await this.supabase
      .from('matches')
      .insert({
        home_team_id: match.home_team_id,
        away_team_id: match.away_team_id,
        home_score: match.home_score,
        away_score: match.away_score,
        match_date: match.match_date,
        competition
      })
      .select()
      .single()

    // Save match events
    if (simulation.events.length > 0) {
      await this.supabase
        .from('match_events')
        .insert(
          simulation.events.map(event => ({
            match_id: savedMatch.id,
            minute: event.minute,
            event_type: event.event_type,
            player_id: event.player_id,
            team_id: event.team_id,
            description: event.description
          }))
        )
    }

    // Update team statistics
    await this.updateTeamMatchStats(userTeam, opponentTeam, match)

    return savedMatch
  }

  private async updateTeamMatchStats(homeTeam: Team, awayTeam: Team, match: Match): Promise<void> {
    const homeWin = match.home_score > match.away_score
    const awayWin = match.away_score > match.home_score
    const draw = match.home_score === match.away_score

    // Update home team
    await this.supabase
      .from('teams')
      .update({
        matches_played: homeTeam.matches_played + 1,
        matches_won: homeTeam.matches_won + (homeWin ? 1 : 0),
        matches_drawn: homeTeam.matches_drawn + (draw ? 1 : 0),
        matches_lost: homeTeam.matches_lost + (awayWin ? 1 : 0),
        goals_for: homeTeam.goals_for + match.home_score,
        goals_against: homeTeam.goals_against + match.away_score,
        league_points: homeTeam.league_points + (homeWin ? 3 : draw ? 1 : 0)
      })
      .eq('id', homeTeam.id)

    // Update away team
    await this.supabase
      .from('teams')
      .update({
        matches_played: awayTeam.matches_played + 1,
        matches_won: awayTeam.matches_won + (awayWin ? 1 : 0),
        matches_drawn: awayTeam.matches_drawn + (draw ? 1 : 0),
        matches_lost: awayTeam.matches_lost + (homeWin ? 1 : 0),
        goals_for: awayTeam.goals_for + match.away_score,
        goals_against: awayTeam.goals_against + match.home_score,
        league_points: awayTeam.league_points + (awayWin ? 3 : draw ? 1 : 0)
      })
      .eq('id', awayTeam.id)
  }

  // Daily Processing
  async processDailyUpdate(userId: string): Promise<void> {
    const { data: profile } = await this.supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single()

    const { data: team } = await this.supabase
      .from('teams')
      .select('*, players(*), facilities(*)')
      .eq('user_id', userId)
      .single()

    if (!profile || !team) return

    // Process player recovery
    await this.processPlayerRecovery(team)

    // Process financial update
    const financial = processDailyFinancialUpdate(team)
    await this.supabase
      .from('teams')
      .update({ cash_balance: financial.newBalance })
      .eq('id', team.id)

    // Update team rating
    const newRating = calculateTeamRating(team)
    await this.supabase
      .from('teams')
      .update({ team_rating: newRating })
      .eq('id', team.id)
  }

  private async processPlayerRecovery(team: Team): Promise<void> {
    if (!team.players || !team.facilities) return

    const recoveryPool = team.facilities.find(f => f.facility_type === 'recovery_pool')
    const medicalCenter = team.facilities.find(f => f.facility_type === 'medical_center')
    
    const recoveryBonus = recoveryPool ? recoveryPool.bonus_percentage : 0
    const medicalBonus = medicalCenter ? medicalCenter.bonus_percentage : 0

    for (const player of team.players) {
      let updatedPlayer = { ...player }

      // Fatigue recovery
      if (player.fatigue > 0) {
        const fatigueRecovered = calculateFatigueRecovery(player, recoveryBonus)
        updatedPlayer.fatigue = Math.max(0, player.fatigue - fatigueRecovered)
      }

      // Injury recovery
      if (player.injury_status !== 'healthy' && player.injury_days > 0) {
        const injuryRecovered = calculateInjuryRecovery(player, medicalBonus)
        updatedPlayer.injury_days = Math.max(0, player.injury_days - injuryRecovered)
        
        if (updatedPlayer.injury_days === 0) {
          updatedPlayer.injury_status = 'healthy'
        }
      }

      // Update player in database if changed
      if (updatedPlayer.fatigue !== player.fatigue || 
          updatedPlayer.injury_days !== player.injury_days || 
          updatedPlayer.injury_status !== player.injury_status) {
        await this.supabase
          .from('players')
          .update({
            fatigue: updatedPlayer.fatigue,
            injury_days: updatedPlayer.injury_days,
            injury_status: updatedPlayer.injury_status
          })
          .eq('id', player.id)
      }
    }
  }

  // Transfer Market
  async generateTransferMarket(): Promise<TransferListing[]> {
    // Generate free agents
    const freeAgents = generateFreeAgentPool(20)
    
    const listings: TransferListing[] = freeAgents.map(player => ({
      id: crypto.randomUUID(),
      player_id: player.id,
      asking_price: calculateMarketValue(player),
      transfer_type: 'free' as const,
      status: 'available' as const,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      player
    }))

    return listings
  }
}

// Singleton instance
export const gameService = new GameService()
