'use client'

import { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react'
import { useAuth } from './auth-context'
import { unifiedCurrencyService } from './unified-currency-service'
import { UserCurrency } from './types'

interface CurrencyContextType {
  currency: UserCurrency | null
  loading: boolean
  refreshCurrency: () => Promise<void>
  updateCurrency: (newCurrency: UserCurrency) => void
  formatBudgetHeader: (teamName: string) => string
  formatBudgetStat: () => string
  formatBudgetSideNav: (abbreviated?: boolean) => string
  hasSufficientFunds: (amount: number) => boolean
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined)

export function CurrencyProvider({ children }: { children: ReactNode }) {
  const { user, profile } = useAuth()
  const [currency, setCurrency] = useState<UserCurrency | null>(null)
  const [loading, setLoading] = useState(true)

  // Fetch currency data
  const fetchCurrency = useCallback(async () => {
    if (!user) {
      setCurrency(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const currencyData = await unifiedCurrencyService.getUserCurrency(user.id)
      setCurrency(currencyData)
    } catch (error) {
      console.error('Error fetching currency:', error)
      setCurrency(null)
    } finally {
      setLoading(false)
    }
  }, [user])

  // Refresh currency (for external calls)
  const refreshCurrency = useCallback(async () => {
    await fetchCurrency()
  }, [fetchCurrency])

  // Update currency locally (for immediate UI updates)
  const updateCurrency = useCallback((newCurrency: UserCurrency) => {
    setCurrency(newCurrency)
  }, [])

  // Formatting methods using current currency
  const formatBudgetHeader = useCallback((teamName: string) => {
    return unifiedCurrencyService.formatBudgetHeader(teamName, currency?.coins || 0)
  }, [currency?.coins])

  const formatBudgetStat = useCallback(() => {
    return unifiedCurrencyService.formatBudgetStat(currency?.coins || 0)
  }, [currency?.coins])

  const formatBudgetSideNav = useCallback((abbreviated: boolean = false) => {
    return unifiedCurrencyService.formatBudgetSideNav(currency?.coins || 0, abbreviated)
  }, [currency?.coins])

  // Check sufficient funds
  const hasSufficientFunds = useCallback((amount: number) => {
    return (currency?.coins || 0) >= amount
  }, [currency?.coins])

  // Fetch currency when user changes
  useEffect(() => {
    fetchCurrency()
  }, [fetchCurrency])

  // Subscribe to currency updates from the service
  useEffect(() => {
    if (!user) return

    const unsubscribe = unifiedCurrencyService.onCurrencyUpdate((updatedCurrency) => {
      // Only update if it's for the current user
      if (updatedCurrency.user_id === user.id) {
        setCurrency(updatedCurrency)
      }
    })

    return unsubscribe
  }, [user])

  // Also sync with profile.userCurrency when it updates
  useEffect(() => {
    if (profile?.userCurrency && profile.userCurrency !== currency) {
      setCurrency(profile.userCurrency)
    }
  }, [profile?.userCurrency, currency])

  const value: CurrencyContextType = {
    currency,
    loading,
    refreshCurrency,
    updateCurrency,
    formatBudgetHeader,
    formatBudgetStat,
    formatBudgetSideNav,
    hasSufficientFunds
  }

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  )
}

export function useCurrency() {
  const context = useContext(CurrencyContext)
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider')
  }
  return context
}
