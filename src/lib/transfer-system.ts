import { Player, Team, TransferListing, TransferBid, PlayerPosition, PLAYER_POSITIONS } from './types'
import { calculatePlayerOverall, calculateAgeMultiplier } from './game-engine'
import { generateRandomName, generateRandomStats } from './utils'
import { ratingService } from './rating-service'

// Transfer Market

export function calculateMarketValue(player: Player): number {
  const baseValue = calculatePlayerOverall(player) * 1000
  const ageMultiplier = getAgeValueMultiplier(player.age)
  const potentialMultiplier = 1 + (player.potential - 75) / 100
  const contractMultiplier = getContractValueMultiplier(player)
  
  return Math.round(baseValue * ageMultiplier * potentialMultiplier * contractMultiplier)
}

function getAgeValueMultiplier(age: number): number {
  if (age <= 20) return 1.5  // High potential
  if (age <= 24) return 1.3  // Prime development
  if (age <= 28) return 1.0  // Peak performance
  if (age <= 32) return 0.7  // Declining
  if (age <= 36) return 0.4  // Veteran
  return 0.2 // Very old
}

function getContractValueMultiplier(player: Player): number {
  if (!player.contract_expires_at) return 0.5 // No contract = lower value
  
  const expiryDate = new Date(player.contract_expires_at)
  const now = new Date()
  const monthsRemaining = (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24 * 30)
  
  if (monthsRemaining <= 6) return 0.6  // Short contract
  if (monthsRemaining <= 12) return 0.8 // Medium contract
  if (monthsRemaining <= 24) return 1.0 // Good contract
  return 1.2 // Long contract
}

export function generateTransferListing(
  player: Player,
  sellingTeam: Team,
  transferType: 'sale' | 'loan' | 'free' = 'sale'
): TransferListing {
  const marketValue = calculateMarketValue(player)
  const askingPrice = transferType === 'free' ? 0 : Math.round(marketValue * (0.8 + Math.random() * 0.4))
  
  const expiryDate = new Date()
  expiryDate.setDate(expiryDate.getDate() + 14) // 2 weeks on market
  
  return {
    id: crypto.randomUUID(),
    player_id: player.id,
    selling_team_id: sellingTeam.id,
    asking_price: askingPrice,
    transfer_type: transferType,
    status: 'available',
    expires_at: expiryDate.toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    player,
    selling_team: sellingTeam,
    bids: []
  }
}

export function createTransferBid(
  transfer: TransferListing,
  biddingTeam: Team,
  bidAmount: number
): TransferBid {
  return {
    id: crypto.randomUUID(),
    transfer_id: transfer.id,
    bidding_team_id: biddingTeam.id,
    bid_amount: bidAmount,
    status: 'pending',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    bidding_team: biddingTeam
  }
}

export function evaluateTransferBid(
  transfer: TransferListing,
  bid: TransferBid,
  sellingTeam: Team
): 'accept' | 'reject' | 'counter' {
  const player = transfer.player
  if (!player) return 'reject'
  
  const marketValue = calculateMarketValue(player)
  const bidRatio = bid.bid_amount / marketValue
  
  // AI decision making for selling team
  if (bidRatio >= 1.2) return 'accept' // Great offer
  if (bidRatio >= 1.0) {
    // Good offer - consider team needs
    const teamNeedsPlayer = evaluateTeamNeed(sellingTeam, player)
    return teamNeedsPlayer ? 'reject' : 'accept'
  }
  if (bidRatio >= 0.8) return 'counter' // Reasonable offer, try to negotiate
  
  return 'reject' // Too low
}

function evaluateTeamNeed(team: Team, player: Player): boolean {
  if (!team.players) return true
  
  // Count players in same position
  const samePositionPlayers = team.players.filter(p => p.position === player.position)
  
  // If less than 2 players in position, team needs the player
  return samePositionPlayers.length <= 2
}

// Free Agents

export function generateFreeAgent(
  position: PlayerPosition,
  minRating: number = 40,
  maxRating: number = 80
): Player {
  const stats = generateRandomStats()
  const age = Math.floor(Math.random() * 15) + 18 // 18-32
  
  // Adjust stats to fit rating range
  const currentRating = (stats.shooting + stats.speed + stats.passing + stats.defense) / 4
  const targetRating = minRating + Math.random() * (maxRating - minRating)
  const adjustment = targetRating - currentRating

  const adjustedStats = {
    shooting: Math.max(20, Math.min(90, stats.shooting + adjustment)),
    speed: Math.max(20, Math.min(90, stats.speed + adjustment)),
    passing: Math.max(20, Math.min(90, stats.passing + adjustment)),
    defense: Math.max(20, Math.min(90, stats.defense + adjustment))
  }
  
  const player: Player = {
    id: crypto.randomUUID(),
    team_id: '',
    name: generateRandomName(),
    position,
    ...adjustedStats,
    endurance: Math.floor(Math.random() * 40) + 40,
    awareness: Math.floor(Math.random() * 40) + 40,
    goalkeeping: position === 'goalkeeper' ? Math.floor(Math.random() * 40) + 40 : 20,
    experience: Math.floor(Math.random() * 500),
    age,
    morale: Math.floor(Math.random() * 30) + 60, // 60-90
    fatigue: Math.floor(Math.random() * 20), // 0-20
    injury_status: 'healthy',
    injury_days: 0,
    contract_salary: Math.floor(Math.random() * 5000) + 1000,
    market_value: 0, // Will be calculated
    potential: Math.floor(Math.random() * 30) + 60, // 60-90
    special_abilities: [],
    training_focus: 'balanced',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  
  player.market_value = calculateMarketValue(player)
  
  return player
}

export function generateFreeAgentPool(count: number = 50): Player[] {
  const freeAgents: Player[] = []
  
  // Generate players for each position
  PLAYER_POSITIONS.forEach(position => {
    const positionCount = Math.floor(count / PLAYER_POSITIONS.length)
    
    for (let i = 0; i < positionCount; i++) {
      const player = generateFreeAgent(position)
      freeAgents.push(player)
    }
  })
  
  // Fill remaining slots with random positions
  const remaining = count - freeAgents.length
  for (let i = 0; i < remaining; i++) {
    const randomPosition = PLAYER_POSITIONS[Math.floor(Math.random() * PLAYER_POSITIONS.length)]
    const player = generateFreeAgent(randomPosition)
    freeAgents.push(player)
  }
  
  return freeAgents
}

// Youth Academy

export function generateYouthPlayer(
  facilityLevel: number = 1,
  teamId: string
): Player {
  // Youth players are typically 16-20 years old
  const age = Math.floor(Math.random() * 5) + 16
  
  // Base potential is higher for youth players
  const basePotential = 70 + Math.floor(Math.random() * 25) // 70-95
  const facilityBonus = facilityLevel * 2
  const potential = Math.min(100, basePotential + facilityBonus)
  
  // Current stats are lower but have room to grow
  const currentRating = 30 + Math.floor(Math.random() * 30) // 30-60
  const stats = generateRandomStats()
  
  // Adjust stats to match current rating
  const adjustment = currentRating - (stats.shooting + stats.speed + stats.passing + stats.defense) / 4

  const position = PLAYER_POSITIONS[Math.floor(Math.random() * PLAYER_POSITIONS.length)]

  const player: Player = {
    id: crypto.randomUUID(),
    team_id: teamId,
    name: generateRandomName(),
    position,
    shooting: Math.max(20, Math.min(70, stats.shooting + adjustment)),
    speed: Math.max(20, Math.min(70, stats.speed + adjustment)),
    passing: Math.max(20, Math.min(70, stats.passing + adjustment)),
    defense: Math.max(20, Math.min(70, stats.defense + adjustment)),
    endurance: Math.floor(Math.random() * 30) + 40,
    awareness: Math.floor(Math.random() * 30) + 40,
    goalkeeping: position === 'goalkeeper' ? Math.floor(Math.random() * 30) + 40 : 20,
    experience: 0, // Youth players start with no experience
    age,
    morale: Math.floor(Math.random() * 20) + 70, // 70-90 (young players are enthusiastic)
    fatigue: 0,
    injury_status: 'healthy',
    injury_days: 0,
    contract_salary: Math.floor(Math.random() * 1000) + 500, // Lower salaries
    market_value: 0, // Will be calculated
    potential,
    special_abilities: [],
    training_focus: 'balanced',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
  
  player.market_value = calculateMarketValue(player)
  
  return player
}

// Contract Management

export function generateContract(
  player: Player,
  team: Team,
  durationMonths: number = 24
): { salary: number; expiryDate: string } {
  const playerRating = calculatePlayerOverall(player)
  const baseSalary = playerRating * 50 // Base salary calculation
  
  // Adjust for team financial status
  const teamMultiplier = Math.max(0.5, Math.min(2.0, team.cash_balance / 50000))
  
  // Adjust for contract length (longer contracts = higher salary)
  const lengthMultiplier = 1 + (durationMonths - 12) / 100
  
  const salary = Math.round(baseSalary * teamMultiplier * lengthMultiplier)
  
  const expiryDate = new Date()
  expiryDate.setMonth(expiryDate.getMonth() + durationMonths)
  
  return {
    salary,
    expiryDate: expiryDate.toISOString()
  }
}

export function renewContract(
  player: Player,
  team: Team,
  durationMonths: number = 24
): Player {
  const contract = generateContract(player, team, durationMonths)
  
  return {
    ...player,
    contract_salary: contract.salary,
    contract_expires_at: contract.expiryDate,
    updated_at: new Date().toISOString()
  }
}

// Scouting System

export interface ScoutReport {
  player: Player
  accuracy: number // 0-100, how accurate the report is
  reportedStats: Partial<Player>
  recommendation: 'buy' | 'monitor' | 'pass'
  scoutingCost: number
}

export function generateScoutReport(
  player: Player,
  scoutingBudget: number
): ScoutReport {
  // Higher budget = more accurate scouting
  const accuracy = Math.min(95, 50 + (scoutingBudget / 1000) * 45)
  
  // Generate reported stats with some inaccuracy
  const reportedStats: Partial<Player> = {}
  const statKeys = ['shooting', 'speed', 'passing', 'defense', 'endurance', 'awareness']
  
  statKeys.forEach(key => {
    const actualValue = player[key as keyof Player] as number
    const error = (100 - accuracy) / 100 * 20 // Max 20 point error at 0% accuracy
    const reportedValue = actualValue + (Math.random() - 0.5) * error
    reportedStats[key as keyof Player] = Math.max(0, Math.min(100, Math.round(reportedValue))) as any
  })
  
  // Generate recommendation based on reported stats and market value
  const reportedRating = Object.values(reportedStats).reduce((sum, val) => sum + (val as number), 0) / Object.keys(reportedStats).length
  const marketValue = calculateMarketValue(player)
  const valueRatio = reportedRating * 1000 / marketValue
  
  let recommendation: 'buy' | 'monitor' | 'pass'
  if (valueRatio > 1.2) recommendation = 'buy'
  else if (valueRatio > 0.8) recommendation = 'monitor'
  else recommendation = 'pass'
  
  return {
    player,
    accuracy,
    reportedStats,
    recommendation,
    scoutingCost: scoutingBudget
  }
}
