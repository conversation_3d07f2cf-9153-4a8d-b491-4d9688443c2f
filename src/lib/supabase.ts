import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          manager_level: number
          experience_points: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          manager_level?: number
          experience_points?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          manager_level?: number
          experience_points?: number
          created_at?: string
          updated_at?: string
        }
      }
      teams: {
        Row: {
          id: string
          user_id: string
          name: string
          logo_url: string | null
          formation: string
          cash_balance: number
          league_position: number | null
          league_points: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          logo_url?: string | null
          formation?: string
          cash_balance?: number
          league_position?: number | null
          league_points?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          logo_url?: string | null
          formation?: string
          cash_balance?: number
          league_position?: number | null
          league_points?: number
          created_at?: string
          updated_at?: string
        }
      }
      players: {
        Row: {
          id: string
          team_id: string
          name: string
          position: string
          jersey_number: number
          shooting: number
          speed: number
          passing: number
          defense: number
          endurance: number
          awareness: number
          goalkeeping: number
          experience: number
          age: number
          morale: number
          fatigue: number
          injury_status: string
          injury_days: number
          contract_salary: number
          contract_expires_at?: string
          market_value: number
          potential: number
          special_abilities: string[]
          perks: Record<string, any>
          training_focus: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          team_id: string
          name: string
          position: string
          jersey_number?: number
          shooting?: number
          speed?: number
          passing?: number
          defense?: number
          endurance?: number
          awareness?: number
          goalkeeping?: number
          experience?: number
          age?: number
          morale?: number
          fatigue?: number
          injury_status?: string
          injury_days?: number
          contract_salary?: number
          contract_expires_at?: string
          market_value?: number
          potential?: number
          special_abilities?: string[]
          perks?: Record<string, any>
          training_focus?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          team_id?: string
          name?: string
          position?: string
          jersey_number?: number
          shooting?: number
          speed?: number
          passing?: number
          defense?: number
          endurance?: number
          awareness?: number
          goalkeeping?: number
          experience?: number
          age?: number
          morale?: number
          fatigue?: number
          injury_status?: string
          injury_days?: number
          contract_salary?: number
          contract_expires_at?: string
          market_value?: number
          potential?: number
          special_abilities?: string[]
          perks?: Record<string, any>
          training_focus?: string
          created_at?: string
          updated_at?: string
        }
      }
      matches: {
        Row: {
          id: string
          home_team_id: string
          away_team_id: string
          home_score: number
          away_score: number
          match_date: string
          competition: string
          created_at: string
        }
        Insert: {
          id?: string
          home_team_id: string
          away_team_id: string
          home_score?: number
          away_score?: number
          match_date: string
          competition: string
          created_at?: string
        }
        Update: {
          id?: string
          home_team_id?: string
          away_team_id?: string
          home_score?: number
          away_score?: number
          match_date?: string
          competition?: string
          created_at?: string
        }
      }
      training_sessions: {
        Row: {
          id: string
          team_id: string
          training_type: string
          intensity: string
          duration_minutes: number
          cost: number
          experience_gained: number
          fatigue_added: number
          completed_at?: string
          created_at: string
        }
        Insert: {
          id?: string
          team_id: string
          training_type: string
          intensity: string
          duration_minutes: number
          cost: number
          experience_gained: number
          fatigue_added: number
          completed_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          team_id?: string
          training_type?: string
          intensity?: string
          duration_minutes?: number
          cost?: number
          experience_gained?: number
          fatigue_added?: number
          completed_at?: string
          created_at?: string
        }
      }
      player_training: {
        Row: {
          id: string
          player_id: string
          training_session_id: string
          stat_improvements: Record<string, number>
          experience_gained: number
          fatigue_added: number
          created_at: string
        }
        Insert: {
          id?: string
          player_id: string
          training_session_id: string
          stat_improvements: Record<string, number>
          experience_gained: number
          fatigue_added: number
          created_at?: string
        }
        Update: {
          id?: string
          player_id?: string
          training_session_id?: string
          stat_improvements?: Record<string, number>
          experience_gained?: number
          fatigue_added?: number
          created_at?: string
        }
      }
      facilities: {
        Row: {
          id: string
          team_id: string
          facility_type: string
          level: number
          bonus_percentage: number
          maintenance_cost: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          team_id: string
          facility_type: string
          level?: number
          bonus_percentage?: number
          maintenance_cost?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          team_id?: string
          facility_type?: string
          level?: number
          bonus_percentage?: number
          maintenance_cost?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}
