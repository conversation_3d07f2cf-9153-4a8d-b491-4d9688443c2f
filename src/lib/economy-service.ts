import { createClient } from '@/lib/supabase-client'
import { UserCurrency, Match, Player, Achievement } from './types'
import { dailyRewardsService } from './daily-rewards'

export interface EconomyTransaction {
  id: string
  user_id: string
  type: 'earn' | 'spend'
  source: 'match_win' | 'match_draw' | 'daily_login' | 'achievement' | 'facility_upgrade' | 'training' | 'transfer' | 'premium_purchase'
  coins_change: number
  tokens_change: number
  boosters_change?: Record<string, number>
  description: string
  created_at: string
}

export interface EarningSource {
  source: string
  baseAmount: number
  multiplier?: number
  description: string
}

export interface SpendingOption {
  id: string
  name: string
  description: string
  cost: {
    coins?: number
    tokens?: number
  }
  category: 'training' | 'facilities' | 'transfers' | 'boosters' | 'premium'
  available: boolean
}

export class EconomyService {
  private supabase = createClient()

  // Earning mechanisms
  private earningRates = {
    match_win: { coins: 2000, tokens: 0, description: 'Victory bonus' },
    match_draw: { coins: 800, tokens: 0, description: 'Draw bonus' },
    match_loss: { coins: 300, tokens: 0, description: 'Participation bonus' },
    training_completion: { coins: 100, tokens: 0, description: 'Training reward' },
    player_level_up: { coins: 500, tokens: 1, description: 'Player development' },
    facility_revenue: { coins: 1000, tokens: 0, description: 'Daily facility income' },
    achievement_unlock: { coins: 1000, tokens: 2, description: 'Achievement reward' },
    league_promotion: { coins: 10000, tokens: 10, description: 'League promotion bonus' },
    tournament_win: { coins: 5000, tokens: 5, description: 'Tournament victory' }
  }

  // Award currency for various game actions
  async awardCurrency(
    userId: string,
    source: keyof typeof this.earningRates,
    multiplier: number = 1,
    additionalData?: any
  ): Promise<{ success: boolean; transaction?: EconomyTransaction; error?: string }> {
    try {
      const earning = this.earningRates[source]
      const coinsEarned = Math.round(earning.coins * multiplier)
      const tokensEarned = Math.round(earning.tokens * multiplier)

      // Get current currency
      let currency = await dailyRewardsService.getUserCurrency(userId)
      
      if (!currency) {
        // Create currency record if it doesn't exist
        await this.supabase
          .from('user_currencies')
          .insert({
            user_id: userId,
            coins: coinsEarned,
            tokens: tokensEarned,
            boosters: { xp_boost: 0, recovery_boost: 0, injury_heal: 0 }
          })
      } else {
        // Update existing currency
        await this.supabase
          .from('user_currencies')
          .update({
            coins: currency.coins + coinsEarned,
            tokens: currency.tokens + tokensEarned
          })
          .eq('user_id', userId)
      }

      // Record transaction
      const { data: transaction } = await this.supabase
        .from('economy_transactions')
        .insert({
          user_id: userId,
          type: 'earn',
          source,
          coins_change: coinsEarned,
          tokens_change: tokensEarned,
          description: earning.description,
          metadata: additionalData
        })
        .select()
        .single()

      return { success: true, transaction }
    } catch (error) {
      console.error('Error awarding currency:', error)
      return { success: false, error: 'Failed to award currency' }
    }
  }

  // Process match rewards
  async processMatchRewards(
    userId: string,
    match: Match,
    isHomeTeam: boolean,
    playerPerformances: Record<string, any>
  ): Promise<void> {
    const homeScore = match.home_score
    const awayScore = match.away_score
    const userScore = isHomeTeam ? homeScore : awayScore
    const opponentScore = isHomeTeam ? awayScore : homeScore

    let source: keyof typeof this.earningRates
    let multiplier = 1

    // Determine match result
    if (userScore > opponentScore) {
      source = 'match_win'
      // Bonus for big wins
      if (userScore - opponentScore >= 5) {
        multiplier = 1.5
      }
    } else if (userScore === opponentScore) {
      source = 'match_draw'
    } else {
      source = 'match_loss'
    }

    // Award base match reward
    await this.awardCurrency(userId, source, multiplier, {
      match_id: match.id,
      score: `${userScore}-${opponentScore}`,
      competition: match.competition
    })

    // Award bonuses for exceptional performances
    for (const [playerId, performance] of Object.entries(playerPerformances)) {
      if (performance.goals >= 3) {
        await this.awardCurrency(userId, 'achievement_unlock', 0.5, {
          reason: 'Hat-trick bonus',
          player_id: playerId
        })
      }
      
      if (performance.saves >= 10) {
        await this.awardCurrency(userId, 'achievement_unlock', 0.3, {
          reason: 'Goalkeeper excellence',
          player_id: playerId
        })
      }
    }
  }

  // Get available spending options
  async getSpendingOptions(userId: string): Promise<SpendingOption[]> {
    const currency = await dailyRewardsService.getUserCurrency(userId)
    const userCoins = currency?.coins || 0
    const userTokens = currency?.tokens || 0

    const options: SpendingOption[] = [
      // Training boosters
      {
        id: 'xp_boost',
        name: 'XP Boost',
        description: 'Double training XP for next session',
        cost: { coins: 1000 },
        category: 'boosters',
        available: userCoins >= 1000
      },
      {
        id: 'recovery_boost',
        name: 'Recovery Boost',
        description: 'Instantly recover 25 fatigue points',
        cost: { coins: 500 },
        category: 'boosters',
        available: userCoins >= 500
      },
      {
        id: 'injury_heal',
        name: 'Injury Heal',
        description: 'Instantly heal minor injuries',
        cost: { tokens: 2 },
        category: 'boosters',
        available: userTokens >= 2
      },
      
      // Premium features
      {
        id: 'premium_scout',
        name: 'Premium Scout',
        description: 'Access to elite transfer market',
        cost: { tokens: 10 },
        category: 'premium',
        available: userTokens >= 10
      },
      {
        id: 'extra_training_slot',
        name: 'Extra Training Slot',
        description: 'Train one additional player per session',
        cost: { tokens: 5 },
        category: 'premium',
        available: userTokens >= 5
      },
      {
        id: 'instant_facility_upgrade',
        name: 'Instant Facility Upgrade',
        description: 'Skip facility upgrade time',
        cost: { tokens: 3 },
        category: 'premium',
        available: userTokens >= 3
      },

      // Coin packages (premium purchases)
      {
        id: 'coin_package_small',
        name: 'Small Coin Package',
        description: '10,000 coins',
        cost: { tokens: 5 },
        category: 'premium',
        available: userTokens >= 5
      },
      {
        id: 'coin_package_large',
        name: 'Large Coin Package',
        description: '50,000 coins',
        cost: { tokens: 20 },
        category: 'premium',
        available: userTokens >= 20
      }
    ]

    return options
  }

  // Purchase spending option
  async purchaseOption(
    userId: string,
    optionId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const options = await this.getSpendingOptions(userId)
      const option = options.find(o => o.id === optionId)

      if (!option) {
        return { success: false, error: 'Option not found' }
      }

      if (!option.available) {
        return { success: false, error: 'Insufficient currency' }
      }

      // Spend currency
      const spendResult = await dailyRewardsService.spendCurrency(userId, option.cost)
      if (!spendResult.success) {
        return { success: false, error: spendResult.error }
      }

      // Apply the purchased effect
      await this.applyPurchaseEffect(userId, option)

      // Record transaction
      await this.supabase
        .from('economy_transactions')
        .insert({
          user_id: userId,
          type: 'spend',
          source: 'premium_purchase',
          coins_change: -(option.cost.coins || 0),
          tokens_change: -(option.cost.tokens || 0),
          description: `Purchased: ${option.name}`,
          metadata: { option_id: optionId }
        })

      return { success: true }
    } catch (error) {
      console.error('Error purchasing option:', error)
      return { success: false, error: 'Purchase failed' }
    }
  }

  private async applyPurchaseEffect(userId: string, option: SpendingOption): Promise<void> {
    const currency = await dailyRewardsService.getUserCurrency(userId)
    if (!currency) return

    switch (option.id) {
      case 'xp_boost':
        await this.supabase
          .from('user_currencies')
          .update({
            boosters: {
              ...currency.boosters,
              xp_boost: currency.boosters.xp_boost + 1
            }
          })
          .eq('user_id', userId)
        break

      case 'recovery_boost':
        await this.supabase
          .from('user_currencies')
          .update({
            boosters: {
              ...currency.boosters,
              recovery_boost: currency.boosters.recovery_boost + 1
            }
          })
          .eq('user_id', userId)
        break

      case 'injury_heal':
        await this.supabase
          .from('user_currencies')
          .update({
            boosters: {
              ...currency.boosters,
              injury_heal: currency.boosters.injury_heal + 1
            }
          })
          .eq('user_id', userId)
        break

      case 'coin_package_small':
        await this.supabase
          .from('user_currencies')
          .update({
            coins: currency.coins + 10000
          })
          .eq('user_id', userId)
        break

      case 'coin_package_large':
        await this.supabase
          .from('user_currencies')
          .update({
            coins: currency.coins + 50000
          })
          .eq('user_id', userId)
        break

      // Premium features would be handled by adding to a user_premium_features table
      default:
        console.log(`Premium feature ${option.id} purchased - implement feature activation`)
    }
  }

  // Get transaction history
  async getTransactionHistory(
    userId: string,
    limit: number = 20
  ): Promise<EconomyTransaction[]> {
    const { data } = await this.supabase
      .from('economy_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    return data || []
  }

  // Calculate daily facility revenue
  async calculateDailyRevenue(userId: string, teamId: string): Promise<number> {
    // Get team facilities
    const { data: facilities } = await this.supabase
      .from('facilities')
      .select('*')
      .eq('team_id', teamId)

    if (!facilities) return 0

    // Calculate revenue based on Aquatic Arena level
    const arena = facilities.find(f => f.facility_type === 'aquatic_arena')
    const baseRevenue = 1000
    const arenaBonus = arena ? arena.bonus_percentage : 0

    return Math.round(baseRevenue * (1 + arenaBonus / 100))
  }

  // Process daily economic activities
  async processDailyEconomics(userId: string, teamId: string): Promise<void> {
    // Award daily facility revenue
    const revenue = await this.calculateDailyRevenue(userId, teamId)
    if (revenue > 0) {
      await this.awardCurrency(userId, 'facility_revenue', revenue / 1000, {
        amount: revenue,
        source: 'daily_revenue'
      })
    }

    // Process facility maintenance costs (handled in facilities service)
    // This would be called from a daily cron job
  }

  // Get economy statistics
  async getEconomyStats(userId: string): Promise<{
    totalEarned: { coins: number; tokens: number }
    totalSpent: { coins: number; tokens: number }
    netWorth: { coins: number; tokens: number }
    transactionCount: number
  }> {
    const transactions = await this.getTransactionHistory(userId, 1000)
    
    const totalEarned = transactions
      .filter(t => t.type === 'earn')
      .reduce((acc, t) => ({
        coins: acc.coins + t.coins_change,
        tokens: acc.tokens + t.tokens_change
      }), { coins: 0, tokens: 0 })

    const totalSpent = transactions
      .filter(t => t.type === 'spend')
      .reduce((acc, t) => ({
        coins: acc.coins + Math.abs(t.coins_change),
        tokens: acc.tokens + Math.abs(t.tokens_change)
      }), { coins: 0, tokens: 0 })

    const currency = await dailyRewardsService.getUserCurrency(userId)
    const netWorth = {
      coins: currency?.coins || 0,
      tokens: currency?.tokens || 0
    }

    return {
      totalEarned,
      totalSpent,
      netWorth,
      transactionCount: transactions.length
    }
  }
}

export const economyService = new EconomyService()
