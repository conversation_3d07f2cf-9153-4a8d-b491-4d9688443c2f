import { createClient } from '@/lib/supabase-client'
import { Player, TrainingSession, PlayerTraining, TRAINING_TYPES, Facility, PlayerPerk } from './types'
import { calculateTrainingGains, applyTrainingToPlayer } from './game-engine'
import { unifiedCurrencyService } from './unified-currency-service'
import { ratingService } from './rating-service'
import { dailyRewardsService } from './daily-rewards'

export interface TrainingDrill {
  id: string
  name: string
  description: string
  training_type: keyof typeof TRAINING_TYPES
  intensity: 'light' | 'medium' | 'high' | 'extreme'
  duration_minutes: number
  player_ids: string[]
  cost: number
  xp_multiplier: number
}

export interface TrainingResult {
  success: boolean
  session?: TrainingSession
  playerResults?: Array<{
    player: Player
    statGains: Record<string, number>
    experienceGained: number
    fatigueAdded: number
    newPerks: PlayerPerk[]
  }>
  error?: string
}

export class TrainingService {
  private supabase = createClient()

  async createTrainingSession(
    teamId: string,
    drill: TrainingDrill
  ): Promise<TrainingResult> {
    try {
      // Get user ID from team
      const { data: team, error: teamError } = await this.supabase
        .from('teams')
        .select('user_id')
        .eq('id', teamId)
        .single()

      if (teamError) {
        return {
          success: false,
          error: `Failed to fetch team data: ${teamError.message}`
        }
      }

      if (!team) {
        return {
          success: false,
          error: 'Team not found'
        }
      }

      // Check if user has enough coins
      const hasFunds = await unifiedCurrencyService.hasSufficientCoins(team.user_id, drill.cost)
      if (!hasFunds) {
        return {
          success: false,
          error: 'Insufficient funds for training session'
        }
      }

      // Get facility bonuses
      const facilityBonus = await this.getFacilityBonus(teamId, 'training_pool')

      // Create training session
      const { data: session, error: sessionError } = await this.supabase
        .from('training_sessions')
        .insert({
          team_id: teamId,
          training_type: drill.training_type,
          intensity: drill.intensity,
          duration_minutes: drill.duration_minutes,
          cost: drill.cost,
          experience_gained: Math.round(10 * drill.xp_multiplier),
          fatigue_added: this.calculateBaseFatigue(drill.intensity, drill.duration_minutes)
        })
        .select()
        .single()

      if (sessionError) throw sessionError

      // Get players for training
      const { data: players, error: playersError } = await this.supabase
        .from('players')
        .select('*')
        .in('id', drill.player_ids)

      if (playersError) {
        throw new Error(`Failed to fetch players: ${playersError.message}`)
      }

      if (!players || players.length === 0) {
        throw new Error('No players found for training')
      }

      // Apply training to each player
      const playerResults = []
      for (const player of players) {
        const result = await this.applyTrainingToPlayer(
          player,
          drill.training_type,
          drill.intensity,
          facilityBonus,
          session.id
        )
        playerResults.push(result)
      }

      // Deduct cost from user currency
      const spendResult = await unifiedCurrencyService.spendCoins(
        team.user_id,
        drill.cost,
        `Training session: ${drill.name}`,
        'training'
      )

      if (!spendResult.success) {
        throw new Error(`Failed to deduct training cost: ${spendResult.error}`)
      }

      // Update team ratings after training
      await ratingService.onPlayerChange(team.user_id)

      return {
        success: true,
        session,
        playerResults
      }
    } catch (error) {
      console.error('Error creating training session:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create training session'
      }
    }
  }

  private async applyTrainingToPlayer(
    player: Player,
    trainingType: keyof typeof TRAINING_TYPES,
    intensity: 'light' | 'medium' | 'high' | 'extreme',
    facilityBonus: number,
    sessionId: string
  ) {
    // Calculate training gains
    const gains = calculateTrainingGains(player, trainingType, intensity, facilityBonus)
    
    // Apply training effects
    const { updatedPlayer, newPerks } = applyTrainingToPlayer(
      player,
      gains.statGains,
      gains.experienceGained,
      gains.fatigueAdded
    )

    // Update player in database
    await this.supabase
      .from('players')
      .update({
        shooting: updatedPlayer.shooting,
        speed: updatedPlayer.speed,
        passing: updatedPlayer.passing,
        defense: updatedPlayer.defense,
        endurance: updatedPlayer.endurance,
        awareness: updatedPlayer.awareness,
        goalkeeping: updatedPlayer.goalkeeping,
        experience: updatedPlayer.experience,
        fatigue: updatedPlayer.fatigue,
        special_abilities: updatedPlayer.special_abilities,
        perks: updatedPlayer.perks
      })
      .eq('id', player.id)

    // Record training session for player
    await this.supabase
      .from('player_training')
      .insert({
        player_id: player.id,
        training_session_id: sessionId,
        stat_improvements: gains.statGains,
        experience_gained: gains.experienceGained,
        fatigue_added: gains.fatigueAdded
      })

    return {
      player: updatedPlayer,
      statGains: gains.statGains,
      experienceGained: gains.experienceGained,
      fatigueAdded: gains.fatigueAdded,
      newPerks
    }
  }

  private async getFacilityBonus(teamId: string, facilityType: string): Promise<number> {
    const { data: facility } = await this.supabase
      .from('facilities')
      .select('level, bonus_percentage')
      .eq('team_id', teamId)
      .eq('facility_type', facilityType)
      .single()

    return facility ? facility.bonus_percentage : 0
  }

  private calculateBaseFatigue(intensity: string, duration: number): number {
    const intensityMultiplier = {
      light: 0.5,
      medium: 1.0,
      high: 1.5,
      extreme: 2.0
    }[intensity] || 1.0

    const baseFatigue = (duration / 90) * 10 // Base 10 fatigue for 90 minutes
    return Math.round(baseFatigue * intensityMultiplier)
  }

  async getAvailableTrainingDrills(teamId: string): Promise<TrainingDrill[]> {
    // Get user's current coin balance
    const { data: team } = await this.supabase
      .from('teams')
      .select('user_id')
      .eq('id', teamId)
      .single()

    if (!team) {
      return []
    }

    const currency = await unifiedCurrencyService.getUserCurrency(team.user_id)
    const cashBalance = currency?.coins || 0

    // Generate available drills based on training types
    const drills: TrainingDrill[] = []
    
    Object.entries(TRAINING_TYPES).forEach(([type, config]) => {
      // Ensure config has required properties
      if (!config || typeof config.base_cost !== 'number') {
        console.warn(`Invalid training config for ${type}:`, config)
        return
      }

      // Basic drill
      drills.push({
        id: `${type}_basic`,
        name: `${config.name} - Basic`,
        description: `${config.description} (Basic intensity)`,
        training_type: type as keyof typeof TRAINING_TYPES,
        intensity: 'medium',
        duration_minutes: 90,
        player_ids: [],
        cost: config.base_cost,
        xp_multiplier: 1.0
      })

      // Intensive drill (if team can afford it)
      if (cashBalance >= config.base_cost * 1.5) {
        drills.push({
          id: `${type}_intensive`,
          name: `${config.name} - Intensive`,
          description: `${config.description} (High intensity, better results)`,
          training_type: type as keyof typeof TRAINING_TYPES,
          intensity: 'high',
          duration_minutes: 120,
          player_ids: [],
          cost: Math.round(config.base_cost * 1.5),
          xp_multiplier: 1.5
        })
      }

      // Elite drill (if team can afford it)
      if (cashBalance >= config.base_cost * 2.5) {
        drills.push({
          id: `${type}_elite`,
          name: `${config.name} - Elite`,
          description: `${config.description} (Extreme intensity, maximum results)`,
          training_type: type as keyof typeof TRAINING_TYPES,
          intensity: 'extreme',
          duration_minutes: 150,
          player_ids: [],
          cost: Math.round(config.base_cost * 2.5),
          xp_multiplier: 2.0
        })
      }
    })

    return drills.filter(drill => drill.cost <= cashBalance)
  }

  async getPlayerTrainingHistory(playerId: string, limit: number = 10): Promise<PlayerTraining[]> {
    const { data } = await this.supabase
      .from('player_training')
      .select(`
        *,
        training_session:training_sessions(*)
      `)
      .eq('player_id', playerId)
      .order('created_at', { ascending: false })
      .limit(limit)

    return data || []
  }

  async getTeamTrainingHistory(teamId: string, limit: number = 20): Promise<TrainingSession[]> {
    const { data } = await this.supabase
      .from('training_sessions')
      .select(`
        *,
        player_training:player_training(
          *,
          player:players(name, position)
        )
      `)
      .eq('team_id', teamId)
      .order('completed_at', { ascending: false })
      .limit(limit)

    return data || []
  }

  async recoverPlayerFatigue(playerId: string, useBooster: boolean = false): Promise<{
    success: boolean
    newFatigue?: number
    error?: string
  }> {
    try {
      const { data: player } = await this.supabase
        .from('players')
        .select('fatigue, team_id')
        .eq('id', playerId)
        .single()

      if (!player) {
        return { success: false, error: 'Player not found' }
      }

      let recoveryAmount = 10 // Base recovery

      if (useBooster) {
        // Check if user has recovery boosters
        const { data: playerData } = await this.supabase
          .from('players')
          .select('team_id')
          .eq('id', playerId)
          .single()

        if (playerData) {
          const { data: team } = await this.supabase
            .from('teams')
            .select('user_id')
            .eq('id', playerData.team_id)
            .single()

          if (team) {
            const currency = await dailyRewardsService.getUserCurrency(team.user_id)
            if (currency && currency.boosters.recovery_boost > 0) {
              recoveryAmount = 25 // Boosted recovery
              
              // Consume booster
              await this.supabase
                .from('user_currencies')
                .update({
                  boosters: {
                    ...currency.boosters,
                    recovery_boost: currency.boosters.recovery_boost - 1
                  }
                })
                .eq('user_id', team.user_id)
            } else {
              return { success: false, error: 'No recovery boosters available' }
            }
          }
        }
      }

      // Get facility bonus
      const facilityBonus = await this.getFacilityBonus(player.team_id, 'recovery_pool')
      const totalRecovery = Math.round(recoveryAmount * (1 + facilityBonus / 100))
      
      const newFatigue = Math.max(0, player.fatigue - totalRecovery)

      await this.supabase
        .from('players')
        .update({ fatigue: newFatigue })
        .eq('id', playerId)

      return {
        success: true,
        newFatigue
      }
    } catch (error) {
      console.error('Error recovering player fatigue:', error)
      return {
        success: false,
        error: 'Failed to recover fatigue'
      }
    }
  }
}

export const trainingService = new TrainingService()
