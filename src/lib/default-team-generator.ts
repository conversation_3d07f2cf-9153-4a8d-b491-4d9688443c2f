import { createClient } from '@/lib/supabase-client'
import { Player, Team } from './types'

export interface DefaultPlayer {
  name: string
  position: string
  jersey_number: number
  stats: {
    shooting: number
    speed: number
    passing: number
    defense: number
    endurance: number
    awareness: number
    goalkeeping: number
  }
  age: number
  potential: number
}

// Default starting roster
export const DEFAULT_ROSTER: DefaultPlayer[] = [
  {
    name: "<PERSON>",
    position: "goalkeeper",
    jersey_number: 1,
    stats: {
      shooting: 20,
      speed: 45,
      passing: 55,
      defense: 60,
      endurance: 65,
      awareness: 70,
      goalkeeping: 75
    },
    age: 22,
    potential: 85
  },
  {
    name: "<PERSON>",
    position: "left-wing",
    jersey_number: 2,
    stats: {
      shooting: 65,
      speed: 70,
      passing: 55,
      defense: 45,
      endurance: 60,
      awareness: 58,
      goalkeeping: 15
    },
    age: 20,
    potential: 80
  },
  {
    name: "<PERSON><PERSON>",
    position: "left-driver",
    jersey_number: 3,
    stats: {
      shooting: 55,
      speed: 60,
      passing: 65,
      defense: 70,
      endurance: 58,
      awareness: 62,
      goalkeeping: 20
    },
    age: 24,
    potential: 78
  },
  {
    name: "<PERSON>",
    position: "center-forward",
    jersey_number: 4,
    stats: {
      shooting: 75,
      speed: 55,
      passing: 60,
      defense: 50,
      endurance: 65,
      awareness: 68,
      goalkeeping: 15
    },
    age: 26,
    potential: 82
  },
  {
    name: "<PERSON> <PERSON>va<PERSON>",
    position: "point",
    jersey_number: 5,
    stats: {
      shooting: 50,
      speed: 55,
      passing: 75,
      defense: 65,
      endurance: 60,
      awareness: 72,
      goalkeeping: 20
    },
    age: 25,
    potential: 79
  },
  {
    name: "Leo <PERSON>pont",
    position: "right-driver",
    jersey_number: 6,
    stats: {
      shooting: 58,
      speed: 62,
      passing: 68,
      defense: 68,
      endurance: 60,
      awareness: 65,
      goalkeeping: 18
    },
    age: 23,
    potential: 81
  },
  {
    name: "Jalen Price",
    position: "right-wing",
    jersey_number: 7,
    stats: {
      shooting: 68,
      speed: 72,
      passing: 52,
      defense: 48,
      endurance: 62,
      awareness: 60,
      goalkeeping: 16
    },
    age: 21,
    potential: 83
  },
  {
    name: "Elias Novak",
    position: "left-driver", // Utility player, can play multiple positions
    jersey_number: 8,
    stats: {
      shooting: 60,
      speed: 58,
      passing: 62,
      defense: 62,
      endurance: 65,
      awareness: 64,
      goalkeeping: 25
    },
    age: 27,
    potential: 75
  },
  {
    name: "Giannis Petros",
    position: "center-forward",
    jersey_number: 9,
    stats: {
      shooting: 72,
      speed: 52,
      passing: 58,
      defense: 52,
      endurance: 68,
      awareness: 66,
      goalkeeping: 18
    },
    age: 24,
    potential: 84
  },
  {
    name: "Noah Lee",
    position: "right-driver",
    jersey_number: 10,
    stats: {
      shooting: 56,
      speed: 64,
      passing: 66,
      defense: 66,
      endurance: 62,
      awareness: 68,
      goalkeeping: 22
    },
    age: 22,
    potential: 80
  }
]

export class DefaultTeamGenerator {
  private supabase = createClient()

  // Generate default team for new user
  async generateDefaultTeam(
    userId: string,
    teamName: string = "Aqua Wolves",
    managerName: string = "Manager"
  ): Promise<{ success: boolean; team?: Team; error?: string }> {
    try {
      // Create the team
      const { data: team, error: teamError } = await this.supabase
        .from('teams')
        .insert({
          user_id: userId,
          name: teamName,
          manager_name: managerName,
          cash_balance: 50000,
          team_rating: 65,
          formation: 'standard-7v7',
          home_kit_color: '#1e40af', // Blue
          away_kit_color: '#dc2626', // Red
          matches_played: 0,
          matches_won: 0,
          matches_drawn: 0,
          matches_lost: 0,
          goals_for: 0,
          goals_against: 0,
          league_points: 0
        })
        .select()
        .single()

      if (teamError) throw teamError

      // Create default players
      const playersToCreate = DEFAULT_ROSTER.map(defaultPlayer => ({
        team_id: team.id,
        name: defaultPlayer.name,
        position: defaultPlayer.position,
        jersey_number: defaultPlayer.jersey_number,
        shooting: defaultPlayer.stats.shooting,
        speed: defaultPlayer.stats.speed,
        passing: defaultPlayer.stats.passing,
        defense: defaultPlayer.stats.defense,
        endurance: defaultPlayer.stats.endurance,
        awareness: defaultPlayer.stats.awareness,
        goalkeeping: defaultPlayer.stats.goalkeeping,
        age: defaultPlayer.age,
        potential: defaultPlayer.potential,
        experience: 0,
        morale: 75,
        fatigue: 0,
        injury_status: 'healthy' as const,
        injury_days: 0,
        contract_salary: this.calculateDefaultSalary(defaultPlayer.stats),
        market_value: this.calculateDefaultMarketValue(defaultPlayer.stats, defaultPlayer.age),
        special_abilities: [],
        perks: {},
        training_focus: 'balanced' as const
      }))

      const { data: players, error: playersError } = await this.supabase
        .from('players')
        .insert(playersToCreate)
        .select()

      if (playersError) throw playersError

      // Create default facilities
      await this.createDefaultFacilities(team.id)

      // Initialize user currency
      await this.supabase
        .from('user_currencies')
        .insert({
          user_id: userId,
          coins: 50000,
          tokens: 5,
          boosters: {
            xp_boost: 1,
            recovery_boost: 1,
            injury_heal: 0
          }
        })

      // Add team to a beginner league
      await this.assignToBeginnerLeague(team.id)

      // Create welcome achievement
      await this.supabase
        .from('achievements')
        .insert({
          user_id: userId,
          achievement_type: 'milestone',
          achievement_name: 'Welcome to Aqua Eleven',
          description: 'Created your first team and started your water polo management journey!',
          reward_coins: 2000,
          reward_tokens: 2
        })

      return { success: true, team: { ...team, players } }
    } catch (error) {
      console.error('Error generating default team:', error)
      return { success: false, error: 'Failed to create default team' }
    }
  }

  private calculateDefaultSalary(stats: DefaultPlayer['stats']): number {
    const avgStat = Object.values(stats).reduce((sum, stat) => sum + stat, 0) / Object.keys(stats).length
    return Math.round(avgStat * 50) // Base salary calculation
  }

  private calculateDefaultMarketValue(stats: DefaultPlayer['stats'], age: number): number {
    const avgStat = Object.values(stats).reduce((sum, stat) => sum + stat, 0) / Object.keys(stats).length
    const ageFactor = age <= 22 ? 1.2 : age <= 26 ? 1.0 : 0.8
    return Math.round(avgStat * 500 * ageFactor)
  }

  private async createDefaultFacilities(teamId: string): Promise<void> {
    const defaultFacilities = [
      {
        team_id: teamId,
        facility_type: 'training_pool',
        level: 1,
        upgrade_cost: 5000,
        maintenance_cost: 100,
        bonus_percentage: 5
      },
      {
        team_id: teamId,
        facility_type: 'recovery_pool',
        level: 1,
        upgrade_cost: 4000,
        maintenance_cost: 80,
        bonus_percentage: 10
      },
      {
        team_id: teamId,
        facility_type: 'youth_academy',
        level: 1,
        upgrade_cost: 8000,
        maintenance_cost: 150,
        bonus_percentage: 3
      },
      {
        team_id: teamId,
        facility_type: 'aquatic_arena',
        level: 1,
        upgrade_cost: 10000,
        maintenance_cost: 200,
        bonus_percentage: 8
      }
    ]

    await this.supabase
      .from('facilities')
      .insert(defaultFacilities)
  }

  private async assignToBeginnerLeague(teamId: string): Promise<void> {
    try {
      // Count teams in existing beginner leagues to find one with space
      const { data: leagues } = await this.supabase
        .from('leagues')
        .select(`
          id,
          name,
          max_teams,
          teams:teams(count)
        `)
        .eq('level', 3) // Level 3 is beginner
        .eq('status', 'active')

      // Find a league with available space
      let availableLeague = null
      if (leagues) {
        for (const league of leagues) {
          const teamCount = Array.isArray(league.teams) ? league.teams.length : (league.teams as any)?.count || 0
          if (teamCount < league.max_teams) {
            availableLeague = league
            break
          }
        }
      }

      if (availableLeague) {
        // Add team to existing league
        await this.supabase
          .from('teams')
          .update({ league_id: availableLeague.id })
          .eq('id', teamId)
      } else {
        // Create new beginner league if none available
        const { data: newLeague } = await this.supabase
          .from('leagues')
          .insert({
            name: `Division 3 - League ${Math.floor(Math.random() * 100)}`,
            level: 3,
            max_teams: 14,
            season_start_date: new Date().toISOString(),
            season_end_date: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000).toISOString(),
            status: 'active'
          })
          .select()
          .single()

        if (newLeague) {
          await this.supabase
            .from('teams')
            .update({ league_id: newLeague.id })
            .eq('id', teamId)
        }
      }
    } catch (error) {
      console.error('Error assigning to beginner league:', error)
      // Non-critical error, team creation should still succeed
    }
  }

  // Check if user already has a team
  async userHasTeam(userId: string): Promise<boolean> {
    const { data: team } = await this.supabase
      .from('teams')
      .select('id')
      .eq('user_id', userId)
      .single()

    return !!team
  }

  // Get team suggestions for new users
  getTeamNameSuggestions(): string[] {
    return [
      "Aqua Wolves",
      "Tidal Titans",
      "Wave Riders",
      "Ocean Warriors",
      "Splash Kings",
      "Water Dragons",
      "Blue Sharks",
      "Sea Eagles",
      "Aqua Thunder",
      "Neptune's Pride",
      "Tsunami Force",
      "Depth Chargers",
      "Liquid Lightning",
      "Hydro Hawks",
      "Current Crushers"
    ]
  }
}

export const defaultTeamGenerator = new DefaultTeamGenerator()
