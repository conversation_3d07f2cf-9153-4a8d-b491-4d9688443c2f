import { createClient } from '@/lib/supabase-client'
import { Facility, FACILITY_TYPES } from './types'
import { dailyRewardsService } from './daily-rewards'
import { unifiedCurrencyService } from './unified-currency-service'

export interface FacilityUpgrade {
  facility: Facility
  currentLevel: number
  maxLevel: number
  upgradeCost: number
  maintenanceCost: number
  currentBonus: number
  nextLevelBonus: number
  canUpgrade: boolean
  description: string
}

export interface UpgradeResult {
  success: boolean
  facility?: Facility
  newLevel?: number
  error?: string
}

export class FacilitiesService {
  private supabase = createClient()

  // Get all facilities for a team
  async getTeamFacilities(teamId: string): Promise<Facility[]> {
    const { data } = await this.supabase
      .from('facilities')
      .select('*')
      .eq('team_id', teamId)
      .order('facility_type')

    return data || []
  }

  // Get facility upgrade information
  async getFacilityUpgradeInfo(teamId: string): Promise<FacilityUpgrade[]> {
    const facilities = await this.getTeamFacilities(teamId)
    const { data: team } = await this.supabase
      .from('teams')
      .select('user_id')
      .eq('id', teamId)
      .single()

    if (!team) {
      return []
    }

    const currency = await unifiedCurrencyService.getUserCurrency(team.user_id)
    const teamCash = currency?.coins || 0
    const upgrades: FacilityUpgrade[] = []

    // Ensure all facility types exist
    for (const [facilityType, config] of Object.entries(FACILITY_TYPES)) {
      let facility = facilities.find(f => f.facility_type === facilityType)
      
      if (!facility) {
        // Create default facility if it doesn't exist
        facility = await this.createDefaultFacility(teamId, facilityType)
      }

      if (facility) {
        const currentLevel = facility.level
        const maxLevel = config.max_level
        const upgradeCost = this.calculateUpgradeCost(facilityType, currentLevel)
        const maintenanceCost = this.calculateMaintenanceCost(facilityType, currentLevel)
        const currentBonus = this.calculateBonus(facilityType, currentLevel)
        const nextLevelBonus = this.calculateBonus(facilityType, currentLevel + 1)

        upgrades.push({
          facility,
          currentLevel,
          maxLevel,
          upgradeCost,
          maintenanceCost,
          currentBonus,
          nextLevelBonus,
          canUpgrade: currentLevel < maxLevel && teamCash >= upgradeCost,
          description: config.description
        })
      }
    }

    return upgrades
  }

  // Upgrade a facility
  async upgradeFacility(
    teamId: string, 
    facilityType: string,
    userId: string
  ): Promise<UpgradeResult> {
    try {
      // Get current facility
      const { data: facility } = await this.supabase
        .from('facilities')
        .select('*')
        .eq('team_id', teamId)
        .eq('facility_type', facilityType)
        .single()

      if (!facility) {
        return { success: false, error: 'Facility not found' }
      }

      const config = FACILITY_TYPES[facilityType as keyof typeof FACILITY_TYPES]
      if (!config) {
        return { success: false, error: 'Invalid facility type' }
      }

      // Check if can upgrade
      if (facility.level >= config.max_level) {
        return { success: false, error: 'Facility is already at maximum level' }
      }

      const upgradeCost = this.calculateUpgradeCost(facilityType, facility.level)

      // Check if user has enough coins and spend them
      const spendResult = await unifiedCurrencyService.spendCoins(
        userId,
        upgradeCost,
        `Facility upgrade: ${config.name} to level ${facility.level + 1}`,
        'facility_upgrade'
      )
      if (!spendResult.success) {
        return { success: false, error: spendResult.error }
      }

      // Upgrade facility
      const newLevel = facility.level + 1
      const newBonus = this.calculateBonus(facilityType, newLevel)
      const newMaintenanceCost = this.calculateMaintenanceCost(facilityType, newLevel)
      const newUpgradeCost = this.calculateUpgradeCost(facilityType, newLevel)

      const { data: upgradedFacility, error } = await this.supabase
        .from('facilities')
        .update({
          level: newLevel,
          bonus_percentage: newBonus,
          maintenance_cost: newMaintenanceCost,
          upgrade_cost: newUpgradeCost
        })
        .eq('id', facility.id)
        .select()
        .single()

      if (error) throw error

      return {
        success: true,
        facility: upgradedFacility,
        newLevel
      }
    } catch (error) {
      console.error('Error upgrading facility:', error)
      return { success: false, error: 'Failed to upgrade facility' }
    }
  }

  // Calculate upgrade cost based on facility type and current level
  private calculateUpgradeCost(facilityType: string, currentLevel: number): number {
    const config = FACILITY_TYPES[facilityType as keyof typeof FACILITY_TYPES]
    if (!config) return 0

    // Exponential cost increase: base_cost * (1.5 ^ level)
    return Math.round(config.base_cost * Math.pow(1.5, currentLevel))
  }

  // Calculate maintenance cost
  private calculateMaintenanceCost(facilityType: string, level: number): number {
    const config = FACILITY_TYPES[facilityType as keyof typeof FACILITY_TYPES]
    if (!config) return 0

    // Linear increase: base_maintenance * level
    return config.base_maintenance * level
  }

  // Calculate bonus percentage based on facility type and level
  private calculateBonus(facilityType: string, level: number): number {
    const config = FACILITY_TYPES[facilityType as keyof typeof FACILITY_TYPES]
    if (!config) return 0

    switch (config.bonus_type) {
      case 'training_xp':
        return level * 5 // 5% per level
      case 'fatigue_recovery':
        return level * 10 // 10% per level
      case 'youth_quality':
        return level * 3 // 3% per level
      case 'revenue':
        return level * 8 // 8% per level
      case 'injury_recovery':
        return level * 15 // 15% per level
      default:
        return level * 5
    }
  }

  // Create default facility
  private async createDefaultFacility(teamId: string, facilityType: string): Promise<Facility> {
    const config = FACILITY_TYPES[facilityType as keyof typeof FACILITY_TYPES]
    
    const { data: facility } = await this.supabase
      .from('facilities')
      .insert({
        team_id: teamId,
        facility_type: facilityType,
        level: 1,
        upgrade_cost: config.base_cost,
        maintenance_cost: config.base_maintenance,
        bonus_percentage: this.calculateBonus(facilityType, 1)
      })
      .select()
      .single()

    return facility
  }

  // Get facility benefits description
  getFacilityBenefits(facilityType: string, level: number): string[] {
    const bonus = this.calculateBonus(facilityType, level)
    
    switch (facilityType) {
      case 'training_pool':
        return [
          `+${bonus}% training XP gain`,
          'Faster player development',
          'Improved skill progression'
        ]
      case 'recovery_pool':
        return [
          `+${bonus}% fatigue recovery rate`,
          'Players recover faster between matches',
          'Reduced injury risk'
        ]
      case 'youth_academy':
        return [
          `+${bonus}% youth player quality`,
          'Better prospects generated',
          'Higher potential players'
        ]
      case 'aquatic_arena':
        return [
          `+${bonus}% match revenue`,
          'Increased fan capacity',
          'Better sponsorship deals'
        ]
      case 'medical_center':
        return [
          `+${bonus}% injury recovery speed`,
          'Reduced injury duration',
          'Better player health monitoring'
        ]
      default:
        return [`+${bonus}% facility bonus`]
    }
  }

  // Process daily maintenance costs
  async processDailyMaintenance(teamId: string, userId: string): Promise<{
    success: boolean
    totalCost: number
    error?: string
  }> {
    try {
      const facilities = await this.getTeamFacilities(teamId)
      const totalMaintenanceCost = facilities.reduce((sum, facility) => 
        sum + facility.maintenance_cost, 0
      )

      if (totalMaintenanceCost > 0) {
        const spendResult = await dailyRewardsService.spendCurrency(userId, { 
          coins: totalMaintenanceCost 
        })
        
        if (!spendResult.success) {
          // If can't afford maintenance, reduce facility efficiency
          await this.reduceFacilityEfficiency(teamId)
          return { 
            success: false, 
            totalCost: totalMaintenanceCost,
            error: 'Insufficient funds for maintenance - facility efficiency reduced' 
          }
        }
      }

      return { success: true, totalCost: totalMaintenanceCost }
    } catch (error) {
      console.error('Error processing maintenance:', error)
      return { 
        success: false, 
        totalCost: 0, 
        error: 'Failed to process maintenance' 
      }
    }
  }

  // Reduce facility efficiency when maintenance can't be paid
  private async reduceFacilityEfficiency(teamId: string): Promise<void> {
    await this.supabase
      .from('facilities')
      .update({
        bonus_percentage: this.supabase.rpc('multiply_bonus', { multiplier: 0.8 })
      })
      .eq('team_id', teamId)
  }

  // Get total facility bonuses for a team
  async getTeamFacilityBonuses(teamId: string): Promise<Record<string, number>> {
    const facilities = await this.getTeamFacilities(teamId)
    const bonuses: Record<string, number> = {}

    facilities.forEach(facility => {
      const config = FACILITY_TYPES[facility.facility_type as keyof typeof FACILITY_TYPES]
      if (config) {
        bonuses[config.bonus_type] = facility.bonus_percentage
      }
    })

    return bonuses
  }

  // Get facility upgrade recommendations
  async getFacilityRecommendations(teamId: string): Promise<Array<{
    facilityType: string
    priority: 'high' | 'medium' | 'low'
    reason: string
  }>> {
    const upgrades = await this.getFacilityUpgradeInfo(teamId)
    const recommendations = []

    for (const upgrade of upgrades) {
      if (!upgrade.canUpgrade) continue

      let priority: 'high' | 'medium' | 'low' = 'low'
      let reason = ''

      // Prioritize based on current level and facility type
      if (upgrade.currentLevel < 3) {
        if (upgrade.facility.facility_type === 'training_pool') {
          priority = 'high'
          reason = 'Training Pool provides essential XP bonuses for player development'
        } else if (upgrade.facility.facility_type === 'recovery_pool') {
          priority = 'high'
          reason = 'Recovery Pool helps maintain player fitness and reduces injuries'
        } else if (upgrade.facility.facility_type === 'aquatic_arena') {
          priority = 'medium'
          reason = 'Aquatic Arena increases revenue for future investments'
        } else {
          priority = 'medium'
          reason = 'Facility upgrade provides good value at current level'
        }
      } else if (upgrade.currentLevel < 6) {
        priority = 'medium'
        reason = 'Mid-level upgrade provides solid improvements'
      } else {
        priority = 'low'
        reason = 'High-level upgrade for optimization'
      }

      recommendations.push({
        facilityType: upgrade.facility.facility_type,
        priority,
        reason
      })
    }

    // Sort by priority
    return recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }
}

export const facilitiesService = new FacilitiesService()
