import { Player, TrainingSession, PlayerTraining, TRAINING_TYPES, SPECIAL_ABILITIES, SpecialAbility, PLAYER_PERKS, PlayerPerk } from './types'

// Training System

export function calculateTrainingGains(
  player: Player,
  trainingType: keyof typeof TRAINING_TYPES,
  intensity: 'light' | 'medium' | 'high' | 'extreme',
  facilityBonus: number = 0
): { statGains: Record<string, number>; experienceGained: number; fatigueAdded: number } {
  const training = TRAINING_TYPES[trainingType]
  const intensityMultiplier = {
    light: 0.7,
    medium: 1.0,
    high: 1.3,
    extreme: 1.6
  }[intensity]

  const ageMultiplier = calculateAgeMultiplier(player.age)
  const moraleMultiplier = player.morale / 100
  const totalMultiplier = intensityMultiplier * ageMultiplier * moraleMultiplier * (1 + facilityBonus / 100)

  // Calculate primary stat gain
  const primaryStatGain = Math.round(
    (Math.random() * 3 + 2) * totalMultiplier * (1 - player[training.primary_stat] / 100)
  )

  // Calculate secondary stat gains
  const statGains: Record<string, number> = {
    [training.primary_stat]: primaryStatGain
  }

  training.secondary_stats.forEach(stat => {
    const secondaryGain = Math.round(
      (Math.random() * 1.5 + 0.5) * totalMultiplier * (1 - player[stat as keyof Player] as number / 100)
    )
    statGains[stat] = secondaryGain
  })

  const experienceGained = Math.round(10 * totalMultiplier)
  const fatigueAdded = Math.round(training.base_fatigue * intensityMultiplier)

  return { statGains, experienceGained, fatigueAdded }
}

export function calculateAgeMultiplier(age: number): number {
  if (age <= 22) return 1.2 // Young players develop faster
  if (age <= 26) return 1.0 // Prime age
  if (age <= 30) return 0.8 // Slight decline
  if (age <= 34) return 0.6 // Noticeable decline
  return 0.4 // Veteran players develop slowly
}

export function applyTrainingToPlayer(
  player: Player,
  statGains: Record<string, number>,
  experienceGained: number,
  fatigueAdded: number
): { updatedPlayer: Player; newPerks: PlayerPerk[] } {
  const updatedPlayer = { ...player }

  // Apply stat gains with caps
  Object.entries(statGains).forEach(([stat, gain]) => {
    if (stat in updatedPlayer) {
      const currentValue = updatedPlayer[stat as keyof Player] as number
      const newValue = Math.min(100, Math.max(0, currentValue + gain))
      ;(updatedPlayer as any)[stat] = newValue
    }
  })

  // Apply experience and fatigue
  updatedPlayer.experience += experienceGained
  updatedPlayer.fatigue = Math.min(100, updatedPlayer.fatigue + fatigueAdded)

  // Check for special ability unlocks
  updatedPlayer.special_abilities = checkSpecialAbilityUnlocks(updatedPlayer)

  // Check for perk unlocks
  const { newPerks, updatedPlayer: playerWithPerks } = checkAndUnlockPerks(updatedPlayer)

  return { updatedPlayer: playerWithPerks, newPerks }
}

export function checkSpecialAbilityUnlocks(player: Player): string[] {
  const unlockedAbilities = [...player.special_abilities]

  SPECIAL_ABILITIES.forEach(ability => {
    if (unlockedAbilities.includes(ability.id)) return

    // Check level requirement
    const playerLevel = Math.floor(player.experience / 100) + 1
    if (playerLevel < ability.unlock_level) return

    // Check stat requirements
    const meetsRequirements = Object.entries(ability.stat_requirements).every(
      ([stat, required]) => (player[stat as keyof Player] as number) >= required
    )

    if (meetsRequirements) {
      unlockedAbilities.push(ability.id)
    }
  })

  return unlockedAbilities
}

// Player Rating Calculations

export function calculatePlayerOverall(player: Player): number {
  const positionWeights = getPositionWeights(player.position)

  let weightedSum = 0
  let totalWeight = 0

  Object.entries(positionWeights).forEach(([stat, weight]) => {
    const statValue = (player[stat as keyof Player] as number) || 0
    weightedSum += statValue * weight
    totalWeight += weight
  })

  const baseRating = totalWeight > 0 ? weightedSum / totalWeight : 0

  // Apply special ability bonuses
  const abilityBonus = (player.special_abilities?.length || 0) * 2

  // Apply age factor
  const ageFactor = getAgeFactor(player.age || 25)

  // Apply morale factor
  const moraleFactor = (player.morale || 75) / 100

  const result = baseRating * ageFactor * moraleFactor + abilityBonus
  return Math.round(isNaN(result) ? 0 : result)
}

function getPositionWeights(position: string): Record<string, number> {
  const weights: Record<string, Record<string, number>> = {
    goalkeeper: {
      goalkeeping: 0.4,
      awareness: 0.2,
      endurance: 0.15,
      defense: 0.15,
      passing: 0.1
    },
    'left-wing': {
      shooting: 0.25,
      speed: 0.25,
      endurance: 0.2,
      passing: 0.15,
      awareness: 0.1,
      defense: 0.05
    },
    'right-wing': {
      shooting: 0.25,
      speed: 0.25,
      endurance: 0.2,
      passing: 0.15,
      awareness: 0.1,
      defense: 0.05
    },
    'left-driver': {
      speed: 0.2,
      passing: 0.2,
      defense: 0.2,
      awareness: 0.15,
      shooting: 0.15,
      endurance: 0.1
    },
    'right-driver': {
      speed: 0.2,
      passing: 0.2,
      defense: 0.2,
      awareness: 0.15,
      shooting: 0.15,
      endurance: 0.1
    },
    'center-forward': {
      shooting: 0.3,
      awareness: 0.2,
      endurance: 0.15,
      speed: 0.15,
      passing: 0.1,
      defense: 0.1
    },
    point: {
      passing: 0.25,
      awareness: 0.25,
      defense: 0.2,
      speed: 0.15,
      shooting: 0.1,
      endurance: 0.05
    }
  }

  return weights[position] || weights.point
}

function getAgeFactor(age: number): number {
  if (age <= 20) return 0.85 // Very young, potential not fully realized
  if (age <= 24) return 0.95 // Young, developing
  if (age <= 28) return 1.0  // Prime age
  if (age <= 32) return 0.95 // Still good
  if (age <= 36) return 0.85 // Declining
  return 0.7 // Veteran
}

// Perk System

export function checkAndUnlockPerks(player: Player): { newPerks: PlayerPerk[]; updatedPlayer: Player } {
  const newPerks: PlayerPerk[] = []
  const currentPerks = player.perks || {}

  // Check all perk categories
  Object.entries(PLAYER_PERKS).forEach(([category, perks]) => {
    perks.forEach(perk => {
      // Check if perk is already unlocked
      if (currentPerks[perk.id]) return

      // Check if player meets requirement
      const statValue = player[perk.requirement.stat] as number
      if (statValue >= perk.requirement.level) {
        newPerks.push(perk)
        currentPerks[perk.id] = {
          unlocked_at: new Date().toISOString(),
          active: true
        }
      }
    })
  })

  const updatedPlayer = {
    ...player,
    perks: currentPerks
  }

  return { newPerks, updatedPlayer }
}

export function getActivePerks(player: Player): PlayerPerk[] {
  const activePerks: PlayerPerk[] = []
  const playerPerks = player.perks || {}

  Object.entries(playerPerks).forEach(([perkId, perkData]) => {
    if (perkData.active) {
      // Find the perk definition
      Object.values(PLAYER_PERKS).forEach(categoryPerks => {
        const perk = categoryPerks.find(p => p.id === perkId)
        if (perk) {
          activePerks.push(perk)
        }
      })
    }
  })

  return activePerks
}

export function applyPerkEffects(player: Player, context: 'training' | 'match' | 'general'): Player {
  const activePerks = getActivePerks(player)
  let modifiedPlayer = { ...player }

  activePerks.forEach(perk => {
    switch (perk.effect.type) {
      case 'stat_boost':
        if (context === 'general' && perk.effect.target) {
          // Apply permanent stat boosts
          const statKey = perk.effect.target as keyof Player
          if (typeof modifiedPlayer[statKey] === 'number') {
            (modifiedPlayer[statKey] as number) += perk.effect.value
          }
        }
        break
      case 'training_bonus':
        if (context === 'training') {
          // Training bonuses are applied in the training calculation
        }
        break
      case 'special_ability':
        // Special abilities are applied during match simulation
        break
    }
  })

  return modifiedPlayer
}

// Fatigue and Recovery

export function calculateFatigueRecovery(
  player: Player,
  facilityBonus: number = 0,
  hoursRested: number = 24
): number {
  const baseRecovery = 15 // Base recovery per day
  const ageMultiplier = player.age <= 25 ? 1.2 : player.age <= 30 ? 1.0 : 0.8
  const enduranceMultiplier = 0.5 + (player.endurance / 200) // 0.5 to 1.0
  const facilityMultiplier = 1 + (facilityBonus / 100)
  
  const recoveryRate = baseRecovery * ageMultiplier * enduranceMultiplier * facilityMultiplier
  const totalRecovery = Math.round(recoveryRate * (hoursRested / 24))
  
  return Math.min(player.fatigue, totalRecovery)
}

export function calculateInjuryRecovery(
  player: Player,
  facilityBonus: number = 0
): number {
  if (player.injury_status === 'healthy') return 0

  const baseRecovery = {
    minor: 1,
    major: 0.5,
    critical: 0.25
  }[player.injury_status]

  const ageMultiplier = player.age <= 25 ? 1.2 : player.age <= 30 ? 1.0 : 0.8
  const facilityMultiplier = 1 + (facilityBonus / 100)
  
  const recoveryDays = Math.round(baseRecovery * ageMultiplier * facilityMultiplier)
  
  return Math.min(player.injury_days, recoveryDays)
}

// Morale System

export function calculateMoraleChange(
  player: Player,
  matchResult: 'win' | 'draw' | 'loss',
  playerPerformance: number, // 0-10 rating
  teamChemistry: number = 75
): number {
  let moraleChange = 0

  // Match result impact
  const resultImpact = {
    win: 5,
    draw: 0,
    loss: -3
  }[matchResult]

  // Performance impact
  const performanceImpact = (playerPerformance - 6) * 2 // -12 to +8

  // Team chemistry impact
  const chemistryImpact = (teamChemistry - 75) / 10 // -2.5 to +2.5

  moraleChange = resultImpact + performanceImpact + chemistryImpact

  // Random factor
  moraleChange += (Math.random() - 0.5) * 4

  return Math.round(moraleChange)
}

export function applyMoraleChange(player: Player, change: number): Player {
  const newMorale = Math.max(0, Math.min(100, player.morale + change))
  return { ...player, morale: newMorale }
}
