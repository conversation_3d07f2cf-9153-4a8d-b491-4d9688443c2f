import { createClient } from '@/lib/supabase-client'
import { leagueTierSystem, LeagueTier, Season, League } from './league-tier-system'
import { advancedMatchScheduler, matchScheduler } from './match-scheduler'
import { economyService } from './economy-service'

export interface SeasonStats {
  total_matches: number
  completed_matches: number
  progress_percentage: number
  current_matchday: number
  total_matchdays: number
  days_remaining: number
}

export interface TeamSeasonPerformance {
  team_id: string
  team_name: string
  position: number
  matches_played: number
  wins: number
  draws: number
  losses: number
  goals_for: number
  goals_against: number
  goal_difference: number
  points: number
  form: string[] // Last 5 results
  trend: 'up' | 'down' | 'stable'
}

export interface SeasonReward {
  team_id: string
  prize_money: number
  promotion_bonus: number
  relegation_penalty: number
  special_awards: {
    top_scorer?: { player_id: string; goals: number; bonus: number }
    best_goalkeeper?: { player_id: string; saves: number; bonus: number }
    fair_play?: { team_id: string; bonus: number }
  }
}

export class SeasonManager {
  private supabase = createClient()

  // Create a new season
  async createSeason(name: string, startDate: Date, endDate: Date): Promise<Season> {
    const { data, error } = await this.supabase
      .from('seasons')
      .insert({
        name,
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        total_matchdays: 26,
        status: 'preparing'
      })
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Start a season (activate all leagues)
  async startSeason(seasonId: string): Promise<void> {
    // Update season status
    await this.supabase
      .from('seasons')
      .update({ status: 'active' })
      .eq('id', seasonId)

    // Generate fixtures for all leagues in this season
    const { data: leagues } = await this.supabase
      .from('leagues')
      .select('id')
      .eq('season_id', seasonId)

    if (leagues) {
      for (const league of leagues) {
        try {
          await advancedMatchScheduler.generateAdvancedLeagueSchedule(league.id, seasonId)
        } catch (error) {
          console.error(`Failed to generate schedule for league ${league.id}:`, error)
        }
      }
    }
  }

  // Get current season statistics
  async getSeasonStats(seasonId: string): Promise<SeasonStats> {
    const { data: season } = await this.supabase
      .from('seasons')
      .select('*')
      .eq('id', seasonId)
      .single()

    if (!season) throw new Error('Season not found')

    // Get match statistics
    const { data: matchStats } = await this.supabase
      .from('matches')
      .select('status')
      .eq('season_id', seasonId)

    const totalMatches = matchStats?.length || 0
    const completedMatches = matchStats?.filter(m => m.status === 'completed').length || 0

    // Calculate progress
    const startDate = new Date(season.start_date)
    const endDate = new Date(season.end_date)
    const now = new Date()
    
    const totalDuration = endDate.getTime() - startDate.getTime()
    const elapsed = now.getTime() - startDate.getTime()
    const progressPercentage = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100))
    
    const daysRemaining = Math.max(0, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)))

    return {
      total_matches: totalMatches,
      completed_matches: completedMatches,
      progress_percentage: progressPercentage,
      current_matchday: season.current_matchday,
      total_matchdays: season.total_matchdays,
      days_remaining: daysRemaining
    }
  }

  // Get team performance in current season
  async getTeamSeasonPerformance(teamId: string, seasonId: string): Promise<TeamSeasonPerformance | null> {
    const { data: standing } = await this.supabase
      .from('league_standings')
      .select(`
        *,
        team:teams(id, name)
      `)
      .eq('team_id', teamId)
      .eq('season_id', seasonId)
      .single()

    if (!standing) return null

    // Calculate trend based on recent form
    const form = standing.form || []
    const recentForm = form.slice(-3) // Last 3 matches
    const wins = recentForm.filter((r: string) => r === 'W').length
    const losses = recentForm.filter((r: string) => r === 'L').length
    
    let trend: 'up' | 'down' | 'stable' = 'stable'
    if (wins > losses) trend = 'up'
    else if (losses > wins) trend = 'down'

    return {
      team_id: standing.team_id,
      team_name: standing.team.name,
      position: standing.position,
      matches_played: standing.matches_played,
      wins: standing.wins,
      draws: standing.draws,
      losses: standing.losses,
      goals_for: standing.goals_for,
      goals_against: standing.goals_against,
      goal_difference: standing.goals_for - standing.goals_against,
      points: standing.points,
      form: form,
      trend
    }
  }

  // Process end of season
  async processSeasonEnd(seasonId: string): Promise<{
    promotions: any[]
    relegations: any[]
    rewards: SeasonReward[]
  }> {
    const promotions: any[] = []
    const relegations: any[] = []
    const rewards: SeasonReward[] = []

    // Get all leagues in this season
    const { data: leagues } = await this.supabase
      .from('leagues')
      .select(`
        id,
        tier:league_tiers(*),
        standings:league_standings(
          *,
          team:teams(id, name, user_id)
        )
      `)
      .eq('season_id', seasonId)

    if (!leagues) return { promotions, relegations, rewards }

    for (const league of leagues) {
      const tier = league.tier as LeagueTier
      const standings = league.standings || []

      // Sort standings by position
      standings.sort((a: any, b: any) => a.position - b.position)

      // Process each team in the league
      for (const standing of standings) {
        const team = standing.team
        const position = standing.position
        const totalTeams = standings.length

        // Calculate rewards
        const tierRewards = leagueTierSystem.calculateTierRewards(tier, position, totalTeams)
        
        // Determine promotion/relegation
        let action = 'stays'
        if (position <= 3 && tier.level < 5) {
          action = 'promoted'
          promotions.push({
            team_id: team.id,
            team_name: team.name,
            from_tier: `${tier.name} ${tier.division}`,
            position
          })
        } else if (position >= totalTeams - 2 && tier.level > 1) {
          action = 'relegated'
          relegations.push({
            team_id: team.id,
            team_name: team.name,
            from_tier: `${tier.name} ${tier.division}`,
            position
          })
        }

        // Calculate special awards
        const specialAwards = await this.calculateSpecialAwards(team.id, seasonId)

        // Create season reward record
        const reward: SeasonReward = {
          team_id: team.id,
          prize_money: tierRewards.prize_money,
          promotion_bonus: action === 'promoted' ? tierRewards.promotion_bonus : 0,
          relegation_penalty: action === 'relegated' ? tierRewards.relegation_penalty : 0,
          special_awards: specialAwards
        }

        rewards.push(reward)

        // Award currency to team owner
        if (team.user_id) {
          const totalReward = reward.prize_money + reward.promotion_bonus - reward.relegation_penalty
          if (totalReward > 0) {
            await economyService.awardCurrency(
              team.user_id,
              'season_reward',
              totalReward,
              { 
                action, 
                position, 
                tier: `${tier.name} ${tier.division}`,
                special_awards: specialAwards
              }
            )
          }
        }

        // Save season reward to database
        await this.supabase
          .from('season_rewards')
          .insert({
            season_id: seasonId,
            team_id: team.id,
            league_id: league.id,
            final_position: position,
            prize_money: reward.prize_money,
            promotion_bonus: reward.promotion_bonus,
            relegation_penalty: reward.relegation_penalty,
            top_scorer_bonus: specialAwards.top_scorer?.bonus || 0,
            best_goalkeeper_bonus: specialAwards.best_goalkeeper?.bonus || 0
          })
      }
    }

    // Update season status
    await this.supabase
      .from('seasons')
      .update({ status: 'finished' })
      .eq('id', seasonId)

    return { promotions, relegations, rewards }
  }

  // Calculate special awards for a team
  private async calculateSpecialAwards(teamId: string, seasonId: string): Promise<{
    top_scorer?: { player_id: string; goals: number; bonus: number }
    best_goalkeeper?: { player_id: string; saves: number; bonus: number }
    fair_play?: { team_id: string; bonus: number }
  }> {
    const awards: any = {}

    // Top scorer calculation
    const { data: topScorer } = await this.supabase
      .from('match_events')
      .select(`
        player_id,
        players(name),
        count(*)
      `)
      .eq('event_type', 'goal')
      .eq('team_id', teamId)
      .in('match_id', 
        this.supabase
          .from('matches')
          .select('id')
          .eq('season_id', seasonId)
      )
      .group('player_id, players.name')
      .order('count', { ascending: false })
      .limit(1)
      .single()

    if (topScorer && topScorer.count >= 10) { // Minimum 10 goals
      awards.top_scorer = {
        player_id: topScorer.player_id,
        goals: topScorer.count,
        bonus: 5000
      }
    }

    // Best goalkeeper calculation
    const { data: bestGK } = await this.supabase
      .from('match_events')
      .select(`
        player_id,
        players(name),
        count(*)
      `)
      .eq('event_type', 'save')
      .eq('team_id', teamId)
      .in('match_id',
        this.supabase
          .from('matches')
          .select('id')
          .eq('season_id', seasonId)
      )
      .group('player_id, players.name')
      .order('count', { ascending: false })
      .limit(1)
      .single()

    if (bestGK && bestGK.count >= 50) { // Minimum 50 saves
      awards.best_goalkeeper = {
        player_id: bestGK.player_id,
        saves: bestGK.count,
        bonus: 3000
      }
    }

    return awards
  }

  // Check if season should end automatically
  async checkSeasonCompletion(seasonId: string): Promise<boolean> {
    const { data: season } = await this.supabase
      .from('seasons')
      .select('end_date, status')
      .eq('id', seasonId)
      .single()

    if (!season || season.status !== 'active') return false

    const now = new Date()
    const endDate = new Date(season.end_date)

    // Check if season has ended by date
    if (now >= endDate) {
      await this.processSeasonEnd(seasonId)
      return true
    }

    return false
  }

  // Advance to next matchday
  async advanceMatchday(seasonId: string): Promise<void> {
    const { data: season } = await this.supabase
      .from('seasons')
      .select('current_matchday, total_matchdays')
      .eq('id', seasonId)
      .single()

    if (!season) return

    const nextMatchday = season.current_matchday + 1

    if (nextMatchday <= season.total_matchdays) {
      await this.supabase
        .from('seasons')
        .update({ current_matchday: nextMatchday })
        .eq('id', seasonId)
    } else {
      // Season is complete
      await this.processSeasonEnd(seasonId)
    }
  }
}

export const seasonManager = new SeasonManager()
