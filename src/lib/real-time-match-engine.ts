import { createClient } from '@/lib/supabase-client'
import { 
  Match, 
  Team, 
  Player, 
  MatchEvent, 
  TacticalSetup,
  PlayerPerformance,
  PlayerSubstitution 
} from './types'
import { calculatePlayerOverall, getActivePerks } from './game-engine'

export interface LiveMatchState {
  match: Match
  home_team: Team
  away_team: Team
  current_minute: number
  current_quarter: number
  home_score: number
  away_score: number
  status: 'not_started' | 'first_quarter' | 'second_quarter' | 'third_quarter' | 'fourth_quarter' | 'finished'
  events: MatchEvent[]
  home_formation: string[]
  away_formation: string[]
  home_tactics: TacticalSetup
  away_tactics: TacticalSetup
  substitutions_used: {
    home: number
    away: number
  }
  timeouts_used: {
    home: number
    away: number
  }
  player_performances: Record<string, PlayerPerformance>
}

export interface MatchAction {
  type: 'substitution' | 'timeout' | 'tactical_change'
  team: 'home' | 'away'
  data: any
}

export interface MatchEventData {
  type: 'goal' | 'exclusion' | 'timeout' | 'substitution' | 'penalty' | 'save' | 'steal' | 'assist'
  minute: number
  quarter: number
  player_id?: string
  team: 'home' | 'away'
  description: string
  impact: number
}

export class RealTimeMatchEngine {
  private supabase = createClient()
  private matchState: LiveMatchState | null = null
  private eventCallbacks: ((event: MatchEventData) => void)[] = []
  private simulationInterval: NodeJS.Timeout | null = null

  constructor() {}

  // Initialize a new match
  async initializeMatch(
    homeTeam: Team,
    awayTeam: Team,
    homeTactics: TacticalSetup,
    awayTactics: TacticalSetup
  ): Promise<LiveMatchState> {
    const match: Match = {
      id: crypto.randomUUID(),
      home_team_id: homeTeam.id,
      away_team_id: awayTeam.id,
      home_score: 0,
      away_score: 0,
      match_date: new Date().toISOString(),
      competition: 'league',
      created_at: new Date().toISOString()
    }

    this.matchState = {
      match,
      home_team: homeTeam,
      away_team: awayTeam,
      current_minute: 0,
      current_quarter: 1,
      home_score: 0,
      away_score: 0,
      status: 'not_started',
      events: [],
      home_formation: this.getStartingFormation(homeTeam),
      away_formation: this.getStartingFormation(awayTeam),
      home_tactics: homeTactics,
      away_tactics: awayTactics,
      substitutions_used: { home: 0, away: 0 },
      timeouts_used: { home: 0, away: 0 },
      player_performances: this.initializePlayerPerformances(homeTeam, awayTeam)
    }

    // Save match to database
    await this.supabase
      .from('matches')
      .insert(match)

    return this.matchState
  }

  // Start real-time simulation
  startSimulation(speedMultiplier: number = 1): void {
    if (!this.matchState || this.simulationInterval) return

    this.matchState.status = 'first_quarter'
    
    // Simulate every second (adjustable with speed multiplier)
    this.simulationInterval = setInterval(() => {
      this.simulateMinute()
    }, 1000 / speedMultiplier)
  }

  // Stop simulation
  stopSimulation(): void {
    if (this.simulationInterval) {
      clearInterval(this.simulationInterval)
      this.simulationInterval = null
    }
  }

  // Subscribe to match events
  onEvent(callback: (event: MatchEventData) => void): void {
    this.eventCallbacks.push(callback)
  }

  // Apply manager actions during match
  async applyAction(action: MatchAction): Promise<boolean> {
    if (!this.matchState) return false

    switch (action.type) {
      case 'substitution':
        return this.makeSubstitution(action.team, action.data)
      case 'timeout':
        return this.callTimeout(action.team)
      case 'tactical_change':
        return this.changeTactics(action.team, action.data)
      default:
        return false
    }
  }

  private simulateMinute(): void {
    if (!this.matchState) return

    this.matchState.current_minute++

    // Check for quarter transitions
    if (this.matchState.current_minute % 8 === 0) {
      this.transitionQuarter()
    }

    // Generate events based on game state
    this.generateMinuteEvents()

    // Update player performances
    this.updatePlayerPerformances()

    // Check if match is finished
    if (this.matchState.current_minute >= 32) {
      this.finishMatch()
    }
  }

  private generateMinuteEvents(): void {
    if (!this.matchState) return

    const eventChance = Math.random()
    
    // Higher chance of events in final minutes of quarters
    const minuteInQuarter = this.matchState.current_minute % 8
    const intensityMultiplier = minuteInQuarter >= 6 ? 1.5 : 1.0

    if (eventChance < 0.15 * intensityMultiplier) {
      this.generateGoalAttempt()
    } else if (eventChance < 0.25 * intensityMultiplier) {
      this.generateDefensiveAction()
    } else if (eventChance < 0.35 * intensityMultiplier) {
      this.generateTurnover()
    }
  }

  private generateGoalAttempt(): void {
    if (!this.matchState) return

    const attackingTeam = Math.random() < 0.5 ? 'home' : 'away'
    const defendingTeam = attackingTeam === 'home' ? 'away' : 'home'
    
    const attackingPlayers = this.getActiveFieldPlayers(attackingTeam)
    const shooter = this.selectPlayerByPosition(attackingPlayers, ['center-forward', 'left-wing', 'right-wing'])
    
    if (!shooter) return

    const shooterRating = this.getPlayerEffectiveRating(shooter, 'shooting')
    const defenseRating = this.getTeamDefenseRating(defendingTeam)
    
    // Apply perks and tactical bonuses
    const shootingBonus = this.calculateShootingBonus(shooter, attackingTeam)
    const finalShootingRating = shooterRating + shootingBonus

    const goalChance = Math.max(0.1, Math.min(0.9, (finalShootingRating - defenseRating) / 100 + 0.3))

    if (Math.random() < goalChance) {
      // Goal scored!
      this.addGoal(attackingTeam, shooter)
    } else {
      // Shot saved or missed
      const goalkeeper = this.getGoalkeeper(defendingTeam)
      if (goalkeeper && Math.random() < 0.7) {
        this.addSave(defendingTeam, goalkeeper)
      } else {
        this.addMissedShot(attackingTeam, shooter)
      }
    }
  }

  private addGoal(team: 'home' | 'away', scorer: Player): void {
    if (!this.matchState) return

    if (team === 'home') {
      this.matchState.home_score++
    } else {
      this.matchState.away_score++
    }

    const event: MatchEventData = {
      type: 'goal',
      minute: this.matchState.current_minute,
      quarter: this.matchState.current_quarter,
      player_id: scorer.id,
      team,
      description: `${scorer.name} scores!`,
      impact: 10
    }

    this.matchState.events.push(event as any)
    this.notifyEvent(event)

    // Update player performance
    this.updatePlayerStat(scorer.id, 'goals', 1)
  }

  private addSave(team: 'home' | 'away', goalkeeper: Player): void {
    if (!this.matchState) return

    const event: MatchEventData = {
      type: 'save',
      minute: this.matchState.current_minute,
      quarter: this.matchState.current_quarter,
      player_id: goalkeeper.id,
      team,
      description: `Great save by ${goalkeeper.name}!`,
      impact: 5
    }

    this.matchState.events.push(event as any)
    this.notifyEvent(event)

    this.updatePlayerStat(goalkeeper.id, 'saves', 1)
  }

  private addMissedShot(team: 'home' | 'away', shooter: Player): void {
    if (!this.matchState) return

    const event: MatchEventData = {
      type: 'goal',
      minute: this.matchState.current_minute,
      quarter: this.matchState.current_quarter,
      player_id: shooter.id,
      team,
      description: `${shooter.name} misses the target`,
      impact: -2
    }

    this.matchState.events.push(event as any)
    this.notifyEvent(event)
  }

  private getPlayerEffectiveRating(player: Player, stat: keyof Player): number {
    const baseRating = player[stat] as number
    const fatigueMultiplier = 1 - (player.fatigue / 200) // Max 50% penalty
    const moraleMultiplier = 0.5 + (player.morale / 200) // 0.5 to 1.0
    
    return Math.round(baseRating * fatigueMultiplier * moraleMultiplier)
  }

  private calculateShootingBonus(player: Player, team: 'home' | 'away'): number {
    let bonus = 0
    
    // Apply player perks
    const perks = getActivePerks(player)
    perks.forEach(perk => {
      if (perk.effect.target === 'shooting_accuracy') {
        bonus += perk.effect.value
      }
      if (perk.effect.target === 'clutch_shooting' && this.matchState!.current_minute % 8 >= 6) {
        bonus += perk.effect.value
      }
    })

    // Apply tactical bonuses
    const tactics = team === 'home' ? this.matchState!.home_tactics : this.matchState!.away_tactics
    if (tactics.style === 'attacking') {
      bonus += 10
    }

    return bonus
  }

  private getActiveFieldPlayers(team: 'home' | 'away'): Player[] {
    const teamData = team === 'home' ? this.matchState!.home_team : this.matchState!.away_team
    return teamData.players?.filter(p => 
      p.position !== 'goalkeeper' && 
      p.injury_status === 'healthy' &&
      p.fatigue < 95
    ) || []
  }

  private selectPlayerByPosition(players: Player[], preferredPositions: string[]): Player | null {
    // Try preferred positions first
    for (const position of preferredPositions) {
      const positionPlayers = players.filter(p => p.position === position)
      if (positionPlayers.length > 0) {
        return positionPlayers[Math.floor(Math.random() * positionPlayers.length)]
      }
    }
    
    // Fallback to any available player
    return players.length > 0 ? players[Math.floor(Math.random() * players.length)] : null
  }

  private getGoalkeeper(team: 'home' | 'away'): Player | null {
    const teamData = team === 'home' ? this.matchState!.home_team : this.matchState!.away_team
    return teamData.players?.find(p => p.position === 'goalkeeper') || null
  }

  private getTeamDefenseRating(team: 'home' | 'away'): number {
    const teamData = team === 'home' ? this.matchState!.home_team : this.matchState!.away_team
    const defensivePlayers = teamData.players?.filter(p => 
      ['goalkeeper', 'left-driver', 'right-driver', 'point'].includes(p.position)
    ) || []
    
    const avgDefense = defensivePlayers.reduce((sum, p) => sum + p.defense, 0) / defensivePlayers.length
    return avgDefense || 50
  }

  private notifyEvent(event: MatchEventData): void {
    this.eventCallbacks.forEach(callback => callback(event))
  }

  private updatePlayerStat(playerId: string, stat: string, value: number): void {
    if (!this.matchState!.player_performances[playerId]) {
      this.matchState!.player_performances[playerId] = {
        player_id: playerId,
        goals: 0,
        assists: 0,
        saves: 0,
        steals: 0,
        exclusions: 0,
        rating: 6.0
      }
    }
    
    (this.matchState!.player_performances[playerId] as any)[stat] += value
  }

  private getStartingFormation(team: Team): string[] {
    // Return player IDs in formation order
    return team.players?.slice(0, 7).map(p => p.id) || []
  }

  private initializePlayerPerformances(homeTeam: Team, awayTeam: Team): Record<string, PlayerPerformance> {
    const performances: Record<string, PlayerPerformance> = {}
    
    const allPlayers = [...(homeTeam.players || []), ...(awayTeam.players || [])]
    allPlayers.forEach(player => {
      performances[player.id] = {
        player_id: player.id,
        goals: 0,
        assists: 0,
        saves: 0,
        steals: 0,
        exclusions: 0,
        rating: 6.0
      }
    })
    
    return performances
  }

  private transitionQuarter(): void {
    if (!this.matchState) return

    this.matchState.current_quarter++
    
    if (this.matchState.current_quarter <= 4) {
      this.matchState.status = `${this.getQuarterName(this.matchState.current_quarter)}` as any
    }
  }

  private getQuarterName(quarter: number): string {
    const names = ['', 'first_quarter', 'second_quarter', 'third_quarter', 'fourth_quarter']
    return names[quarter] || 'finished'
  }

  private generateDefensiveAction(): void {
    // Implementation for defensive actions like steals, blocks
  }

  private generateTurnover(): void {
    // Implementation for turnovers and possession changes
  }

  private makeSubstitution(team: 'home' | 'away', data: any): boolean {
    // Implementation for player substitutions
    return true
  }

  private callTimeout(team: 'home' | 'away'): boolean {
    // Implementation for timeouts
    return true
  }

  private changeTactics(team: 'home' | 'away', data: any): boolean {
    // Implementation for tactical changes
    return true
  }

  private updatePlayerPerformances(): void {
    // Update player ratings based on actions
  }

  private async finishMatch(): Promise<void> {
    if (!this.matchState) return

    this.matchState.status = 'finished'
    this.stopSimulation()

    // Update match in database
    await this.supabase
      .from('matches')
      .update({
        home_score: this.matchState.home_score,
        away_score: this.matchState.away_score
      })
      .eq('id', this.matchState.match.id)

    // Save match events
    for (const event of this.matchState.events) {
      await this.supabase
        .from('match_events')
        .insert({
          match_id: this.matchState.match.id,
          minute: event.minute,
          event_type: event.event_type,
          player_id: event.player_id,
          team_id: event.team === 'home' ? this.matchState.home_team.id : this.matchState.away_team.id,
          description: event.description
        })
    }
  }

  // Get current match state
  getMatchState(): LiveMatchState | null {
    return this.matchState
  }
}

export const realTimeMatchEngine = new RealTimeMatchEngine()
