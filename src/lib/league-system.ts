import { createClient } from '@/lib/supabase-client'
import { League, Team, Match, Competition } from './types'
import { simulateMatch, generateAITactics } from './match-engine'
import { matchScheduler } from './match-scheduler'
import { economyService } from './economy-service'
import { DEFAULT_FORMATION } from './types'

// League Management

export interface LeagueTable {
  position: number
  team: Team
  played: number
  won: number
  drawn: number
  lost: number
  goalsFor: number
  goalsAgainst: number
  goalDifference: number
  points: number
  form: ('W' | 'D' | 'L')[]
}

export function generateLeagueTable(league: League): LeagueTable[] {
  if (!league.teams) return []

  const table: LeagueTable[] = league.teams.map(team => ({
    position: 0, // Will be calculated
    team,
    played: team.matches_played,
    won: team.matches_won,
    drawn: team.matches_drawn,
    lost: team.matches_lost,
    goalsFor: team.goals_for,
    goalsAgainst: team.goals_against,
    goalDifference: team.goals_for - team.goals_against,
    points: team.league_points,
    form: [] // Simplified - would need match history
  }))

  // Sort by points, then goal difference, then goals for
  table.sort((a, b) => {
    if (a.points !== b.points) return b.points - a.points
    if (a.goalDifference !== b.goalDifference) return b.goalDifference - a.goalDifference
    return b.goalsFor - a.goalsFor
  })

  // Assign positions
  table.forEach((entry, index) => {
    entry.position = index + 1
  })

  return table
}

export function generateLeagueFixtures(league: League): Match[] {
  if (!league.teams || league.teams.length < 2) return []

  const fixtures: Match[] = []
  const teams = league.teams
  const numTeams = teams.length

  // Generate round-robin fixtures (each team plays every other team twice)
  for (let round = 0; round < (numTeams - 1) * 2; round++) {
    const isSecondHalf = round >= numTeams - 1
    const roundMatches: Match[] = []

    for (let match = 0; match < numTeams / 2; match++) {
      let home = (round + match) % (numTeams - 1)
      let away = (numTeams - 1 - match + round) % (numTeams - 1)

      // Last team stays in place
      if (match === 0) {
        away = numTeams - 1
      }

      // Swap home/away for second half of season
      if (isSecondHalf) {
        [home, away] = [away, home]
      }

      const homeTeam = teams[home]
      const awayTeam = teams[away]

      if (homeTeam && awayTeam) {
        const matchDate = new Date()
        matchDate.setDate(matchDate.getDate() + round * 3) // 3 days between rounds

        roundMatches.push({
          id: crypto.randomUUID(),
          home_team_id: homeTeam.id,
          away_team_id: awayTeam.id,
          home_score: 0,
          away_score: 0,
          match_date: matchDate.toISOString(),
          competition: `${league.name} - Matchday ${Math.floor(round / (numTeams - 1)) + 1}`,
          created_at: new Date().toISOString()
        })
      }
    }

    fixtures.push(...roundMatches)
  }

  return fixtures
}

export function simulateLeagueMatchday(
  league: League,
  matchday: number
): { matches: Match[]; updatedTeams: Team[] } {
  const fixtures = generateLeagueFixtures(league)
  const matchdayFixtures = fixtures.filter(match => 
    match.competition.includes(`Matchday ${matchday}`)
  )

  const simulatedMatches: Match[] = []
  const teamUpdates: Record<string, Partial<Team>> = {}

  // Initialize team updates
  league.teams?.forEach(team => {
    teamUpdates[team.id] = {
      matches_played: team.matches_played,
      matches_won: team.matches_won,
      matches_drawn: team.matches_drawn,
      matches_lost: team.matches_lost,
      goals_for: team.goals_for,
      goals_against: team.goals_against,
      league_points: team.league_points
    }
  })

  matchdayFixtures.forEach(fixture => {
    const homeTeam = league.teams?.find(t => t.id === fixture.home_team_id)
    const awayTeam = league.teams?.find(t => t.id === fixture.away_team_id)

    if (!homeTeam || !awayTeam) return

    // Generate AI tactics for both teams
    const homeTactics = {
      formation: DEFAULT_FORMATION,
      ...generateAITactics(homeTeam)
    }
    const awayTactics = {
      formation: DEFAULT_FORMATION,
      ...generateAITactics(awayTeam)
    }

    // Simulate the match
    const simulation = simulateMatch(homeTeam, awayTeam, homeTactics, awayTactics)
    const match = simulation.match

    simulatedMatches.push(match)

    // Update team statistics
    const homeUpdate = teamUpdates[homeTeam.id]
    const awayUpdate = teamUpdates[awayTeam.id]

    if (homeUpdate && awayUpdate) {
      // Update matches played
      homeUpdate.matches_played = (homeUpdate.matches_played || 0) + 1
      awayUpdate.matches_played = (awayUpdate.matches_played || 0) + 1

      // Update goals
      homeUpdate.goals_for = (homeUpdate.goals_for || 0) + match.home_score
      homeUpdate.goals_against = (homeUpdate.goals_against || 0) + match.away_score
      awayUpdate.goals_for = (awayUpdate.goals_for || 0) + match.away_score
      awayUpdate.goals_against = (awayUpdate.goals_against || 0) + match.home_score

      // Update results and points
      if (match.home_score > match.away_score) {
        // Home win
        homeUpdate.matches_won = (homeUpdate.matches_won || 0) + 1
        awayUpdate.matches_lost = (awayUpdate.matches_lost || 0) + 1
        homeUpdate.league_points = (homeUpdate.league_points || 0) + 3
      } else if (match.home_score < match.away_score) {
        // Away win
        awayUpdate.matches_won = (awayUpdate.matches_won || 0) + 1
        homeUpdate.matches_lost = (homeUpdate.matches_lost || 0) + 1
        awayUpdate.league_points = (awayUpdate.league_points || 0) + 3
      } else {
        // Draw
        homeUpdate.matches_drawn = (homeUpdate.matches_drawn || 0) + 1
        awayUpdate.matches_drawn = (awayUpdate.matches_drawn || 0) + 1
        homeUpdate.league_points = (homeUpdate.league_points || 0) + 1
        awayUpdate.league_points = (awayUpdate.league_points || 0) + 1
      }
    }
  })

  // Apply updates to teams
  const updatedTeams = league.teams?.map(team => ({
    ...team,
    ...teamUpdates[team.id]
  })) || []

  return { matches: simulatedMatches, updatedTeams }
}

// Promotion and Relegation

export function calculatePromotionRelegation(
  leagues: League[]
): { promotions: { team: Team; fromLeague: League; toLeague: League }[]; relegations: { team: Team; fromLeague: League; toLeague: League }[] } {
  const promotions: { team: Team; fromLeague: League; toLeague: League }[] = []
  const relegations: { team: Team; fromLeague: League; toLeague: League }[] = []

  // Sort leagues by level
  const sortedLeagues = leagues.sort((a, b) => a.level - b.level)

  for (let i = 0; i < sortedLeagues.length - 1; i++) {
    const lowerLeague = sortedLeagues[i + 1] // Higher level number = lower division
    const higherLeague = sortedLeagues[i]

    const lowerTable = generateLeagueTable(lowerLeague)
    const higherTable = generateLeagueTable(higherLeague)

    // Promote top 2 teams from lower league
    const teamsToPromote = lowerTable.slice(0, 2)
    teamsToPromote.forEach(entry => {
      promotions.push({
        team: entry.team,
        fromLeague: lowerLeague,
        toLeague: higherLeague
      })
    })

    // Relegate bottom 2 teams from higher league
    const teamsToRelegate = higherTable.slice(-2)
    teamsToRelegate.forEach(entry => {
      relegations.push({
        team: entry.team,
        fromLeague: higherLeague,
        toLeague: lowerLeague
      })
    })
  }

  return { promotions, relegations }
}

// Cup Competitions

export function generateCupFixtures(competition: Competition, teams: Team[]): Match[] {
  if (!teams.length || teams.length < 2) return []

  const fixtures: Match[] = []
  const numRounds = Math.ceil(Math.log2(teams.length))
  
  // Shuffle teams for random draw
  const shuffledTeams = [...teams].sort(() => Math.random() - 0.5)
  
  let currentRoundTeams = shuffledTeams
  
  for (let round = 1; round <= numRounds; round++) {
    const roundFixtures: Match[] = []
    const nextRoundTeams: Team[] = []
    
    // Pair teams for matches
    for (let i = 0; i < currentRoundTeams.length; i += 2) {
      const homeTeam = currentRoundTeams[i]
      const awayTeam = currentRoundTeams[i + 1]
      
      if (homeTeam && awayTeam) {
        const matchDate = new Date()
        matchDate.setDate(matchDate.getDate() + round * 7) // Weekly rounds
        
        const match: Match = {
          id: crypto.randomUUID(),
          home_team_id: homeTeam.id,
          away_team_id: awayTeam.id,
          home_score: 0,
          away_score: 0,
          match_date: matchDate.toISOString(),
          competition: `${competition.name} - Round ${round}`,
          created_at: new Date().toISOString()
        }
        
        roundFixtures.push(match)
        
        // Simulate match to determine winner for next round
        const homeTactics = { formation: DEFAULT_FORMATION, ...generateAITactics(homeTeam) }
        const awayTactics = { formation: DEFAULT_FORMATION, ...generateAITactics(awayTeam) }
        const simulation = simulateMatch(homeTeam, awayTeam, homeTactics, awayTactics)
        
        // Winner advances (or home team if draw for simplicity)
        const winner = simulation.home_score >= simulation.away_score ? homeTeam : awayTeam
        nextRoundTeams.push(winner)
      } else if (homeTeam) {
        // Bye to next round
        nextRoundTeams.push(homeTeam)
      }
    }
    
    fixtures.push(...roundFixtures)
    currentRoundTeams = nextRoundTeams
    
    if (currentRoundTeams.length <= 1) break
  }
  
  return fixtures
}

// Season Management

export function advanceSeason(league: League): League {
  const updatedLeague = { ...league }
  
  // Reset team statistics for new season
  if (updatedLeague.teams) {
    updatedLeague.teams = updatedLeague.teams.map(team => ({
      ...team,
      matches_played: 0,
      matches_won: 0,
      matches_drawn: 0,
      matches_lost: 0,
      goals_for: 0,
      goals_against: 0,
      league_points: 0,
      league_position: 1
    }))
  }
  
  // Update season dates
  const currentDate = new Date()
  updatedLeague.season_start_date = currentDate.toISOString()
  
  const endDate = new Date(currentDate)
  endDate.setMonth(endDate.getMonth() + 6) // 6-month season
  updatedLeague.season_end_date = endDate.toISOString()
  
  updatedLeague.current_matchday = 1
  updatedLeague.status = 'active'
  
  return updatedLeague
}

export function isSeasonComplete(league: League): boolean {
  if (!league.teams) return true

  const expectedMatches = (league.teams.length - 1) * 2 // Each team plays every other team twice

  return league.teams.every(team => team.matches_played >= expectedMatches)
}

// Enhanced League System Service
export interface LeagueStanding {
  team: Team
  position: number
  matches_played: number
  wins: number
  draws: number
  losses: number
  goals_for: number
  goals_against: number
  goal_difference: number
  points: number
  form: string[] // Last 5 results: 'W', 'D', 'L'
}

export interface SeasonSchedule {
  matchday: number
  date: string
  matches: Match[]
}

export interface LeaguePromotion {
  team_id: string
  from_league: string
  to_league: string
  reason: 'promotion' | 'relegation'
  season: string
}

export class EnhancedLeagueSystem {
  private supabase = createClient()

  // Generate league standings with enhanced data
  async getLeagueStandings(leagueId: string): Promise<LeagueStanding[]> {
    const { data: teams } = await this.supabase
      .from('teams')
      .select(`
        *,
        home_matches:matches!matches_home_team_id_fkey(
          id, home_score, away_score, match_date,
          away_team:teams!matches_away_team_id_fkey(name)
        ),
        away_matches:matches!matches_away_team_id_fkey(
          id, home_score, away_score, match_date,
          home_team:teams!matches_home_team_id_fkey(name)
        )
      `)
      .eq('league_id', leagueId)

    if (!teams) return []

    const standings: LeagueStanding[] = teams.map(team => {
      const homeMatches = team.home_matches || []
      const awayMatches = team.away_matches || []
      const allMatches = [...homeMatches, ...awayMatches]
        .filter(m => m.home_score !== null && m.away_score !== null)
        .sort((a, b) => new Date(b.match_date).getTime() - new Date(a.match_date).getTime())

      let wins = 0, draws = 0, losses = 0
      let goalsFor = 0, goalsAgainst = 0
      const form: string[] = []

      // Calculate stats from home matches
      homeMatches.forEach(match => {
        if (match.home_score !== null && match.away_score !== null) {
          goalsFor += match.home_score
          goalsAgainst += match.away_score

          if (match.home_score > match.away_score) {
            wins++
            form.unshift('W')
          } else if (match.home_score === match.away_score) {
            draws++
            form.unshift('D')
          } else {
            losses++
            form.unshift('L')
          }
        }
      })

      // Calculate stats from away matches
      awayMatches.forEach(match => {
        if (match.home_score !== null && match.away_score !== null) {
          goalsFor += match.away_score
          goalsAgainst += match.home_score

          if (match.away_score > match.home_score) {
            wins++
            form.unshift('W')
          } else if (match.away_score === match.home_score) {
            draws++
            form.unshift('D')
          } else {
            losses++
            form.unshift('L')
          }
        }
      })

      return {
        team,
        position: 0, // Will be set after sorting
        matches_played: wins + draws + losses,
        wins,
        draws,
        losses,
        goals_for: goalsFor,
        goals_against: goalsAgainst,
        goal_difference: goalsFor - goalsAgainst,
        points: wins * 3 + draws,
        form: form.slice(0, 5) // Last 5 matches
      }
    })

    // Sort by points, then goal difference, then goals for
    standings.sort((a, b) => {
      if (a.points !== b.points) return b.points - a.points
      if (a.goal_difference !== b.goal_difference) return b.goal_difference - a.goal_difference
      return b.goals_for - a.goals_for
    })

    // Set positions
    standings.forEach((standing, index) => {
      standing.position = index + 1
    })

    return standings
  }

  // Process end of season promotions/relegations
  async processSeasonEnd(leagueId: string): Promise<{
    promotions: LeaguePromotion[]
    relegations: LeaguePromotion[]
  }> {
    const standings = await this.getLeagueStandings(leagueId)
    const promotions: LeaguePromotion[] = []
    const relegations: LeaguePromotion[] = []

    // Get league info
    const { data: league } = await this.supabase
      .from('leagues')
      .select('level, name')
      .eq('id', leagueId)
      .single()

    if (!league) return { promotions, relegations }

    // Top 2 teams get promoted (if not in top level)
    if (league.level > 1) {
      const topTeams = standings.slice(0, 2)

      for (const standing of topTeams) {
        // Find higher level league
        const { data: higherLeague } = await this.supabase
          .from('leagues')
          .select('id, name')
          .eq('level', league.level - 1)
          .eq('status', 'active')
          .limit(1)
          .single()

        if (higherLeague) {
          await this.supabase
            .from('teams')
            .update({ league_id: higherLeague.id })
            .eq('id', standing.team.id)

          promotions.push({
            team_id: standing.team.id,
            from_league: league.name,
            to_league: higherLeague.name,
            reason: 'promotion',
            season: new Date().getFullYear().toString()
          })

          // Award promotion bonus
          if (standing.team.user_id) {
            await economyService.awardCurrency(
              standing.team.user_id,
              'league_promotion',
              1,
              { league: higherLeague.name, position: standing.position }
            )
          }
        }
      }
    }

    return { promotions, relegations }
  }

  // Auto-simulate scheduled matches
  async autoSimulateMatches(): Promise<void> {
    await matchScheduler.autoSimulateScheduledMatches()
  }
}

export const enhancedLeagueSystem = new EnhancedLeagueSystem()
