import { createClient } from '@/lib/supabase-client'

export class LeagueInitializationService {
  private supabase = createClient()

  // Initialize the complete league system
  async initializeLeagueSystem(): Promise<void> {
    try {
      console.log('Initializing league system...')
      
      // Step 1: Setup league tiers
      await this.setupLeagueTiers()
      
      // Step 2: Create active season
      await this.createActiveSeason()
      
      // Step 3: Create initial leagues for Bronze Division V
      await this.createInitialLeagues()
      
      console.log('League system initialization complete')
    } catch (error) {
      console.error('Error initializing league system:', error)
      throw error
    }
  }

  // Setup all league tiers (Bronze to Legends, Divisions I-V)
  private async setupLeagueTiers(): Promise<void> {
    try {
      // Check if tiers already exist
      const { data: existingTiers } = await this.supabase
        .from('league_tiers')
        .select('id')
        .limit(1)

      if (existingTiers && existingTiers.length > 0) {
        console.log('League tiers already exist')
        return
      }

      console.log('Creating league tiers...')

      const tiers = [
        // Bronze Tier (Level 1) - Starting tier for new players
        { name: 'Bronze', level: 1, division: 1, prize_money_base: 5000, promotion_bonus: 2000, relegation_penalty: 500, tactics_bonus_enabled: false, scouting_quality_multiplier: 0.8, fanbase_growth_multiplier: 0.8 },
        { name: 'Bronze', level: 1, division: 2, prize_money_base: 4500, promotion_bonus: 1800, relegation_penalty: 450, tactics_bonus_enabled: false, scouting_quality_multiplier: 0.8, fanbase_growth_multiplier: 0.8 },
        { name: 'Bronze', level: 1, division: 3, prize_money_base: 4000, promotion_bonus: 1600, relegation_penalty: 400, tactics_bonus_enabled: false, scouting_quality_multiplier: 0.8, fanbase_growth_multiplier: 0.8 },
        { name: 'Bronze', level: 1, division: 4, prize_money_base: 3500, promotion_bonus: 1400, relegation_penalty: 350, tactics_bonus_enabled: false, scouting_quality_multiplier: 0.8, fanbase_growth_multiplier: 0.8 },
        { name: 'Bronze', level: 1, division: 5, prize_money_base: 3000, promotion_bonus: 1200, relegation_penalty: 300, tactics_bonus_enabled: false, scouting_quality_multiplier: 0.8, fanbase_growth_multiplier: 0.8 },
        
        // Silver Tier (Level 2) - Intermediate with tactics bonuses
        { name: 'Silver', level: 2, division: 1, prize_money_base: 10000, promotion_bonus: 4000, relegation_penalty: 1000, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.0, fanbase_growth_multiplier: 1.0 },
        { name: 'Silver', level: 2, division: 2, prize_money_base: 9000, promotion_bonus: 3600, relegation_penalty: 900, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.0, fanbase_growth_multiplier: 1.0 },
        { name: 'Silver', level: 2, division: 3, prize_money_base: 8000, promotion_bonus: 3200, relegation_penalty: 800, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.0, fanbase_growth_multiplier: 1.0 },
        { name: 'Silver', level: 2, division: 4, prize_money_base: 7000, promotion_bonus: 2800, relegation_penalty: 700, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.0, fanbase_growth_multiplier: 1.0 },
        { name: 'Silver', level: 2, division: 5, prize_money_base: 6000, promotion_bonus: 2400, relegation_penalty: 600, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.0, fanbase_growth_multiplier: 1.0 },
        
        // Gold Tier (Level 3) - High skill required
        { name: 'Gold', level: 3, division: 1, prize_money_base: 20000, promotion_bonus: 8000, relegation_penalty: 2000, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.2, fanbase_growth_multiplier: 1.2 },
        { name: 'Gold', level: 3, division: 2, prize_money_base: 18000, promotion_bonus: 7200, relegation_penalty: 1800, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.2, fanbase_growth_multiplier: 1.2 },
        { name: 'Gold', level: 3, division: 3, prize_money_base: 16000, promotion_bonus: 6400, relegation_penalty: 1600, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.2, fanbase_growth_multiplier: 1.2 },
        { name: 'Gold', level: 3, division: 4, prize_money_base: 14000, promotion_bonus: 5600, relegation_penalty: 1400, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.2, fanbase_growth_multiplier: 1.2 },
        { name: 'Gold', level: 3, division: 5, prize_money_base: 12000, promotion_bonus: 4800, relegation_penalty: 1200, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.2, fanbase_growth_multiplier: 1.2 },
        
        // Elite Tier (Level 4) - Top-tier, invitation-based
        { name: 'Elite', level: 4, division: 1, prize_money_base: 40000, promotion_bonus: 16000, relegation_penalty: 4000, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.5, fanbase_growth_multiplier: 1.5 },
        { name: 'Elite', level: 4, division: 2, prize_money_base: 36000, promotion_bonus: 14400, relegation_penalty: 3600, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.5, fanbase_growth_multiplier: 1.5 },
        { name: 'Elite', level: 4, division: 3, prize_money_base: 32000, promotion_bonus: 12800, relegation_penalty: 3200, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.5, fanbase_growth_multiplier: 1.5 },
        { name: 'Elite', level: 4, division: 4, prize_money_base: 28000, promotion_bonus: 11200, relegation_penalty: 2800, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.5, fanbase_growth_multiplier: 1.5 },
        { name: 'Elite', level: 4, division: 5, prize_money_base: 24000, promotion_bonus: 9600, relegation_penalty: 2400, tactics_bonus_enabled: true, scouting_quality_multiplier: 1.5, fanbase_growth_multiplier: 1.5 },
        
        // Legends Tier (Level 5) - Global Top 1%
        { name: 'Legends', level: 5, division: 1, prize_money_base: 100000, promotion_bonus: 40000, relegation_penalty: 10000, tactics_bonus_enabled: true, scouting_quality_multiplier: 2.0, fanbase_growth_multiplier: 2.0 },
        { name: 'Legends', level: 5, division: 2, prize_money_base: 90000, promotion_bonus: 36000, relegation_penalty: 9000, tactics_bonus_enabled: true, scouting_quality_multiplier: 2.0, fanbase_growth_multiplier: 2.0 },
        { name: 'Legends', level: 5, division: 3, prize_money_base: 80000, promotion_bonus: 32000, relegation_penalty: 8000, tactics_bonus_enabled: true, scouting_quality_multiplier: 2.0, fanbase_growth_multiplier: 2.0 },
        { name: 'Legends', level: 5, division: 4, prize_money_base: 70000, promotion_bonus: 28000, relegation_penalty: 7000, tactics_bonus_enabled: true, scouting_quality_multiplier: 2.0, fanbase_growth_multiplier: 2.0 },
        { name: 'Legends', level: 5, division: 5, prize_money_base: 60000, promotion_bonus: 24000, relegation_penalty: 6000, tactics_bonus_enabled: true, scouting_quality_multiplier: 2.0, fanbase_growth_multiplier: 2.0 }
      ]

      const { error } = await this.supabase
        .from('league_tiers')
        .insert(tiers)

      if (error) throw error
      console.log('League tiers created successfully')
    } catch (error) {
      console.error('Error setting up league tiers:', error)
      throw error
    }
  }

  // Create active season
  private async createActiveSeason(): Promise<void> {
    try {
      // Check if active season already exists
      const { data: activeSeason } = await this.supabase
        .from('seasons')
        .select('id')
        .eq('status', 'active')
        .single()

      if (activeSeason) {
        console.log('Active season already exists')
        return
      }

      console.log('Creating active season...')

      const startDate = new Date()
      const endDate = new Date(startDate)
      endDate.setDate(endDate.getDate() + 14) // 14-day season

      const { error } = await this.supabase
        .from('seasons')
        .insert({
          name: `Season ${startDate.getFullYear()}/${String(startDate.getMonth() + 1).padStart(2, '0')}`,
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          current_matchday: 1,
          total_matchdays: 26,
          status: 'active'
        })

      if (error) throw error
      console.log('Active season created successfully')
    } catch (error) {
      console.error('Error creating active season:', error)
      throw error
    }
  }

  // Create initial leagues for Bronze Division V (starting leagues for new players)
  private async createInitialLeagues(): Promise<void> {
    try {
      // Get Bronze Division V tier
      const { data: bronzeTier } = await this.supabase
        .from('league_tiers')
        .select('id')
        .eq('name', 'Bronze')
        .eq('division', 5)
        .single()

      if (!bronzeTier) {
        throw new Error('Bronze Division V tier not found')
      }

      // Get active season
      const { data: activeSeason } = await this.supabase
        .from('seasons')
        .select('id')
        .eq('status', 'active')
        .single()

      if (!activeSeason) {
        throw new Error('No active season found')
      }

      // Check if leagues already exist for this tier and season
      const { data: existingLeagues } = await this.supabase
        .from('leagues')
        .select('id')
        .eq('tier_id', bronzeTier.id)
        .eq('season_id', activeSeason.id)
        .limit(1)

      if (existingLeagues && existingLeagues.length > 0) {
        console.log('Initial leagues already exist')
        return
      }

      console.log('Creating initial Bronze Division V leagues...')

      // Create 5 initial Bronze Division V leagues
      const leaguesToCreate = []
      for (let i = 1; i <= 5; i++) {
        leaguesToCreate.push({
          tier_id: bronzeTier.id,
          season_id: activeSeason.id,
          name: `Bronze Division V - League ${i}`,
          max_teams: 14,
          current_teams: 0,
          status: 'active'
        })
      }

      const { error } = await this.supabase
        .from('leagues')
        .insert(leaguesToCreate)

      if (error) throw error
      console.log('Initial leagues created successfully')
    } catch (error) {
      console.error('Error creating initial leagues:', error)
      throw error
    }
  }

  // Get or create a Bronze Division V league with available space
  async getAvailableBronzeLeague(): Promise<string | null> {
    try {
      // Get Bronze Division V tier
      const { data: bronzeTier } = await this.supabase
        .from('league_tiers')
        .select('id')
        .eq('name', 'Bronze')
        .eq('division', 5)
        .single()

      if (!bronzeTier) return null

      // Get active season
      const { data: activeSeason } = await this.supabase
        .from('seasons')
        .select('id')
        .eq('status', 'active')
        .single()

      if (!activeSeason) return null

      // Find available league
      const { data: availableLeague } = await this.supabase
        .from('leagues')
        .select('id, current_teams')
        .eq('tier_id', bronzeTier.id)
        .eq('season_id', activeSeason.id)
        .eq('status', 'active')
        .lt('current_teams', 14)
        .limit(1)
        .single()

      if (availableLeague) {
        return availableLeague.id
      }

      // Create new league if none available
      const { data: newLeague } = await this.supabase
        .from('leagues')
        .insert({
          tier_id: bronzeTier.id,
          season_id: activeSeason.id,
          name: `Bronze Division V - League ${Math.floor(Math.random() * 1000)}`,
          max_teams: 14,
          current_teams: 0,
          status: 'active'
        })
        .select('id')
        .single()

      return newLeague?.id || null
    } catch (error) {
      console.error('Error getting available Bronze league:', error)
      return null
    }
  }
}

export const leagueInitializationService = new LeagueInitializationService()
