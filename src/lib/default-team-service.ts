import { createClient } from '@/lib/supabase-client'
import { Player, Team } from './types'
import { ratingService } from './rating-service'
import { leagueInitializationService } from './league-initialization'

export interface DefaultPlayer {
  name: string
  position: string
  jersey_number: number
  stats: {
    shooting: number
    speed: number
    passing: number
    defense: number
    endurance: number
    awareness: number
    goalkeeping: number
  }
  age: number
  potential: number
}

// Default roster for new teams
export const DEFAULT_ROSTER: DefaultPlayer[] = [
  {
    name: "<PERSON>",
    position: "goalkeeper",
    jersey_number: 1,
    stats: {
      shooting: 25,
      speed: 45,
      passing: 55,
      defense: 60,
      endurance: 65,
      awareness: 70,
      goalkeeping: 75
    },
    age: 22,
    potential: 85
  },
  {
    name: "<PERSON>",
    position: "left-wing",
    jersey_number: 2,
    stats: {
      shooting: 65,
      speed: 70,
      passing: 55,
      defense: 45,
      endurance: 60,
      awareness: 58,
      goalkeeping: 20
    },
    age: 20,
    potential: 82
  },
  {
    name: "<PERSON><PERSON>",
    position: "left-driver",
    jersey_number: 3,
    stats: {
      shooting: 55,
      speed: 65,
      passing: 70,
      defense: 68,
      endurance: 62,
      awareness: 65,
      goalkeeping: 25
    },
    age: 24,
    potential: 80
  },
  {
    name: "<PERSON>",
    position: "center-forward",
    jersey_number: 4,
    stats: {
      shooting: 75,
      speed: 58,
      passing: 60,
      defense: 50,
      endurance: 65,
      awareness: 72,
      goalkeeping: 20
    },
    age: 26,
    potential: 85
  },
  {
    name: "David Kovač",
    position: "point",
    jersey_number: 5,
    stats: {
      shooting: 50,
      speed: 55,
      passing: 75,
      defense: 70,
      endurance: 60,
      awareness: 78,
      goalkeeping: 25
    },
    age: 28,
    potential: 82
  },
  {
    name: "Leo Dupont",
    position: "right-driver",
    jersey_number: 6,
    stats: {
      shooting: 58,
      speed: 68,
      passing: 68,
      defense: 65,
      endurance: 64,
      awareness: 62,
      goalkeeping: 22
    },
    age: 23,
    potential: 83
  },
  {
    name: "Jalen Price",
    position: "right-wing",
    jersey_number: 7,
    stats: {
      shooting: 68,
      speed: 72,
      passing: 52,
      defense: 48,
      endurance: 58,
      awareness: 60,
      goalkeeping: 18
    },
    age: 21,
    potential: 84
  },
  {
    name: "Elias Novak",
    position: "point",
    jersey_number: 8,
    stats: {
      shooting: 48,
      speed: 52,
      passing: 72,
      defense: 68,
      endurance: 58,
      awareness: 75,
      goalkeeping: 28
    },
    age: 25,
    potential: 81
  },
  {
    name: "Giannis Petros",
    position: "center-forward",
    jersey_number: 9,
    stats: {
      shooting: 72,
      speed: 55,
      passing: 58,
      defense: 52,
      endurance: 68,
      awareness: 70,
      goalkeeping: 22
    },
    age: 27,
    potential: 83
  },
  {
    name: "Noah Lee",
    position: "right-driver",
    jersey_number: 10,
    stats: {
      shooting: 52,
      speed: 62,
      passing: 65,
      defense: 62,
      endurance: 60,
      awareness: 58,
      goalkeeping: 25
    },
    age: 22,
    potential: 80
  }
]

export class DefaultTeamService {
  private supabase = createClient()

  // Create default team for new user
  async createDefaultTeam(userId: string, teamName?: string): Promise<{
    success: boolean
    team?: Team
    error?: string
  }> {
    try {
      console.log('Creating default team for user:', userId)

      // Test database connection first
      const { data: testData, error: testError } = await this.supabase
        .from('profiles')
        .select('id')
        .eq('id', userId)
        .single()

      if (testError) {
        console.error('Database connection test failed:', testError)
        return { success: false, error: 'Database connection failed' }
      }

      console.log('Database connection test passed, user profile exists:', !!testData)

      // Check if user already has a team
      const { data: existingTeam, error: existingTeamError } = await this.supabase
        .from('teams')
        .select('id')
        .eq('user_id', userId)
        .single()

      if (existingTeamError && existingTeamError.code !== 'PGRST116') {
        console.error('Error checking existing team:', existingTeamError)
        return { success: false, error: 'Failed to check existing team' }
      }

      if (existingTeam) {
        console.log('User already has a team:', existingTeam.id)
        return { success: false, error: 'User already has a team' }
      }

      console.log('No existing team found, creating new team...')

      // Create team
      const { data: team, error: teamError } = await this.supabase
        .from('teams')
        .insert({
          user_id: userId,
          name: teamName || 'Aqua Eleven FC',
          cash_balance: 50000,
          team_rating: 65,
          matches_played: 0,
          matches_won: 0,
          matches_drawn: 0,
          matches_lost: 0,
          goals_for: 0,
          goals_against: 0,
          league_points: 0
        })
        .select()
        .single()

      if (teamError) throw teamError

      // Create default players
      const playersToCreate = DEFAULT_ROSTER.map(defaultPlayer => ({
        team_id: team.id,
        name: defaultPlayer.name,
        position: defaultPlayer.position,
        jersey_number: defaultPlayer.jersey_number,
        shooting: defaultPlayer.stats.shooting,
        speed: defaultPlayer.stats.speed,
        passing: defaultPlayer.stats.passing,
        defense: defaultPlayer.stats.defense,
        endurance: defaultPlayer.stats.endurance,
        awareness: defaultPlayer.stats.awareness,
        goalkeeping: defaultPlayer.stats.goalkeeping,
        age: defaultPlayer.age,
        potential: defaultPlayer.potential,
        experience: 0,
        morale: 75,
        fatigue: 0,
        injury_status: 'healthy',
        injury_days: 0,
        contract_salary: 1000 + Math.floor(Math.random() * 500),
        market_value: 10000 + Math.floor(Math.random() * 5000),
        special_abilities: [],
        perks: {},
        training_focus: 'balanced'
      }))

      const { error: playersError } = await this.supabase
        .from('players')
        .insert(playersToCreate)

      if (playersError) throw playersError

      // Create default facilities
      const defaultFacilities = [
        {
          team_id: team.id,
          facility_type: 'training_pool',
          level: 1,
          upgrade_cost: 5000,
          maintenance_cost: 100,
          bonus_percentage: 5
        },
        {
          team_id: team.id,
          facility_type: 'recovery_pool',
          level: 1,
          upgrade_cost: 4000,
          maintenance_cost: 80,
          bonus_percentage: 10
        },
        {
          team_id: team.id,
          facility_type: 'youth_academy',
          level: 1,
          upgrade_cost: 6000,
          maintenance_cost: 120,
          bonus_percentage: 3
        },
        {
          team_id: team.id,
          facility_type: 'aquatic_arena',
          level: 1,
          upgrade_cost: 8000,
          maintenance_cost: 150,
          bonus_percentage: 8
        }
      ]

      const { error: facilitiesError } = await this.supabase
        .from('facilities')
        .insert(defaultFacilities)

      if (facilitiesError) throw facilitiesError

      // Create default formation
      const { error: formationError } = await this.supabase
        .from('formations')
        .insert({
          team_id: team.id,
          name: 'Default Formation',
          formation_data: {
            formation_type: 'standard-7v7',
            player_positions: {
              goalkeeper: playersToCreate[0].name,
              left_wing: playersToCreate[1].name,
              left_driver: playersToCreate[2].name,
              center_forward: playersToCreate[3].name,
              point: playersToCreate[4].name,
              right_driver: playersToCreate[5].name,
              right_wing: playersToCreate[6].name
            }
          },
          is_active: true
        })

      if (formationError) throw formationError

      // Initialize user currency if it doesn't exist
      const { data: existingCurrency } = await this.supabase
        .from('user_currencies')
        .select('id')
        .eq('user_id', userId)
        .single()

      if (!existingCurrency) {
        await this.supabase
          .from('user_currencies')
          .insert({
            user_id: userId,
            coins: 50000,
            tokens: 5,
            boosters: {
              xp_boost: 1,
              recovery_boost: 1,
              injury_heal: 0
            }
          })
      }

      // Add team to a beginner league if available
      await this.assignToBeginnerLeague(team.id)

      // Update team ratings after creating default team
      await ratingService.onPlayerChange(userId)

      return { success: true, team }
    } catch (error: any) {
      console.error('Error creating default team:', error)
      console.error('Default team error details:', {
        message: error?.message,
        details: error?.details,
        hint: error?.hint,
        code: error?.code
      })
      return { success: false, error: error?.message || 'Failed to create default team' }
    }
  }

  // Assign team to beginner league (Bronze Division V)
  async assignToBeginnerLeague(teamId: string): Promise<void> {
    try {
      console.log('Assigning team to beginner league:', teamId)

      // Initialize league system if needed
      await leagueInitializationService.initializeLeagueSystem()

      // Get available Bronze Division V league
      const leagueId = await leagueInitializationService.getAvailableBronzeLeague()

      if (leagueId) {
        // Assign team to league
        await this.supabase
          .from('teams')
          .update({ league_id: leagueId })
          .eq('id', teamId)

        // Update league current_teams count
        await this.supabase.rpc('increment_league_teams', { league_id: leagueId })

        console.log('Successfully assigned team to league:', leagueId)
      } else {
        console.error('Could not find or create available Bronze league')
      }
    } catch (error) {
      console.error('Error assigning to beginner league:', error)
      // Non-critical error, team creation should still succeed
    }
  }

  // Ensure league system is properly set up
  private async ensureLeagueSystemSetup(): Promise<void> {
    try {
      // Check if we have league tiers
      const { data: tiers } = await this.supabase
        .from('league_tiers')
        .select('id')
        .limit(1)

      if (!tiers || tiers.length === 0) {
        console.log('Setting up league tiers...')
        await this.setupLeagueTiers()
      }

      // Check if we have an active season
      const { data: activeSeason } = await this.supabase
        .from('seasons')
        .select('id')
        .eq('status', 'active')
        .single()

      if (!activeSeason) {
        console.log('Creating active season...')
        await this.createActiveSeason()
      }
    } catch (error) {
      console.error('Error ensuring league system setup:', error)
    }
  }

  // Setup league tiers
  private async setupLeagueTiers(): Promise<void> {
    const tiers = [
      // Bronze Tier (Level 1)
      { name: 'Bronze', level: 1, division: 1, prize_money_base: 5000, promotion_bonus: 2000, relegation_penalty: 500 },
      { name: 'Bronze', level: 1, division: 2, prize_money_base: 4500, promotion_bonus: 1800, relegation_penalty: 450 },
      { name: 'Bronze', level: 1, division: 3, prize_money_base: 4000, promotion_bonus: 1600, relegation_penalty: 400 },
      { name: 'Bronze', level: 1, division: 4, prize_money_base: 3500, promotion_bonus: 1400, relegation_penalty: 350 },
      { name: 'Bronze', level: 1, division: 5, prize_money_base: 3000, promotion_bonus: 1200, relegation_penalty: 300 },
      // Silver Tier (Level 2)
      { name: 'Silver', level: 2, division: 1, prize_money_base: 10000, promotion_bonus: 4000, relegation_penalty: 1000 },
      { name: 'Silver', level: 2, division: 2, prize_money_base: 9000, promotion_bonus: 3600, relegation_penalty: 900 },
      { name: 'Silver', level: 2, division: 3, prize_money_base: 8000, promotion_bonus: 3200, relegation_penalty: 800 },
      { name: 'Silver', level: 2, division: 4, prize_money_base: 7000, promotion_bonus: 2800, relegation_penalty: 700 },
      { name: 'Silver', level: 2, division: 5, prize_money_base: 6000, promotion_bonus: 2400, relegation_penalty: 600 },
    ]

    await this.supabase
      .from('league_tiers')
      .insert(tiers.map(tier => ({
        ...tier,
        tactics_bonus_enabled: tier.level >= 2,
        scouting_quality_multiplier: tier.level >= 2 ? 1.0 : 0.8,
        fanbase_growth_multiplier: tier.level >= 2 ? 1.0 : 0.8
      })))
  }

  // Create active season
  private async createActiveSeason(): Promise<void> {
    const startDate = new Date()
    const endDate = new Date(startDate)
    endDate.setDate(endDate.getDate() + 14) // 14-day season

    await this.supabase
      .from('seasons')
      .insert({
        name: `Season ${startDate.getFullYear()}/${startDate.getMonth() + 1}`,
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        current_matchday: 1,
        total_matchdays: 26,
        status: 'active'
      })
  }

  // Check if user needs default team
  async checkAndCreateDefaultTeam(userId: string): Promise<{
    hasTeam: boolean
    team?: Team
  }> {
    try {
      const { data: team } = await this.supabase
        .from('teams')
        .select(`
          *,
          players:players(*),
          facilities:facilities(*)
        `)
        .eq('user_id', userId)
        .single()

      if (team) {
        return { hasTeam: true, team }
      } else {
        // Create default team
        const result = await this.createDefaultTeam(userId)
        if (result.success && result.team) {
          // Fetch the complete team data
          const { data: completeTeam } = await this.supabase
            .from('teams')
            .select(`
              *,
              players:players(*),
              facilities:facilities(*)
            `)
            .eq('id', result.team.id)
            .single()

          return { hasTeam: true, team: completeTeam }
        }
        return { hasTeam: false }
      }
    } catch (error) {
      console.error('Error checking/creating default team:', error)
      return { hasTeam: false }
    }
  }
}

export const defaultTeamService = new DefaultTeamService()
