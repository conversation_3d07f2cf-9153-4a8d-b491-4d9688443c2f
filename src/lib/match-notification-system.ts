import { createClient } from '@/lib/supabase-client'
import { Team, Match } from './types'

export interface MatchNotification {
  id: string
  user_id: string
  match_id: string
  notification_type: 'pre_match' | 'match_start' | 'half_time' | 'match_end'
  scheduled_time: string
  sent_at?: string
  message: string
  created_at: string
}

export interface UpcomingMatch {
  id: string
  match_date: string
  home_team: Team
  away_team: Team
  competition: string
  is_user_team: boolean
  time_until_match: string
  notification_status: 'pending' | 'sent' | 'none'
}

export class MatchNotificationSystem {
  private supabase = createClient()

  // Schedule notifications for a match
  async scheduleMatchNotifications(userId: string, matchId: string): Promise<void> {
    try {
      // Get match details
      const { data: match } = await this.supabase
        .from('matches')
        .select(`
          *,
          home_team:teams!matches_home_team_id_fkey(name),
          away_team:teams!matches_away_team_id_fkey(name)
        `)
        .eq('id', matchId)
        .single()

      if (!match) return

      const matchDate = new Date(match.match_date)
      
      // Schedule pre-match notification (15 minutes before)
      const preMatchTime = new Date(matchDate)
      preMatchTime.setMinutes(preMatchTime.getMinutes() - 15)
      
      await this.supabase
        .from('match_notifications')
        .insert({
          user_id: userId,
          match_id: matchId,
          notification_type: 'pre_match',
          scheduled_time: preMatchTime.toISOString(),
          message: `Your match between ${match.home_team.name} and ${match.away_team.name} starts in 15 minutes!`
        })
      
      // Schedule match start notification
      await this.supabase
        .from('match_notifications')
        .insert({
          user_id: userId,
          match_id: matchId,
          notification_type: 'match_start',
          scheduled_time: matchDate.toISOString(),
          message: `Your match between ${match.home_team.name} and ${match.away_team.name} is starting now!`
        })
      
      // Schedule half-time notification (16 minutes after start)
      const halfTimeDate = new Date(matchDate)
      halfTimeDate.setMinutes(halfTimeDate.getMinutes() + 16)
      
      await this.supabase
        .from('match_notifications')
        .insert({
          user_id: userId,
          match_id: matchId,
          notification_type: 'half_time',
          scheduled_time: halfTimeDate.toISOString(),
          message: `Half-time! Make tactical adjustments for ${match.home_team.name} vs ${match.away_team.name}.`
        })
      
      // Schedule match end notification (32 minutes after start)
      const matchEndDate = new Date(matchDate)
      matchEndDate.setMinutes(matchEndDate.getMinutes() + 32)
      
      await this.supabase
        .from('match_notifications')
        .insert({
          user_id: userId,
          match_id: matchId,
          notification_type: 'match_end',
          scheduled_time: matchEndDate.toISOString(),
          message: `Your match between ${match.home_team.name} and ${match.away_team.name} has ended.`
        })
    } catch (error) {
      console.error('Error scheduling match notifications:', error)
    }
  }

  // Get upcoming matches with notification status
  async getUpcomingMatches(userId: string, limit: number = 5): Promise<UpcomingMatch[]> {
    try {
      // Get user's team
      const { data: teams } = await this.supabase
        .from('teams')
        .select('id')
        .eq('user_id', userId)
      
      if (!teams || teams.length === 0) return []
      
      const teamIds = teams.map(team => team.id)
      
      // Get upcoming matches
      const { data: matches } = await this.supabase
        .from('matches')
        .select(`
          id,
          match_date,
          home_team_id,
          away_team_id,
          competition,
          home_team:teams!matches_home_team_id_fkey(id, name),
          away_team:teams!matches_away_team_id_fkey(id, name)
        `)
        .or(`home_team_id.in.(${teamIds.join(',')}),away_team_id.in.(${teamIds.join(',')})`)
        .gte('match_date', new Date().toISOString())
        .order('match_date', { ascending: true })
        .limit(limit)
      
      if (!matches) return []
      
      // Get notification status for these matches
      const { data: notifications } = await this.supabase
        .from('match_notifications')
        .select('match_id, notification_type, sent_at')
        .eq('user_id', userId)
        .in('match_id', matches.map(m => m.id))
      
      // Format results
      return matches.map(match => {
        const matchDate = new Date(match.match_date)
        const now = new Date()
        const diffMs = matchDate.getTime() - now.getTime()
        const diffMins = Math.floor(diffMs / 60000)
        
        let timeUntilMatch = ''
        if (diffMins < 60) {
          timeUntilMatch = `${diffMins} minutes`
        } else if (diffMins < 1440) {
          timeUntilMatch = `${Math.floor(diffMins / 60)} hours`
        } else {
          timeUntilMatch = `${Math.floor(diffMins / 1440)} days`
        }
        
        // Check notification status
        const matchNotifications = notifications?.filter(n => n.match_id === match.id) || []
        const preMatchNotification = matchNotifications.find(n => n.notification_type === 'pre_match')
        
        let notificationStatus: 'pending' | 'sent' | 'none' = 'none'
        if (preMatchNotification) {
          notificationStatus = preMatchNotification.sent_at ? 'sent' : 'pending'
        }
        
        return {
          id: match.id,
          match_date: match.match_date,
          home_team: match.home_team,
          away_team: match.away_team,
          competition: match.competition,
          is_user_team: teamIds.includes(match.home_team_id) || teamIds.includes(match.away_team_id),
          time_until_match: timeUntilMatch,
          notification_status: notificationStatus
        }
      })
    } catch (error) {
      console.error('Error getting upcoming matches:', error)
      return []
    }
  }

  // Process pending notifications
  async processPendingNotifications(): Promise<number> {
    try {
      const now = new Date()
      
      // Get notifications that are due
      const { data: dueNotifications } = await this.supabase
        .from('match_notifications')
        .select('*')
        .is('sent_at', null)
        .lte('scheduled_time', now.toISOString())
      
      if (!dueNotifications || dueNotifications.length === 0) return 0
      
      // In a real app, this would send push notifications, emails, etc.
      // For now, we'll just mark them as sent
      for (const notification of dueNotifications) {
        await this.supabase
          .from('match_notifications')
          .update({ sent_at: now.toISOString() })
          .eq('id', notification.id)
      }
      
      return dueNotifications.length
    } catch (error) {
      console.error('Error processing notifications:', error)
      return 0
    }
  }

  // Mark notification as read
  async markNotificationAsRead(notificationId: string): Promise<void> {
    try {
      await this.supabase
        .from('match_notifications')
        .update({ sent_at: new Date().toISOString() })
        .eq('id', notificationId)
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  // Get user's unread notifications
  async getUnreadNotifications(userId: string): Promise<MatchNotification[]> {
    try {
      const { data } = await this.supabase
        .from('match_notifications')
        .select(`
          *,
          match:matches(
            id,
            match_date,
            home_team:teams!matches_home_team_id_fkey(name),
            away_team:teams!matches_away_team_id_fkey(name)
          )
        `)
        .eq('user_id', userId)
        .is('sent_at', null)
        .order('scheduled_time', { ascending: true })
      
      return data || []
    } catch (error) {
      console.error('Error getting unread notifications:', error)
      return []
    }
  }
}

export const matchNotificationSystem = new MatchNotificationSystem()
