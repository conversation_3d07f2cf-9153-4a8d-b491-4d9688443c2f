export interface Player {
  id: string
  team_id: string
  name: string
  position: PlayerPosition
  jersey_number: number
  shooting: number
  speed: number
  passing: number
  defense: number
  endurance: number
  awareness: number
  goalkeeping: number
  experience: number
  age: number
  morale: number
  fatigue: number
  injury_status: 'healthy' | 'minor' | 'major' | 'critical'
  injury_days: number
  contract_salary: number
  contract_expires_at?: string
  market_value: number
  potential: number
  special_abilities: string[]
  perks: Record<string, any>
  training_focus: 'shooting' | 'speed' | 'passing' | 'defense' | 'endurance' | 'awareness' | 'goalkeeping' | 'balanced'
  created_at: string
  updated_at: string
}

export interface Team {
  id: string
  user_id: string
  name: string
  logo_url?: string
  formation: string
  team_rating: number
  league_id?: string
  league_position: number
  league_points: number
  matches_played: number
  matches_won: number
  matches_drawn: number
  matches_lost: number
  goals_for: number
  goals_against: number
  cash_balance: number
  fan_base: number
  created_at: string
  updated_at: string
  players?: Player[]
  league?: League
  facilities?: Facility[]
}

export interface Profile {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  manager_level: number
  experience_points: number
  created_at: string
  updated_at: string
  team?: Team
  userCurrency?: UserCurrency
}

export interface DailyLogin {
  id: string
  user_id: string
  login_date: string
  streak_count: number
  rewards_claimed: Record<string, any>
  created_at: string
}

export interface UserCurrency {
  id: string
  user_id: string
  coins: number
  tokens: number
  boosters: {
    xp_boost: number
    recovery_boost: number
    injury_heal: number
  }
  created_at: string
  updated_at: string
}

export interface Achievement {
  id: string
  user_id: string
  achievement_type: string
  achievement_name: string
  description?: string
  reward_coins: number
  reward_tokens: number
  unlocked_at: string
  created_at: string
}

export interface Match {
  id: string
  home_team_id: string
  away_team_id: string
  home_score: number
  away_score: number
  match_date: string
  competition: string
  created_at: string
}

export type PlayerPosition = 
  | 'goalkeeper'
  | 'left-wing'
  | 'right-wing'
  | 'left-driver'
  | 'right-driver'
  | 'center-forward'
  | 'point'

export interface Formation {
  id: string
  name: string
  positions: {
    [key in PlayerPosition]?: {
      x: number
      y: number
      playerId?: string
    }
  }
}

export const PLAYER_POSITIONS: PlayerPosition[] = [
  'goalkeeper',
  'left-wing',
  'right-wing',
  'left-driver',
  'right-driver',
  'center-forward',
  'point'
]

export const POSITION_LABELS: Record<PlayerPosition, string> = {
  'goalkeeper': 'Goalkeeper',
  'left-wing': 'Left Wing',
  'right-wing': 'Right Wing',
  'left-driver': 'Left Driver',
  'right-driver': 'Right Driver',
  'center-forward': 'Center Forward',
  'point': 'Point'
}

export const DEFAULT_FORMATION: Formation = {
  id: 'default',
  name: 'Square 3x2',
  positions: {
    'goalkeeper': { x: 15, y: 50 },
    'point': { x: 45, y: 50 },
    'left-driver': { x: 55, y: 25 },
    'right-driver': { x: 55, y: 75 },
    'left-wing': { x: 75, y: 25 },
    'right-wing': { x: 75, y: 75 },
    'center-forward': { x: 75, y: 50 }
  }
}

// Game Engine Types

export interface League {
  id: string
  name: string
  level: number
  max_teams: number
  season_start_date?: string
  season_end_date?: string
  current_matchday: number
  status: 'active' | 'finished' | 'preparing'
  prize_money: number
  created_at: string
  updated_at: string
  teams?: Team[]
}

export interface Competition {
  id: string
  name: string
  type: 'cup' | 'tournament' | 'friendly'
  status: 'upcoming' | 'active' | 'finished'
  entry_fee: number
  prize_money: number
  max_participants: number
  current_round: number
  start_date?: string
  end_date?: string
  created_at: string
  updated_at: string
  participants?: Team[]
}

export interface TrainingSession {
  id: string
  team_id: string
  training_type: 'shooting' | 'speed' | 'passing' | 'defense' | 'endurance' | 'awareness' | 'goalkeeping' | 'tactical'
  intensity: 'light' | 'medium' | 'high' | 'extreme'
  duration_minutes: number
  cost: number
  experience_gained: number
  fatigue_added: number
  completed_at: string
  created_at: string
  player_training?: PlayerTraining[]
}

export interface PlayerTraining {
  id: string
  player_id: string
  training_session_id: string
  stat_improvements: Record<string, number>
  experience_gained: number
  fatigue_added: number
  created_at: string
}

export interface Facility {
  id: string
  team_id: string
  facility_type: 'training_pool' | 'recovery_pool' | 'youth_academy' | 'aquatic_arena' | 'medical_center'
  level: number
  upgrade_cost: number
  maintenance_cost: number
  bonus_percentage: number
  created_at: string
  updated_at: string
}

export interface TransferListing {
  id: string
  player_id: string
  selling_team_id?: string
  asking_price: number
  transfer_type: 'sale' | 'loan' | 'free'
  status: 'available' | 'sold' | 'withdrawn'
  expires_at?: string
  created_at: string
  updated_at: string
  player?: Player
  selling_team?: Team
  bids?: TransferBid[]
}

export interface TransferBid {
  id: string
  transfer_id: string
  bidding_team_id: string
  bid_amount: number
  status: 'pending' | 'accepted' | 'rejected' | 'outbid'
  created_at: string
  updated_at: string
  bidding_team?: Team
}

export interface MatchEvent {
  id: string
  match_id: string
  minute: number
  event_type: 'goal' | 'exclusion' | 'timeout' | 'substitution' | 'penalty' | 'save'
  player_id?: string
  team_id: string
  description?: string
  created_at: string
  player?: Player
  team?: Team
}

// Game Mechanics Types

export interface MatchSimulation {
  match: Match
  home_team: Team
  away_team: Team
  events: MatchEvent[]
  current_minute: number
  home_score: number
  away_score: number
  status: 'not_started' | 'first_quarter' | 'second_quarter' | 'third_quarter' | 'fourth_quarter' | 'finished'
}

export interface TacticalSetup {
  formation: Formation
  offensive_style: 'balanced' | 'counter_attack' | 'power_play' | 'possession'
  defensive_style: 'zone' | 'man_to_man' | 'press' | 'hybrid'
  goalkeeper_strategy: 'conservative' | 'aggressive' | 'distribution'
  substitutions: PlayerSubstitution[]
}

export interface PlayerSubstitution {
  minute: number
  player_out_id: string
  player_in_id: string
  reason: 'tactical' | 'injury' | 'fatigue' | 'exclusion'
}

export interface PlayerPerformance {
  player_id: string
  goals: number
  assists: number
  saves: number
  exclusions: number
  shots_taken: number
  shots_on_target: number
  passes_completed: number
  passes_attempted: number
  steals: number
  turnovers: number
  rating: number
}

export interface SpecialAbility {
  id: string
  name: string
  description: string
  type: 'offensive' | 'defensive' | 'goalkeeping' | 'utility'
  unlock_level: number
  stat_requirements: Record<string, number>
  effects: Record<string, number>
}

// Game Constants

export const SPECIAL_ABILITIES: SpecialAbility[] = [
  {
    id: 'power_shot',
    name: 'Power Shot',
    description: 'Increases shooting power and accuracy',
    type: 'offensive',
    unlock_level: 5,
    stat_requirements: { shooting: 75 },
    effects: { shooting: 10 }
  },
  {
    id: 'quick_counter',
    name: 'Quick Counter',
    description: 'Faster transitions from defense to attack',
    type: 'utility',
    unlock_level: 8,
    stat_requirements: { speed: 70, awareness: 65 },
    effects: { speed: 5, awareness: 5 }
  },
  {
    id: 'shot_blocker',
    name: 'Shot Blocker',
    description: 'Better at blocking and intercepting shots',
    type: 'defensive',
    unlock_level: 6,
    stat_requirements: { defense: 75, awareness: 60 },
    effects: { defense: 8, awareness: 3 }
  },
  {
    id: 'fast_break_finisher',
    name: 'Fast Break Finisher',
    description: 'Excels in counter-attack situations',
    type: 'offensive',
    unlock_level: 7,
    stat_requirements: { swimming: 75, shooting: 65 },
    effects: { swimming: 5, shooting: 5 }
  },
  {
    id: 'goalkeeper_reflexes',
    name: 'Lightning Reflexes',
    description: 'Enhanced reaction time for saves',
    type: 'goalkeeping',
    unlock_level: 10,
    stat_requirements: { goalkeeping: 80, awareness: 70 },
    effects: { goalkeeping: 12, awareness: 5 }
  }
]

export const TRAINING_TYPES = {
  shooting: {
    name: 'Shot Practice',
    description: 'Improves shooting power and accuracy',
    primary_stat: 'shooting',
    secondary_stats: ['awareness'],
    base_cost: 100,
    base_fatigue: 5
  },
  speed: {
    name: 'Sprint Drills',
    description: 'Boosts speed and acceleration',
    primary_stat: 'speed',
    secondary_stats: ['endurance'],
    base_cost: 80,
    base_fatigue: 8
  },
  passing: {
    name: 'Passing Drills',
    description: 'Enhances ball distribution and vision',
    primary_stat: 'passing',
    secondary_stats: ['awareness'],
    base_cost: 90,
    base_fatigue: 4
  },
  defense: {
    name: 'Defensive Drills',
    description: 'Improves tackling and positioning',
    primary_stat: 'defense',
    secondary_stats: ['awareness', 'speed'],
    base_cost: 95,
    base_fatigue: 6
  },
  endurance: {
    name: 'Swim Sets',
    description: 'Builds stamina and resistance to fatigue',
    primary_stat: 'endurance',
    secondary_stats: ['speed'],
    base_cost: 70,
    base_fatigue: 3
  },
  awareness: {
    name: 'Tactical Simulations',
    description: 'Enhances game intelligence and positioning',
    primary_stat: 'awareness',
    secondary_stats: ['passing', 'defense'],
    base_cost: 110,
    base_fatigue: 4
  },
  goalkeeping: {
    name: 'Goalkeeper Training',
    description: 'Specialized training for goalkeepers',
    primary_stat: 'goalkeeping',
    secondary_stats: ['awareness', 'endurance'],
    base_cost: 120,
    base_fatigue: 6
  },
  tactical: {
    name: 'Team Tactics',
    description: 'Improves team coordination and chemistry',
    primary_stat: 'awareness',
    secondary_stats: ['passing', 'defense'],
    base_cost: 150,
    base_fatigue: 5
  }
} as const

export const FACILITY_TYPES = {
  training_pool: {
    name: 'Training Pool',
    description: 'Improves training effectiveness',
    max_level: 10,
    base_cost: 5000,
    base_maintenance: 100,
    bonus_type: 'training_xp'
  },
  recovery_pool: {
    name: 'Recovery Pool',
    description: 'Reduces player fatigue faster',
    max_level: 8,
    base_cost: 7500,
    base_maintenance: 150,
    bonus_type: 'fatigue_recovery'
  },
  youth_academy: {
    name: 'Youth Academy',
    description: 'Generates better youth players',
    max_level: 10,
    base_cost: 10000,
    base_maintenance: 200,
    bonus_type: 'youth_quality'
  },
  aquatic_arena: {
    name: 'Aquatic Arena',
    description: 'Increases fan base and revenue',
    max_level: 15,
    base_cost: 15000,
    base_maintenance: 300,
    bonus_type: 'revenue'
  },
  medical_center: {
    name: 'Medical Center',
    description: 'Faster injury recovery',
    max_level: 8,
    base_cost: 8000,
    base_maintenance: 180,
    bonus_type: 'injury_recovery'
  }
} as const

// Player Perks System
export interface PlayerPerk {
  id: string
  name: string
  description: string
  requirement: {
    stat: keyof Player
    level: number
  }
  effect: {
    type: 'stat_boost' | 'special_ability' | 'training_bonus'
    value: number
    target?: string
  }
}

export const PLAYER_PERKS: Record<string, PlayerPerk[]> = {
  shooting: [
    {
      id: 'power_shot',
      name: 'Power Shot',
      description: '+10% shooting accuracy in critical moments',
      requirement: { stat: 'shooting', level: 70 },
      effect: { type: 'special_ability', value: 10, target: 'shooting_accuracy' }
    },
    {
      id: 'clutch_scorer',
      name: 'Clutch Scorer',
      description: '+15% shooting in final quarter',
      requirement: { stat: 'shooting', level: 85 },
      effect: { type: 'special_ability', value: 15, target: 'clutch_shooting' }
    }
  ],
  speed: [
    {
      id: 'sprint_burst',
      name: 'Sprint Burst',
      description: '+20% speed for first 2 minutes of each quarter',
      requirement: { stat: 'speed', level: 70 },
      effect: { type: 'special_ability', value: 20, target: 'early_speed' }
    },
    {
      id: 'endurance_runner',
      name: 'Endurance Runner',
      description: 'Slower fatigue accumulation',
      requirement: { stat: 'speed', level: 85 },
      effect: { type: 'special_ability', value: -25, target: 'fatigue_rate' }
    }
  ],
  defense: [
    {
      id: 'steal_master',
      name: 'Steal Master',
      description: '+15% chance to steal ball',
      requirement: { stat: 'defense', level: 70 },
      effect: { type: 'special_ability', value: 15, target: 'steal_chance' }
    },
    {
      id: 'defensive_wall',
      name: 'Defensive Wall',
      description: '+10% team defense when on field',
      requirement: { stat: 'defense', level: 85 },
      effect: { type: 'stat_boost', value: 10, target: 'team_defense' }
    }
  ],
  goalkeeping: [
    {
      id: 'reflex_save',
      name: 'Reflex Save',
      description: '+20% save chance on close shots',
      requirement: { stat: 'goalkeeping', level: 70 },
      effect: { type: 'special_ability', value: 20, target: 'close_save' }
    },
    {
      id: 'penalty_specialist',
      name: 'Penalty Specialist',
      description: '+30% save chance on penalties',
      requirement: { stat: 'goalkeeping', level: 85 },
      effect: { type: 'special_ability', value: 30, target: 'penalty_save' }
    }
  ]
}
