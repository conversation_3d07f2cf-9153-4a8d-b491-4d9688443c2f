import { createClient } from '@/lib/supabase-client'
import { DailyLogin, UserCurrency, Achievement } from './types'
import { unifiedCurrencyService } from './unified-currency-service'

export interface DailyReward {
  day: number
  coins: number
  tokens: number
  boosters?: {
    type: 'xp_boost' | 'recovery_boost' | 'injury_heal'
    amount: number
  }
  special?: boolean
}

// Daily reward schedule (7-day cycle)
export const DAILY_REWARDS: DailyReward[] = [
  { day: 1, coins: 1000, tokens: 0 },
  { day: 2, coins: 1500, tokens: 0 },
  { day: 3, coins: 2000, tokens: 1 },
  { day: 4, coins: 2500, tokens: 0, boosters: { type: 'xp_boost', amount: 1 } },
  { day: 5, coins: 3000, tokens: 1 },
  { day: 6, coins: 4000, tokens: 2, boosters: { type: 'recovery_boost', amount: 1 } },
  { day: 7, coins: 5000, tokens: 5, boosters: { type: 'injury_heal', amount: 2 }, special: true }
]

export class DailyRewardsService {
  private supabase = createClient()

  async checkDailyLogin(userId: string): Promise<{
    canClaim: boolean
    streak: number
    reward?: DailyReward
    lastLogin?: string
  }> {
    const today = new Date().toISOString().split('T')[0]
    
    // Get the most recent login
    const { data: lastLogin } = await this.supabase
      .from('daily_logins')
      .select('*')
      .eq('user_id', userId)
      .order('login_date', { ascending: false })
      .limit(1)
      .single()

    if (!lastLogin) {
      // First time login
      return {
        canClaim: true,
        streak: 1,
        reward: DAILY_REWARDS[0]
      }
    }

    const lastLoginDate = lastLogin.login_date
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    const yesterdayStr = yesterday.toISOString().split('T')[0]

    if (lastLoginDate === today) {
      // Already claimed today
      return {
        canClaim: false,
        streak: lastLogin.streak_count,
        lastLogin: lastLoginDate
      }
    }

    let newStreak: number
    if (lastLoginDate === yesterdayStr) {
      // Consecutive day
      newStreak = lastLogin.streak_count + 1
    } else {
      // Streak broken
      newStreak = 1
    }

    const rewardIndex = (newStreak - 1) % DAILY_REWARDS.length
    const reward = DAILY_REWARDS[rewardIndex]

    return {
      canClaim: true,
      streak: newStreak,
      reward,
      lastLogin: lastLoginDate
    }
  }

  async claimDailyReward(userId: string): Promise<{
    success: boolean
    reward?: DailyReward
    newStreak?: number
    error?: string
  }> {
    try {
      const loginCheck = await this.checkDailyLogin(userId)
      
      if (!loginCheck.canClaim) {
        return {
          success: false,
          error: 'Daily reward already claimed today'
        }
      }

      const today = new Date().toISOString().split('T')[0]
      const reward = loginCheck.reward!
      
      // Start transaction
      const { data: loginData, error: loginError } = await this.supabase
        .from('daily_logins')
        .insert({
          user_id: userId,
          login_date: today,
          streak_count: loginCheck.streak,
          rewards_claimed: {
            coins: reward.coins,
            tokens: reward.tokens,
            boosters: reward.boosters || null
          }
        })
        .select()
        .single()

      if (loginError) {
        throw loginError
      }

      // Update user currencies using unified service for broadcasting
      await unifiedCurrencyService.awardCoins(
        userId,
        reward.coins,
        `Daily reward: Day ${loginCheck.streak}`,
        'daily_login'
      )

      // Check for streak achievements
      await this.checkStreakAchievements(userId, loginCheck.streak)

      return {
        success: true,
        reward,
        newStreak: loginCheck.streak
      }
    } catch (error) {
      console.error('Error claiming daily reward:', error)
      return {
        success: false,
        error: 'Failed to claim daily reward'
      }
    }
  }

  private async updateUserCurrency(userId: string, reward: DailyReward): Promise<void> {
    // Get current currency
    const { data: currency } = await this.supabase
      .from('user_currencies')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (!currency) {
      // Create new currency record
      await this.supabase
        .from('user_currencies')
        .insert({
          user_id: userId,
          coins: reward.coins,
          tokens: reward.tokens,
          boosters: {
            xp_boost: reward.boosters?.type === 'xp_boost' ? reward.boosters.amount : 0,
            recovery_boost: reward.boosters?.type === 'recovery_boost' ? reward.boosters.amount : 0,
            injury_heal: reward.boosters?.type === 'injury_heal' ? reward.boosters.amount : 0
          }
        })
    } else {
      // Update existing currency
      const newBoosters = { ...currency.boosters }
      if (reward.boosters) {
        newBoosters[reward.boosters.type] += reward.boosters.amount
      }

      await this.supabase
        .from('user_currencies')
        .update({
          coins: currency.coins + reward.coins,
          tokens: currency.tokens + reward.tokens,
          boosters: newBoosters
        })
        .eq('user_id', userId)
    }
  }

  private async checkStreakAchievements(userId: string, streak: number): Promise<void> {
    const achievements = []

    if (streak === 7) {
      achievements.push({
        achievement_type: 'login_streak',
        achievement_name: 'Week Warrior',
        description: 'Login for 7 consecutive days',
        reward_coins: 2000,
        reward_tokens: 1
      })
    }

    if (streak === 30) {
      achievements.push({
        achievement_type: 'login_streak',
        achievement_name: 'Monthly Master',
        description: 'Login for 30 consecutive days',
        reward_coins: 10000,
        reward_tokens: 10
      })
    }

    if (streak === 100) {
      achievements.push({
        achievement_type: 'login_streak',
        achievement_name: 'Dedication Legend',
        description: 'Login for 100 consecutive days',
        reward_coins: 50000,
        reward_tokens: 50
      })
    }

    for (const achievement of achievements) {
      // Check if already unlocked
      const { data: existing } = await this.supabase
        .from('achievements')
        .select('id')
        .eq('user_id', userId)
        .eq('achievement_name', achievement.achievement_name)
        .single()

      if (!existing) {
        await this.supabase
          .from('achievements')
          .insert({
            user_id: userId,
            ...achievement
          })

        // Award achievement rewards
        await this.updateUserCurrency(userId, {
          day: 0,
          coins: achievement.reward_coins,
          tokens: achievement.reward_tokens
        })
      }
    }
  }

  async getUserCurrency(userId: string): Promise<UserCurrency | null> {
    const { data } = await this.supabase
      .from('user_currencies')
      .select('*')
      .eq('user_id', userId)
      .single()

    return data
  }

  async spendCurrency(
    userId: string, 
    cost: { coins?: number; tokens?: number }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const currency = await this.getUserCurrency(userId)
      
      if (!currency) {
        return { success: false, error: 'Currency record not found' }
      }

      if (cost.coins && currency.coins < cost.coins) {
        return { success: false, error: 'Insufficient coins' }
      }

      if (cost.tokens && currency.tokens < cost.tokens) {
        return { success: false, error: 'Insufficient tokens' }
      }

      await this.supabase
        .from('user_currencies')
        .update({
          coins: currency.coins - (cost.coins || 0),
          tokens: currency.tokens - (cost.tokens || 0)
        })
        .eq('user_id', userId)

      return { success: true }
    } catch (error) {
      console.error('Error spending currency:', error)
      return { success: false, error: 'Failed to spend currency' }
    }
  }
}

export const dailyRewardsService = new DailyRewardsService()
