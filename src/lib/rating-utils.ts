import { Player, Team } from './types'
import { calculatePlayerOverall } from './game-engine'
import { calculateTeamRating } from './progression-system'

/**
 * Calculate ratings directly from player data
 * This ensures all components use the same calculation method
 */
export function calculateRatingsFromPlayers(players: Player[]): {
  avgRating: number
  playerCount: number
} {
  if (!players || players.length === 0) {
    return {
      avgRating: 0,
      playerCount: 0
    }
  }

  const playerRatings = players.map(player => calculatePlayerOverall(player))
  const avgRating = Math.round(playerRatings.reduce((sum, rating) => sum + rating, 0) / playerRatings.length)

  return {
    avgRating,
    playerCount: players.length
  }
}

/**
 * Calculate team rating directly from team data
 */
export function calculateTeamRatingFromTeam(team: Team | null): number {
  if (!team) return 0
  return calculateTeamRating(team)
}

/**
 * Get rating color based on value
 */
export function getRatingColor(rating: number): string {
  if (rating >= 85) return 'text-green-600'
  if (rating >= 75) return 'text-blue-600'
  if (rating >= 65) return 'text-yellow-600'
  if (rating >= 50) return 'text-orange-600'
  return 'text-red-600'
}

/**
 * Get rating description based on value
 */
export function getRatingDescription(rating: number): string {
  if (rating >= 90) return 'World Class'
  if (rating >= 85) return 'Elite'
  if (rating >= 80) return 'Excellent'
  if (rating >= 75) return 'Very Good'
  if (rating >= 70) return 'Good'
  if (rating >= 65) return 'Above Average'
  if (rating >= 60) return 'Average'
  if (rating >= 50) return 'Below Average'
  if (rating >= 40) return 'Poor'
  return 'Very Poor'
}
