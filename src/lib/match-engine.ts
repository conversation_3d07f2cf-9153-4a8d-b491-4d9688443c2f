import { 
  Match, 
  Team, 
  Player, 
  MatchEvent, 
  MatchSimulation, 
  TacticalSetup, 
  PlayerPerformance,
  PlayerSubstitution 
} from './types'
import { calculatePlayerOverall } from './game-engine'

// Match Simulation Engine

export function simulateMatch(
  homeTeam: Team,
  awayTeam: Team,
  homeTactics: TacticalSetup,
  awayTactics: TacticalSetup
): MatchSimulation {
  const match: Match = {
    id: crypto.randomUUID(),
    home_team_id: homeTeam.id,
    away_team_id: awayTeam.id,
    home_score: 0,
    away_score: 0,
    match_date: new Date().toISOString(),
    competition: 'league',
    created_at: new Date().toISOString()
  }

  const simulation: MatchSimulation = {
    match,
    home_team: homeTeam,
    away_team: awayTeam,
    events: [],
    current_minute: 0,
    home_score: 0,
    away_score: 0,
    status: 'not_started'
  }

  // Simulate each quarter (8 minutes each)
  for (let quarter = 1; quarter <= 4; quarter++) {
    simulation.status = `${getQuarterName(quarter)}` as any
    simulateQuarter(simulation, homeTactics, awayTactics, quarter)
  }

  simulation.status = 'finished'
  simulation.match.home_score = simulation.home_score
  simulation.match.away_score = simulation.away_score

  return simulation
}

function getQuarterName(quarter: number): string {
  const names = ['first_quarter', 'second_quarter', 'third_quarter', 'fourth_quarter']
  return names[quarter - 1]
}

function simulateQuarter(
  simulation: MatchSimulation,
  homeTactics: TacticalSetup,
  awayTactics: TacticalSetup,
  quarter: number
) {
  const quarterStartMinute = (quarter - 1) * 8
  const quarterEndMinute = quarter * 8

  // Calculate team strengths
  const homeStrength = calculateTeamStrength(simulation.home_team, homeTactics)
  const awayStrength = calculateTeamStrength(simulation.away_team, awayTactics)

  // Simulate events during the quarter
  for (let minute = quarterStartMinute; minute < quarterEndMinute; minute++) {
    simulation.current_minute = minute

    // Check for random events
    if (Math.random() < 0.15) { // 15% chance of event per minute
      const event = generateRandomEvent(
        simulation,
        homeStrength,
        awayStrength,
        homeTactics,
        awayTactics,
        minute
      )
      
      if (event) {
        simulation.events.push(event)
        
        // Update score if it's a goal
        if (event.event_type === 'goal') {
          if (event.team_id === simulation.home_team.id) {
            simulation.home_score++
          } else {
            simulation.away_score++
          }
        }
      }
    }
  }
}

function calculateTeamStrength(team: Team, tactics: TacticalSetup): number {
  if (!team.players) return 50

  const activePlayer = team.players.slice(0, 7) // Starting 7
  const averageRating = activePlayer.reduce((sum, player) => {
    return sum + calculatePlayerOverall(player)
  }, 0) / activePlayer.length

  // Apply tactical bonuses
  const tacticalBonus = calculateTacticalBonus(tactics, team)
  
  // Apply fatigue penalty
  const averageFatigue = activePlayer.reduce((sum, player) => sum + player.fatigue, 0) / activePlayer.length
  const fatigueMultiplier = 1 - (averageFatigue / 200) // Max 50% penalty

  // Apply morale bonus
  const averageMorale = activePlayer.reduce((sum, player) => sum + player.morale, 0) / activePlayer.length
  const moraleMultiplier = 0.5 + (averageMorale / 200) // 0.5 to 1.0

  return averageRating * (1 + tacticalBonus / 100) * fatigueMultiplier * moraleMultiplier
}

function calculateTacticalBonus(tactics: TacticalSetup, team: Team): number {
  // This is a simplified tactical calculation
  // In a real implementation, this would be much more complex
  
  let bonus = 0

  // Formation bonus based on player positions
  bonus += 5 // Base formation bonus

  // Style bonuses (simplified)
  if (tactics.offensive_style === 'counter_attack') {
    bonus += 3 // Good for fast teams
  }
  
  if (tactics.defensive_style === 'press') {
    bonus += 2 // Good for high-energy teams
  }

  return bonus
}

function generateRandomEvent(
  simulation: MatchSimulation,
  homeStrength: number,
  awayStrength: number,
  homeTactics: TacticalSetup,
  awayTactics: TacticalSetup,
  minute: number
): MatchEvent | null {
  const totalStrength = homeStrength + awayStrength
  const homeAdvantage = homeStrength / totalStrength

  // Determine which team has possession
  const homeHasPossession = Math.random() < homeAdvantage

  const attackingTeam = homeHasPossession ? simulation.home_team : simulation.away_team
  const defendingTeam = homeHasPossession ? simulation.away_team : simulation.home_team

  // Determine event type based on probabilities
  const eventRoll = Math.random()

  if (eventRoll < 0.4) {
    // Goal attempt
    return generateGoalEvent(simulation, attackingTeam, defendingTeam, minute)
  } else if (eventRoll < 0.6) {
    // Exclusion
    return generateExclusionEvent(simulation, attackingTeam, defendingTeam, minute)
  } else if (eventRoll < 0.8) {
    // Save
    return generateSaveEvent(simulation, attackingTeam, defendingTeam, minute)
  } else if (eventRoll < 0.9) {
    // Penalty
    return generatePenaltyEvent(simulation, attackingTeam, defendingTeam, minute)
  }

  return null
}

function generateGoalEvent(
  simulation: MatchSimulation,
  attackingTeam: Team,
  defendingTeam: Team,
  minute: number
): MatchEvent {
  // Select a random attacking player (excluding goalkeeper)
  const attackingPlayers = attackingTeam.players?.filter(p => p.position !== 'goalkeeper') || []
  const scorer = attackingPlayers[Math.floor(Math.random() * attackingPlayers.length)]

  // Calculate goal probability based on player stats and defending team
  const attackingStrength = calculatePlayerOverall(scorer)
  const defendingGoalkeeper = defendingTeam.players?.find(p => p.position === 'goalkeeper')
  const defendingStrength = defendingGoalkeeper ? calculatePlayerOverall(defendingGoalkeeper) : 50

  const goalProbability = (attackingStrength / (attackingStrength + defendingStrength)) * 0.6

  const isGoal = Math.random() < goalProbability

  return {
    id: crypto.randomUUID(),
    match_id: simulation.match.id,
    minute,
    event_type: isGoal ? 'goal' : 'save',
    player_id: isGoal ? scorer.id : defendingGoalkeeper?.id,
    team_id: isGoal ? attackingTeam.id : defendingTeam.id,
    description: isGoal 
      ? `${scorer.name} scores!` 
      : `Great save by ${defendingGoalkeeper?.name || 'goalkeeper'}!`,
    created_at: new Date().toISOString()
  }
}

function generateExclusionEvent(
  simulation: MatchSimulation,
  attackingTeam: Team,
  defendingTeam: Team,
  minute: number
): MatchEvent {
  // Exclusions are more likely for defending players
  const excludedTeam = Math.random() < 0.7 ? defendingTeam : attackingTeam
  const players = excludedTeam.players?.filter(p => p.position !== 'goalkeeper') || []
  const excludedPlayer = players[Math.floor(Math.random() * players.length)]

  return {
    id: crypto.randomUUID(),
    match_id: simulation.match.id,
    minute,
    event_type: 'exclusion',
    player_id: excludedPlayer.id,
    team_id: excludedTeam.id,
    description: `${excludedPlayer.name} excluded for 20 seconds`,
    created_at: new Date().toISOString()
  }
}

function generateSaveEvent(
  simulation: MatchSimulation,
  attackingTeam: Team,
  defendingTeam: Team,
  minute: number
): MatchEvent {
  const goalkeeper = defendingTeam.players?.find(p => p.position === 'goalkeeper')
  
  return {
    id: crypto.randomUUID(),
    match_id: simulation.match.id,
    minute,
    event_type: 'save',
    player_id: goalkeeper?.id,
    team_id: defendingTeam.id,
    description: `Excellent save by ${goalkeeper?.name || 'goalkeeper'}!`,
    created_at: new Date().toISOString()
  }
}

function generatePenaltyEvent(
  simulation: MatchSimulation,
  attackingTeam: Team,
  defendingTeam: Team,
  minute: number
): MatchEvent {
  return {
    id: crypto.randomUUID(),
    match_id: simulation.match.id,
    minute,
    event_type: 'penalty',
    player_id: undefined,
    team_id: attackingTeam.id,
    description: `Penalty awarded to ${attackingTeam.name}`,
    created_at: new Date().toISOString()
  }
}

// Player Performance Calculation

export function calculatePlayerPerformances(
  simulation: MatchSimulation
): Record<string, PlayerPerformance> {
  const performances: Record<string, PlayerPerformance> = {}

  // Initialize performances for all players
  const allPlayers = [...(simulation.home_team.players || []), ...(simulation.away_team.players || [])]
  
  allPlayers.forEach(player => {
    performances[player.id] = {
      player_id: player.id,
      goals: 0,
      assists: 0,
      saves: 0,
      exclusions: 0,
      shots_taken: 0,
      shots_on_target: 0,
      passes_completed: 0,
      passes_attempted: 0,
      steals: 0,
      turnovers: 0,
      rating: 6.0 // Base rating
    }
  })

  // Process events to calculate statistics
  simulation.events.forEach(event => {
    if (!event.player_id) return

    const performance = performances[event.player_id]
    if (!performance) return

    switch (event.event_type) {
      case 'goal':
        performance.goals++
        performance.shots_taken++
        performance.shots_on_target++
        performance.rating += 1.0
        break
      case 'save':
        performance.saves++
        performance.rating += 0.3
        break
      case 'exclusion':
        performance.exclusions++
        performance.rating -= 0.5
        break
      case 'penalty':
        performance.rating += 0.2
        break
    }
  })

  // Calculate final ratings
  Object.values(performances).forEach(performance => {
    // Add random variation
    performance.rating += (Math.random() - 0.5) * 2
    
    // Clamp rating between 1 and 10
    performance.rating = Math.max(1, Math.min(10, performance.rating))
    
    // Round to 1 decimal place
    performance.rating = Math.round(performance.rating * 10) / 10
  })

  return performances
}

// Tactical AI

export function generateAITactics(team: Team): TacticalSetup {
  // This is a simplified AI tactical generator
  // In a real implementation, this would analyze the team and opponent
  
  const formations = ['default'] // Could expand with more formations
  const offensiveStyles = ['balanced', 'counter_attack', 'power_play', 'possession'] as const
  const defensiveStyles = ['zone', 'man_to_man', 'press', 'hybrid'] as const
  const goalkeeperStrategies = ['conservative', 'aggressive', 'distribution'] as const

  return {
    formation: { id: 'default', name: 'Standard', positions: {} },
    offensive_style: offensiveStyles[Math.floor(Math.random() * offensiveStyles.length)],
    defensive_style: defensiveStyles[Math.floor(Math.random() * defensiveStyles.length)],
    goalkeeper_strategy: goalkeeperStrategies[Math.floor(Math.random() * goalkeeperStrategies.length)],
    substitutions: []
  }
}
