import { createClient } from '@/lib/supabase-client'

export interface MatchFixture {
  id?: string
  home_team_id: string
  away_team_id: string
  league_id: string
  season_id: string
  matchday: number
  match_date: string
  competition: string
  status: string
}

export interface ScheduledMatch {
  id: string
  home_team: {
    id: string
    name: string
  }
  away_team: {
    id: string
    name: string
  }
  match_date: string
  matchday: number
  status: string
  home_score?: number
  away_score?: number
}

export class MatchSchedulingService {
  private supabase = createClient()

  // Generate complete schedule for a league
  async generateLeagueSchedule(leagueId: string, seasonId: string): Promise<void> {
    try {
      console.log('Generating schedule for league:', leagueId)

      // Get all teams in the league
      const { data: teams } = await this.supabase
        .from('teams')
        .select('id, name')
        .eq('league_id', leagueId)

      if (!teams || teams.length < 2) {
        console.log('Not enough teams to generate schedule')
        return
      }

      // Clear existing fixtures for this league and season
      await this.supabase
        .from('matches')
        .delete()
        .eq('league_id', leagueId)
        .eq('season_id', seasonId)

      // Generate round-robin fixtures
      const fixtures = this.generateRoundRobinFixtures(teams, leagueId, seasonId)

      // Insert fixtures into database
      if (fixtures.length > 0) {
        const { error } = await this.supabase
          .from('matches')
          .insert(fixtures)

        if (error) throw error
        console.log(`Generated ${fixtures.length} fixtures for league ${leagueId}`)
      }
    } catch (error) {
      console.error('Error generating league schedule:', error)
      throw error
    }
  }

  // Generate round-robin fixtures (each team plays each other twice)
  private generateRoundRobinFixtures(
    teams: { id: string; name: string }[],
    leagueId: string,
    seasonId: string
  ): MatchFixture[] {
    const fixtures: MatchFixture[] = []
    const teamCount = teams.length
    
    if (teamCount < 2) return fixtures

    let matchday = 1
    const startDate = new Date()
    
    // First round: each team plays each other once
    for (let i = 0; i < teamCount; i++) {
      for (let j = i + 1; j < teamCount; j++) {
        const matchDate = new Date(startDate)
        matchDate.setDate(startDate.getDate() + (matchday - 1) * 2) // Matches every 2 days
        
        fixtures.push({
          home_team_id: teams[i].id,
          away_team_id: teams[j].id,
          league_id: leagueId,
          season_id: seasonId,
          matchday: matchday,
          match_date: matchDate.toISOString(),
          competition: 'league',
          status: 'scheduled'
        })
        
        matchday++
      }
    }

    // Second round: reverse fixtures (away teams become home teams)
    const firstRoundFixtures = [...fixtures]
    for (const fixture of firstRoundFixtures) {
      const matchDate = new Date(startDate)
      matchDate.setDate(startDate.getDate() + (matchday - 1) * 2)
      
      fixtures.push({
        home_team_id: fixture.away_team_id,
        away_team_id: fixture.home_team_id,
        league_id: leagueId,
        season_id: seasonId,
        matchday: matchday,
        match_date: matchDate.toISOString(),
        competition: 'league',
        status: 'scheduled'
      })
      
      matchday++
    }

    return fixtures
  }

  // Get upcoming matches for a team
  async getTeamUpcomingMatches(teamId: string, limit: number = 5): Promise<ScheduledMatch[]> {
    try {
      const { data: matches } = await this.supabase
        .from('matches')
        .select(`
          id,
          match_date,
          matchday,
          status,
          home_score,
          away_score,
          home_team:teams!matches_home_team_id_fkey(id, name),
          away_team:teams!matches_away_team_id_fkey(id, name)
        `)
        .or(`home_team_id.eq.${teamId},away_team_id.eq.${teamId}`)
        .eq('status', 'scheduled')
        .gte('match_date', new Date().toISOString())
        .order('match_date', { ascending: true })
        .limit(limit)

      return matches?.map(match => ({
        id: match.id,
        home_team: match.home_team,
        away_team: match.away_team,
        match_date: match.match_date,
        matchday: match.matchday,
        status: match.status,
        home_score: match.home_score,
        away_score: match.away_score
      })) || []
    } catch (error) {
      console.error('Error fetching team upcoming matches:', error)
      return []
    }
  }

  // Get recent matches for a team
  async getTeamRecentMatches(teamId: string, limit: number = 5): Promise<ScheduledMatch[]> {
    try {
      const { data: matches } = await this.supabase
        .from('matches')
        .select(`
          id,
          match_date,
          matchday,
          status,
          home_score,
          away_score,
          home_team:teams!matches_home_team_id_fkey(id, name),
          away_team:teams!matches_away_team_id_fkey(id, name)
        `)
        .or(`home_team_id.eq.${teamId},away_team_id.eq.${teamId}`)
        .eq('status', 'completed')
        .lte('match_date', new Date().toISOString())
        .order('match_date', { ascending: false })
        .limit(limit)

      return matches?.map(match => ({
        id: match.id,
        home_team: match.home_team,
        away_team: match.away_team,
        match_date: match.match_date,
        matchday: match.matchday,
        status: match.status,
        home_score: match.home_score,
        away_score: match.away_score
      })) || []
    } catch (error) {
      console.error('Error fetching team recent matches:', error)
      return []
    }
  }

  // Get all matches for a league
  async getLeagueMatches(leagueId: string, seasonId: string): Promise<ScheduledMatch[]> {
    try {
      const { data: matches } = await this.supabase
        .from('matches')
        .select(`
          id,
          match_date,
          matchday,
          status,
          home_score,
          away_score,
          home_team:teams!matches_home_team_id_fkey(id, name),
          away_team:teams!matches_away_team_id_fkey(id, name)
        `)
        .eq('league_id', leagueId)
        .eq('season_id', seasonId)
        .order('matchday', { ascending: true })
        .order('match_date', { ascending: true })

      return matches?.map(match => ({
        id: match.id,
        home_team: match.home_team,
        away_team: match.away_team,
        match_date: match.match_date,
        matchday: match.matchday,
        status: match.status,
        home_score: match.home_score,
        away_score: match.away_score
      })) || []
    } catch (error) {
      console.error('Error fetching league matches:', error)
      return []
    }
  }

  // Simulate a match (basic implementation)
  async simulateMatch(matchId: string): Promise<{ homeScore: number; awayScore: number }> {
    try {
      // Get match details
      const { data: match } = await this.supabase
        .from('matches')
        .select(`
          id,
          home_team_id,
          away_team_id,
          home_team:teams!matches_home_team_id_fkey(team_rating),
          away_team:teams!matches_away_team_id_fkey(team_rating)
        `)
        .eq('id', matchId)
        .single()

      if (!match) throw new Error('Match not found')

      // Simple simulation based on team ratings
      const homeRating = match.home_team.team_rating || 50
      const awayRating = match.away_team.team_rating || 50
      
      // Add some randomness
      const homeBonus = Math.random() * 20 - 10 // -10 to +10
      const awayBonus = Math.random() * 20 - 10
      
      const adjustedHomeRating = Math.max(0, homeRating + homeBonus)
      const adjustedAwayRating = Math.max(0, awayRating + awayBonus)
      
      // Calculate scores (0-10 range, weighted by ratings)
      const homeScore = Math.floor((adjustedHomeRating / 100) * 10 * Math.random())
      const awayScore = Math.floor((adjustedAwayRating / 100) * 10 * Math.random())
      
      // Update match with results
      await this.supabase
        .from('matches')
        .update({
          home_score: homeScore,
          away_score: awayScore,
          status: 'completed'
        })
        .eq('id', matchId)

      // Update team statistics
      const homeResult = homeScore > awayScore ? 'win' : homeScore === awayScore ? 'draw' : 'loss'
      const awayResult = awayScore > homeScore ? 'win' : awayScore === homeScore ? 'draw' : 'loss'

      await this.supabase.rpc('update_team_match_stats', {
        team_id: match.home_team_id,
        goals_scored: homeScore,
        goals_conceded: awayScore,
        result: homeResult
      })

      await this.supabase.rpc('update_team_match_stats', {
        team_id: match.away_team_id,
        goals_scored: awayScore,
        goals_conceded: homeScore,
        result: awayResult
      })

      return { homeScore, awayScore }
    } catch (error) {
      console.error('Error simulating match:', error)
      throw error
    }
  }

  // Auto-generate schedules for leagues that need them
  async autoGenerateSchedules(): Promise<void> {
    try {
      // Get active leagues without scheduled matches
      const { data: leagues } = await this.supabase
        .from('leagues')
        .select(`
          id,
          season_id,
          current_teams,
          matches:matches(count)
        `)
        .eq('status', 'active')
        .gte('current_teams', 2)

      if (!leagues) return

      for (const league of leagues) {
        const matchCount = Array.isArray(league.matches) ? league.matches.length : (league.matches as any)?.count || 0
        
        // Generate schedule if no matches exist and league has enough teams
        if (matchCount === 0 && league.current_teams >= 2) {
          await this.generateLeagueSchedule(league.id, league.season_id)
        }
      }
    } catch (error) {
      console.error('Error auto-generating schedules:', error)
    }
  }
}

export const matchSchedulingService = new MatchSchedulingService()
