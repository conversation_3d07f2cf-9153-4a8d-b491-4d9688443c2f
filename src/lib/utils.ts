import { type ClassValue, clsx } from 'clsx'

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs)
}

export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date))
}

export function formatDateTime(date: string | Date) {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date))
}

export function calculatePlayerOverall(player: {
  shooting: number
  speed: number
  passing: number
  defense: number
}) {
  return Math.round((player.shooting + player.speed + player.passing + player.defense) / 4)
}

export function getPlayerPositionColor(position: string) {
  const colors = {
    goalkeeper: 'bg-yellow-500',
    'left-wing': 'bg-blue-500',
    'right-wing': 'bg-blue-500',
    'left-driver': 'bg-green-500',
    'right-driver': 'bg-green-500',
    'center-forward': 'bg-red-500',
    point: 'bg-purple-500',
  }
  return colors[position as keyof typeof colors] || 'bg-gray-500'
}

export function generateRandomName() {
  const firstNames = [
    'Alex', 'Jordan', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Avery',
    'Blake', '<PERSON>', '<PERSON>', '<PERSON>', 'Fin<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'River', '<PERSON>',
    '<PERSON>ler', 'Tanner', 'Teagan', 'Wren', 'Zion', 'Ari'
  ]
  
  const lastNames = [
    'Anderson', 'Brown', 'Davis', 'Garcia', 'Johnson', 'Jones', 'Martinez',
    'Miller', 'Moore', 'Rodriguez', 'Smith', 'Taylor', 'Thomas', 'Wilson',
    'Clark', 'Lewis', 'Lee', 'Walker', 'Hall', 'Allen', 'Young', 'King',
    'Wright', 'Lopez', 'Hill', 'Scott', 'Green', 'Adams', 'Baker', 'Nelson'
  ]
  
  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)]
  const lastName = lastNames[Math.floor(Math.random() * lastNames.length)]
  
  return `${firstName} ${lastName}`
}

export function generateRandomStats() {
  return {
    shooting: Math.floor(Math.random() * 40) + 30, // 30-70
    speed: Math.floor(Math.random() * 40) + 30, // 30-70
    passing: Math.floor(Math.random() * 40) + 30, // 30-70
    defense: Math.floor(Math.random() * 40) + 30, // 30-70
    age: Math.floor(Math.random() * 15) + 18, // 18-32
    experience: Math.floor(Math.random() * 1000), // 0-999
  }
}
