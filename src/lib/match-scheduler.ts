import { createClient } from '@/lib/supabase-client'
import { Team, Match, League, TacticalSetup } from './types'
import { leagueTierSystem, Season } from './league-tier-system'

export interface ScheduledMatch {
  id: string
  home_team: Team
  away_team: Team
  scheduled_time: string
  competition: string
  league_id?: string
  season_id?: string
  matchday: number
  status: 'scheduled' | 'live' | 'completed' | 'cancelled'
  home_tactics?: TacticalSetup
  away_tactics?: TacticalSetup
}

export interface MatchResult {
  match: Match
  home_team_rating_change: number
  away_team_rating_change: number
  experience_gained: Record<string, number>
  injuries: Array<{
    player_id: string
    injury_type: string
    days_out: number
  }>
}

export interface SeasonSchedule {
  season: Season
  matchdays: MatchDay[]
  total_matches: number
}

export interface MatchDay {
  matchday: number
  date: string
  matches: ScheduledMatch[]
  time_slots: string[] // ['00:00', '06:00', '12:00', '18:00']
}

// Enhanced Match Scheduler for 14-day seasons with 4 daily time slots
export class AdvancedMatchScheduler {
  private supabase = createClient()

  // Match time slots (4 per day)
  private readonly TIME_SLOTS = ['00:00', '06:00', '12:00', '18:00']
  private readonly SEASON_DURATION_DAYS = 14
  private readonly MATCHES_PER_DAY = 4

  // Schedule a friendly match
  async scheduleFriendlyMatch(
    homeTeamId: string,
    awayTeamId: string,
    scheduledTime: string
  ): Promise<{ success: boolean; match?: ScheduledMatch; error?: string }> {
    try {
      // Get team data
      const { data: homeTeam } = await this.supabase
        .from('teams')
        .select(`
          *,
          players:players(*)
        `)
        .eq('id', homeTeamId)
        .single()

      const { data: awayTeam } = await this.supabase
        .from('teams')
        .select(`
          *,
          players:players(*)
        `)
        .eq('id', awayTeamId)
        .single()

      if (!homeTeam || !awayTeam) {
        return { success: false, error: 'Teams not found' }
      }

      // Create match record
      const { data: match, error } = await this.supabase
        .from('matches')
        .insert({
          home_team_id: homeTeamId,
          away_team_id: awayTeamId,
          match_date: scheduledTime,
          competition: 'friendly'
        })
        .select()
        .single()

      if (error) throw error

      const scheduledMatch: ScheduledMatch = {
        id: match.id,
        home_team: homeTeam,
        away_team: awayTeam,
        scheduled_time: scheduledTime,
        competition: 'friendly',
        status: 'scheduled'
      }

      return { success: true, match: scheduledMatch }
    } catch (error) {
      console.error('Error scheduling friendly match:', error)
      return { success: false, error: 'Failed to schedule match' }
    }
  }

  // Generate league fixtures for a season
  async generateLeagueFixtures(leagueId: string): Promise<{
    success: boolean
    fixtures?: ScheduledMatch[]
    error?: string
  }> {
    try {
      // Get league and teams
      const { data: league } = await this.supabase
        .from('leagues')
        .select(`
          *,
          teams:teams(
            *,
            players:players(*)
          )
        `)
        .eq('id', leagueId)
        .single()

      if (!league || !league.teams || league.teams.length < 2) {
        return { success: false, error: 'League not found or insufficient teams' }
      }

      const teams = league.teams
      const fixtures: ScheduledMatch[] = []
      
      // Generate round-robin fixtures (each team plays every other team twice)
      const totalRounds = (teams.length - 1) * 2
      const matchesPerRound = teams.length / 2

      let currentDate = new Date(league.season_start_date || new Date())
      
      for (let round = 0; round < totalRounds; round++) {
        const roundMatches = this.generateRoundFixtures(teams, round)
        
        for (const match of roundMatches) {
          // Schedule matches every 3 days
          const matchTime = new Date(currentDate)
          matchTime.setHours(19, 0, 0, 0) // 7 PM matches
          
          const { data: createdMatch } = await this.supabase
            .from('matches')
            .insert({
              home_team_id: match.home_team.id,
              away_team_id: match.away_team.id,
              match_date: matchTime.toISOString(),
              competition: 'league'
            })
            .select()
            .single()

          if (createdMatch) {
            fixtures.push({
              id: createdMatch.id,
              home_team: match.home_team,
              away_team: match.away_team,
              scheduled_time: matchTime.toISOString(),
              competition: 'league',
              league_id: leagueId,
              status: 'scheduled'
            })
          }
          
          // Next match in 3 days
          currentDate.setDate(currentDate.getDate() + 3)
        }
      }

      return { success: true, fixtures }
    } catch (error) {
      console.error('Error generating league fixtures:', error)
      return { success: false, error: 'Failed to generate fixtures' }
    }
  }

  private generateRoundFixtures(teams: Team[], round: number): Array<{
    home_team: Team
    away_team: Team
  }> {
    const fixtures = []
    const n = teams.length
    
    // Round-robin algorithm
    for (let i = 0; i < n / 2; i++) {
      let home = (round + i) % (n - 1)
      let away = (n - 1 - i + round) % (n - 1)
      
      // Last team stays fixed
      if (i === 0) {
        away = n - 1
      }
      
      // Alternate home/away for return fixtures
      if (round >= n - 1) {
        [home, away] = [away, home]
      }
      
      fixtures.push({
        home_team: teams[home],
        away_team: teams[away]
      })
    }
    
    return fixtures
  }

  // Get upcoming matches for a team
  async getUpcomingMatches(teamId: string, limit: number = 5): Promise<ScheduledMatch[]> {
    const { data: matches } = await this.supabase
      .from('matches')
      .select(`
        *,
        home_team:teams!matches_home_team_id_fkey(
          *,
          players:players(*)
        ),
        away_team:teams!matches_away_team_id_fkey(
          *,
          players:players(*)
        )
      `)
      .or(`home_team_id.eq.${teamId},away_team_id.eq.${teamId}`)
      .gte('match_date', new Date().toISOString())
      .order('match_date', { ascending: true })
      .limit(limit)

    return matches?.map(match => ({
      id: match.id,
      home_team: match.home_team,
      away_team: match.away_team,
      scheduled_time: match.match_date,
      competition: match.competition,
      status: 'scheduled' as const
    })) || []
  }

  // Get recent matches for a team
  async getRecentMatches(teamId: string, limit: number = 5): Promise<Match[]> {
    const { data: matches } = await this.supabase
      .from('matches')
      .select(`
        *,
        home_team:teams!matches_home_team_id_fkey(*),
        away_team:teams!matches_away_team_id_fkey(*)
      `)
      .or(`home_team_id.eq.${teamId},away_team_id.eq.${teamId}`)
      .not('home_score', 'is', null)
      .not('away_score', 'is', null)
      .order('match_date', { ascending: false })
      .limit(limit)

    return matches || []
  }

  // Process match result and update team stats
  async processMatchResult(matchResult: MatchResult): Promise<void> {
    const { match } = matchResult

    // Update team statistics
    await this.updateTeamStats(match.home_team_id, match.away_team_id, match)

    // Update player experience
    for (const [playerId, experience] of Object.entries(matchResult.experience_gained)) {
      await this.supabase
        .from('players')
        .update({
          experience: this.supabase.rpc('increment_experience', { 
            player_id: playerId, 
            amount: experience 
          })
        })
        .eq('id', playerId)
    }

    // Process injuries
    for (const injury of matchResult.injuries) {
      await this.supabase
        .from('players')
        .update({
          injury_status: injury.injury_type,
          injury_days: injury.days_out
        })
        .eq('id', injury.player_id)
    }

    // Update team ratings
    await this.supabase
      .from('teams')
      .update({
        team_rating: this.supabase.rpc('update_team_rating', {
          team_id: match.home_team_id,
          change: matchResult.home_team_rating_change
        })
      })
      .eq('id', match.home_team_id)

    await this.supabase
      .from('teams')
      .update({
        team_rating: this.supabase.rpc('update_team_rating', {
          team_id: match.away_team_id,
          change: matchResult.away_team_rating_change
        })
      })
      .eq('id', match.away_team_id)
  }

  private async updateTeamStats(homeTeamId: string, awayTeamId: string, match: Match): Promise<void> {
    const homeWin = match.home_score > match.away_score
    const awayWin = match.away_score > match.home_score
    const draw = match.home_score === match.away_score

    // Update home team stats
    await this.supabase
      .from('teams')
      .update({
        matches_played: this.supabase.rpc('increment', { column_name: 'matches_played' }),
        matches_won: homeWin ? this.supabase.rpc('increment', { column_name: 'matches_won' }) : undefined,
        matches_drawn: draw ? this.supabase.rpc('increment', { column_name: 'matches_drawn' }) : undefined,
        matches_lost: awayWin ? this.supabase.rpc('increment', { column_name: 'matches_lost' }) : undefined,
        goals_for: this.supabase.rpc('increment_by', { 
          column_name: 'goals_for', 
          amount: match.home_score 
        }),
        goals_against: this.supabase.rpc('increment_by', { 
          column_name: 'goals_against', 
          amount: match.away_score 
        }),
        league_points: this.supabase.rpc('increment_by', {
          column_name: 'league_points',
          amount: homeWin ? 3 : draw ? 1 : 0
        })
      })
      .eq('id', homeTeamId)

    // Update away team stats
    await this.supabase
      .from('teams')
      .update({
        matches_played: this.supabase.rpc('increment', { column_name: 'matches_played' }),
        matches_won: awayWin ? this.supabase.rpc('increment', { column_name: 'matches_won' }) : undefined,
        matches_drawn: draw ? this.supabase.rpc('increment', { column_name: 'matches_drawn' }) : undefined,
        matches_lost: homeWin ? this.supabase.rpc('increment', { column_name: 'matches_lost' }) : undefined,
        goals_for: this.supabase.rpc('increment_by', { 
          column_name: 'goals_for', 
          amount: match.away_score 
        }),
        goals_against: this.supabase.rpc('increment_by', { 
          column_name: 'goals_against', 
          amount: match.home_score 
        }),
        league_points: this.supabase.rpc('increment_by', {
          column_name: 'league_points',
          amount: awayWin ? 3 : draw ? 1 : 0
        })
      })
      .eq('id', awayTeamId)
  }

  // Auto-simulate matches that are due
  async autoSimulateScheduledMatches(): Promise<void> {
    const now = new Date()
    
    const { data: dueMatches } = await this.supabase
      .from('matches')
      .select(`
        *,
        home_team:teams!matches_home_team_id_fkey(
          *,
          players:players(*)
        ),
        away_team:teams!matches_away_team_id_fkey(
          *,
          players:players(*)
        )
      `)
      .lte('match_date', now.toISOString())
      .is('home_score', null)
      .is('away_score', null)

    if (!dueMatches) return

    for (const match of dueMatches) {
      // Auto-simulate with basic tactics
      const defaultTactics: TacticalSetup = {
        formation: 'standard-7v7',
        style: 'balanced',
        pressing: 'medium',
        tempo: 'medium'
      }

      // This would integrate with the real-time match engine
      // For now, we'll use the basic simulation
      // const result = await simulateMatch(match.home_team, match.away_team, defaultTactics, defaultTactics)
      
      // Update match with simulated result
      const homeScore = Math.floor(Math.random() * 15) + 5
      const awayScore = Math.floor(Math.random() * 15) + 5
      
      await this.supabase
        .from('matches')
        .update({
          home_score: homeScore,
          away_score: awayScore
        })
        .eq('id', match.id)

      // Process the result
      const matchResult: MatchResult = {
        match: { ...match, home_score: homeScore, away_score: awayScore },
        home_team_rating_change: homeScore > awayScore ? 2 : homeScore === awayScore ? 0 : -1,
        away_team_rating_change: awayScore > homeScore ? 2 : awayScore === homeScore ? 0 : -1,
        experience_gained: {},
        injuries: []
      }

      await this.processMatchResult(matchResult)
    }
  }
  // Advanced match scheduling for 14-day seasons with 4 daily time slots
  async generateAdvancedLeagueSchedule(leagueId: string, seasonId: string): Promise<SeasonSchedule> {
    // Fetch league and season information
    const { data: league } = await this.supabase
      .from('leagues')
      .select('*, season:seasons(*)')
      .eq('id', leagueId)
      .single()

    if (!league) throw new Error('League not found')

    const season = league.season as Season

    // Fetch teams in the league
    const { data: teams } = await this.supabase
      .from('teams')
      .select('*')
      .eq('league_id', leagueId)

    if (!teams || teams.length < 2) {
      throw new Error('Not enough teams in the league')
    }

    // Create matchdays structure
    const matchdays: MatchDay[] = []
    let totalMatches = 0

    // Calculate season start and end dates
    const seasonStart = new Date(season.start_date)

    // Generate all fixtures using round-robin
    const allFixtures: { home_team: Team; away_team: Team }[] = []
    const totalRounds = (teams.length - 1) * 2 // Home and away

    for (let round = 0; round < totalRounds; round++) {
      const roundFixtures = this.generateRoundFixtures(teams, round)
      allFixtures.push(...roundFixtures)
    }

    // Calculate matches per matchday
    const totalMatchdays = Math.min(26, allFixtures.length) // Cap at 26 matchdays
    const matchesPerMatchday = Math.ceil(allFixtures.length / totalMatchdays)

    // Create matchday structure with 4 time slots per day
    for (let matchday = 1; matchday <= totalMatchdays; matchday++) {
      // Calculate date for this matchday (spread evenly across 14 days)
      const dayOffset = Math.floor((matchday - 1) * this.SEASON_DURATION_DAYS / totalMatchdays)
      const matchdayDate = new Date(seasonStart)
      matchdayDate.setDate(seasonStart.getDate() + dayOffset)

      const matchdayMatches: ScheduledMatch[] = []
      const startIdx = (matchday - 1) * matchesPerMatchday
      const endIdx = Math.min(startIdx + matchesPerMatchday, allFixtures.length)

      // Get matches for this matchday
      const matchdayFixtures = allFixtures.slice(startIdx, endIdx)

      // Distribute matches across time slots
      for (let i = 0; i < matchdayFixtures.length; i++) {
        const fixture = matchdayFixtures[i]
        const timeSlotIndex = i % this.TIME_SLOTS.length

        // Calculate date with proper time slot
        const matchDate = new Date(matchdayDate)
        const [hours, minutes] = this.TIME_SLOTS[timeSlotIndex].split(':').map(Number)
        matchDate.setHours(hours, minutes, 0, 0)

        // Create match in database
        const { data: createdMatch } = await this.supabase
          .from('matches')
          .insert({
            home_team_id: fixture.home_team.id,
            away_team_id: fixture.away_team.id,
            league_id: leagueId,
            season_id: seasonId,
            matchday: matchday,
            match_date: matchDate.toISOString(),
            competition: 'league',
            status: 'scheduled'
          })
          .select()
          .single()

        if (createdMatch) {
          matchdayMatches.push({
            id: createdMatch.id,
            home_team: fixture.home_team,
            away_team: fixture.away_team,
            scheduled_time: matchDate.toISOString(),
            competition: 'league',
            league_id: leagueId,
            season_id: seasonId,
            matchday: matchday,
            status: 'scheduled'
          })

          totalMatches++
        }
      }

      // Add matchday to schedule
      matchdays.push({
        matchday,
        date: matchdayDate.toISOString(),
        matches: matchdayMatches,
        time_slots: this.TIME_SLOTS
      })
    }

    // Update league with current matchday and status
    await this.supabase
      .from('leagues')
      .update({
        current_matchday: 1,
        status: 'active',
        current_teams: teams.length
      })
      .eq('id', leagueId)

    return {
      season,
      matchdays,
      total_matches: totalMatches
    }
  }
}

// Keep the original MatchScheduler class for backward compatibility
export class MatchScheduler extends AdvancedMatchScheduler {}

export const matchScheduler = new MatchScheduler()
export const advancedMatchScheduler = new AdvancedMatchScheduler()
