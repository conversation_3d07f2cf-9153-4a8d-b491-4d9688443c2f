import { createClient } from '@/lib/supabase-client'
import { Team, Player, Match } from './types'

export interface TeamAnalytics {
  team_id: string
  team_name: string
  matches_played: number
  wins: number
  draws: number
  losses: number
  goals_scored: number
  goals_conceded: number
  goal_difference: number
  points: number
  form: string[] // Last 5 results: 'W', 'D', 'L'
  win_percentage: number
  clean_sheets: number
  scoring_rate: number // Goals per match
  conceding_rate: number // Goals conceded per match
  performance_trend: 'improving' | 'declining' | 'stable'
  power_ranking: number // 1-100 rating
  power_ranking_change: number // Change since last week
}

export interface PlayerAnalytics {
  player_id: string
  player_name: string
  position: string
  matches_played: number
  minutes_played: number
  goals: number
  assists: number
  saves?: number // For goalkeepers
  exclusions: number
  form_rating: number // 1-10 rating
  form_trend: 'improving' | 'declining' | 'stable'
  performance_index: number // Overall performance metric
  fatigue_level: number // 0-100
  morale: number // 0-100
}

export interface MatchAnalysis {
  match_id: string
  home_team: Team
  away_team: Team
  home_score: number
  away_score: number
  match_date: string
  key_moments: {
    minute: number
    description: string
    impact: 'positive' | 'negative' | 'neutral'
    player_id?: string
  }[]
  possession: {
    home: number
    away: number
  }
  shots: {
    home: number
    away: number
  }
  shots_on_target: {
    home: number
    away: number
  }
  exclusions: {
    home: number
    away: number
  }
  tactical_analysis: string
  player_performances: PlayerAnalytics[]
}

export interface WeeklyWrapUp {
  week_number: number
  season_id: string
  start_date: string
  end_date: string
  matches_played: number
  top_scorers: {
    player_id: string
    player_name: string
    team_name: string
    goals: number
  }[]
  best_goalkeepers: {
    player_id: string
    player_name: string
    team_name: string
    saves: number
  }[]
  team_of_the_week: {
    player_id: string
    player_name: string
    team_name: string
    position: string
    rating: number
  }[]
  biggest_upset: {
    match_id: string
    underdog: string
    favorite: string
    score: string
  } | null
  power_rankings: {
    team_id: string
    team_name: string
    ranking: number
    previous_ranking: number
    change: number
  }[]
  featured_match: {
    match_id: string
    home_team: string
    away_team: string
    score: string
    description: string
  }
}

export class LeagueAnalytics {
  private supabase = createClient()

  // Get team analytics
  async getTeamAnalytics(teamId: string): Promise<TeamAnalytics | null> {
    try {
      // Get team info
      const { data: team } = await this.supabase
        .from('teams')
        .select('*')
        .eq('id', teamId)
        .single()

      if (!team) return null

      // Get match results
      const { data: matches } = await this.supabase
        .from('matches')
        .select('*')
        .or(`home_team_id.eq.${teamId},away_team_id.eq.${teamId}`)
        .not('home_score', 'is', null)
        .not('away_score', 'is', null)
        .order('match_date', { ascending: false })

      if (!matches || matches.length === 0) {
        return {
          team_id: team.id,
          team_name: team.name,
          matches_played: 0,
          wins: 0,
          draws: 0,
          losses: 0,
          goals_scored: 0,
          goals_conceded: 0,
          goal_difference: 0,
          points: 0,
          form: [],
          win_percentage: 0,
          clean_sheets: 0,
          scoring_rate: 0,
          conceding_rate: 0,
          performance_trend: 'stable',
          power_ranking: 50,
          power_ranking_change: 0
        }
      }

      // Calculate stats
      let wins = 0
      let draws = 0
      let losses = 0
      let goalsScored = 0
      let goalsConceded = 0
      let cleanSheets = 0
      const form: string[] = []

      matches.forEach(match => {
        const isHome = match.home_team_id === teamId
        const teamScore = isHome ? match.home_score : match.away_score
        const opponentScore = isHome ? match.away_score : match.home_score

        goalsScored += teamScore
        goalsConceded += opponentScore

        if (teamScore > opponentScore) {
          wins++
          form.push('W')
        } else if (teamScore === opponentScore) {
          draws++
          form.push('D')
        } else {
          losses++
          form.push('L')
        }

        if (opponentScore === 0) {
          cleanSheets++
        }
      })

      const matchesPlayed = wins + draws + losses
      const points = wins * 3 + draws
      const goalDifference = goalsScored - goalsConceded
      const winPercentage = matchesPlayed > 0 ? (wins / matchesPlayed) * 100 : 0
      const scoringRate = matchesPlayed > 0 ? goalsScored / matchesPlayed : 0
      const concedingRate = matchesPlayed > 0 ? goalsConceded / matchesPlayed : 0

      // Calculate performance trend based on recent form
      const recentForm = form.slice(0, 5)
      const recentWins = recentForm.filter(r => r === 'W').length
      const recentLosses = recentForm.filter(r => r === 'L').length
      
      let performanceTrend: 'improving' | 'declining' | 'stable' = 'stable'
      if (recentWins > recentLosses) {
        performanceTrend = 'improving'
      } else if (recentLosses > recentWins) {
        performanceTrend = 'declining'
      }

      // Calculate power ranking (simplified algorithm)
      const winRatio = matchesPlayed > 0 ? wins / matchesPlayed : 0
      const goalRatio = goalsConceded > 0 ? goalsScored / goalsConceded : goalsScored > 0 ? 2 : 1
      const powerRanking = Math.min(100, Math.max(1, Math.round((winRatio * 50) + (goalRatio * 25) + (cleanSheets * 2))))
      
      // Mock power ranking change
      const powerRankingChange = performanceTrend === 'improving' ? 2 : performanceTrend === 'declining' ? -2 : 0

      return {
        team_id: team.id,
        team_name: team.name,
        matches_played: matchesPlayed,
        wins,
        draws,
        losses,
        goals_scored: goalsScored,
        goals_conceded: goalsConceded,
        goal_difference: goalDifference,
        points,
        form: form.slice(0, 5), // Last 5 matches
        win_percentage: winPercentage,
        clean_sheets: cleanSheets,
        scoring_rate: scoringRate,
        conceding_rate: concedingRate,
        performance_trend: performanceTrend,
        power_ranking: powerRanking,
        power_ranking_change: powerRankingChange
      }
    } catch (error) {
      console.error('Error getting team analytics:', error)
      return null
    }
  }

  // Get player analytics
  async getPlayerAnalytics(playerId: string): Promise<PlayerAnalytics | null> {
    try {
      // Get player info
      const { data: player } = await this.supabase
        .from('players')
        .select('*')
        .eq('id', playerId)
        .single()

      if (!player) return null

      // Get match events
      const { data: events } = await this.supabase
        .from('match_events')
        .select(`
          *,
          match:matches(*)
        `)
        .eq('player_id', playerId)
        .order('created_at', { ascending: false })

      // Calculate stats
      const matchesPlayed = new Set(events?.map(e => e.match_id) || []).size
      const goals = events?.filter(e => e.event_type === 'goal').length || 0
      const assists = events?.filter(e => e.event_type === 'assist').length || 0
      const saves = events?.filter(e => e.event_type === 'save').length || 0
      const exclusions = events?.filter(e => e.event_type === 'exclusion').length || 0
      
      // Calculate form rating (1-10)
      const recentEvents = events?.slice(0, 10) || []
      const positiveEvents = recentEvents.filter(e => ['goal', 'save', 'assist'].includes(e.event_type)).length
      const negativeEvents = recentEvents.filter(e => ['exclusion', 'yellow_card', 'red_card'].includes(e.event_type)).length
      
      const formRating = Math.min(10, Math.max(1, 5 + (positiveEvents * 0.5) - (negativeEvents * 0.7)))
      
      // Calculate form trend
      const olderEvents = events?.slice(10, 20) || []
      const olderPositive = olderEvents.filter(e => ['goal', 'save', 'assist'].includes(e.event_type)).length
      const olderNegative = olderEvents.filter(e => ['exclusion', 'yellow_card', 'red_card'].includes(e.event_type)).length
      
      const olderRating = Math.min(10, Math.max(1, 5 + (olderPositive * 0.5) - (olderNegative * 0.7)))
      
      let formTrend: 'improving' | 'declining' | 'stable' = 'stable'
      if (formRating > olderRating + 0.5) {
        formTrend = 'improving'
      } else if (formRating < olderRating - 0.5) {
        formTrend = 'declining'
      }
      
      // Calculate performance index
      const performanceIndex = Math.round(
        (goals * 3) + 
        (assists * 2) + 
        (saves * 0.5) - 
        (exclusions * 2) + 
        (formRating * 5)
      )

      return {
        player_id: player.id,
        player_name: player.name,
        position: player.position,
        matches_played: matchesPlayed,
        minutes_played: matchesPlayed * 32, // Approximate
        goals,
        assists,
        saves,
        exclusions,
        form_rating: formRating,
        form_trend: formTrend,
        performance_index: performanceIndex,
        fatigue_level: player.fatigue || 0,
        morale: player.morale || 50
      }
    } catch (error) {
      console.error('Error getting player analytics:', error)
      return null
    }
  }

  // Generate weekly wrap-up
  async generateWeeklyWrapUp(leagueId: string, weekNumber: number): Promise<WeeklyWrapUp> {
    try {
      // Get league info
      const { data: league } = await this.supabase
        .from('leagues')
        .select('*, season:seasons(*)')
        .eq('id', leagueId)
        .single()

      if (!league) throw new Error('League not found')

      // Calculate date range for the week
      const startDate = new Date(league.season.start_date)
      startDate.setDate(startDate.getDate() + (weekNumber - 1) * 7)
      
      const endDate = new Date(startDate)
      endDate.setDate(endDate.getDate() + 6)

      // Get matches played in this week
      const { data: matches } = await this.supabase
        .from('matches')
        .select(`
          *,
          home_team:teams!matches_home_team_id_fkey(id, name),
          away_team:teams!matches_away_team_id_fkey(id, name)
        `)
        .eq('league_id', leagueId)
        .gte('match_date', startDate.toISOString())
        .lte('match_date', endDate.toISOString())
        .not('home_score', 'is', null)
        .not('away_score', 'is', null)

      if (!matches || matches.length === 0) {
        throw new Error('No matches found for this week')
      }

      // Mock top scorers and goalkeepers for now
      const topScorers = [
        { player_id: 'p1', player_name: 'Luca Rossi', team_name: 'Team A', goals: 5 },
        { player_id: 'p2', player_name: 'Mateo Delgado', team_name: 'Team B', goals: 4 },
        { player_id: 'p3', player_name: 'Kenji Sato', team_name: 'Team C', goals: 3 }
      ]

      const bestGks = [
        { player_id: 'g1', player_name: 'David Kovač', team_name: 'Team D', saves: 15 },
        { player_id: 'g2', player_name: 'Leo Dupont', team_name: 'Team E', saves: 12 },
        { player_id: 'g3', player_name: 'Jalen Price', team_name: 'Team F', saves: 10 }
      ]

      // Mock data for team of the week and power rankings
      const teamOfTheWeek = [
        { player_id: 'p1', player_name: 'Luca Rossi', team_name: 'Team A', position: 'GK', rating: 9.2 },
        { player_id: 'p2', player_name: 'Mateo Delgado', team_name: 'Team B', position: 'LW', rating: 8.7 },
        { player_id: 'p3', player_name: 'Kenji Sato', team_name: 'Team C', position: 'D', rating: 8.5 },
        { player_id: 'p4', player_name: 'Pablo Hernández', team_name: 'Team D', position: 'CF', rating: 9.0 },
        { player_id: 'p5', player_name: 'David Kovač', team_name: 'Team E', position: 'P', rating: 8.8 },
        { player_id: 'p6', player_name: 'Leo Dupont', team_name: 'Team F', position: 'D', rating: 8.3 },
        { player_id: 'p7', player_name: 'Jalen Price', team_name: 'Team G', position: 'RW', rating: 8.9 }
      ]

      const powerRankings = [
        { team_id: 't1', team_name: 'Team A', ranking: 1, previous_ranking: 2, change: 1 },
        { team_id: 't2', team_name: 'Team B', ranking: 2, previous_ranking: 1, change: -1 },
        { team_id: 't3', team_name: 'Team C', ranking: 3, previous_ranking: 3, change: 0 },
        { team_id: 't4', team_name: 'Team D', ranking: 4, previous_ranking: 6, change: 2 },
        { team_id: 't5', team_name: 'Team E', ranking: 5, previous_ranking: 4, change: -1 }
      ]

      // Find biggest upset (simplified)
      let biggestUpset = null
      if (matches.length > 0) {
        const upsetMatch = matches[0]
        biggestUpset = {
          match_id: upsetMatch.id,
          underdog: upsetMatch.home_team.name,
          favorite: upsetMatch.away_team.name,
          score: `${upsetMatch.home_score}-${upsetMatch.away_score}`
        }
      }

      // Featured match
      const featuredMatch = matches.length > 0 ? {
        match_id: matches[0].id,
        home_team: matches[0].home_team.name,
        away_team: matches[0].away_team.name,
        score: `${matches[0].home_score}-${matches[0].away_score}`,
        description: 'An exciting match with multiple lead changes and dramatic finish.'
      } : null

      return {
        week_number: weekNumber,
        season_id: league.season.id,
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        matches_played: matches.length,
        top_scorers: topScorers?.map(ts => ({
          player_id: ts.player_id,
          player_name: ts.players.name,
          team_name: ts.teams.name,
          goals: ts.count
        })) || [],
        best_goalkeepers: bestGks?.map(gk => ({
          player_id: gk.player_id,
          player_name: gk.players.name,
          team_name: gk.teams.name,
          saves: gk.count
        })) || [],
        team_of_the_week: teamOfTheWeek,
        biggest_upset: biggestUpset,
        power_rankings: powerRankings,
        featured_match: featuredMatch
      }
    } catch (error) {
      console.error('Error generating weekly wrap-up:', error)
      throw error
    }
  }
}

export const leagueAnalytics = new LeagueAnalytics()
