import { createClient } from '@/lib/supabase-client'

export interface LeagueTier {
  id: string
  name: 'Bronze' | 'Silver' | 'Gold' | 'Elite' | 'Legends'
  level: number // 1-5
  division: number // 1-5 (I-V)
  prize_money_base: number
  promotion_bonus: number
  relegation_penalty: number
  tactics_bonus_enabled: boolean
  scouting_quality_multiplier: number
  fanbase_growth_multiplier: number
  created_at: string
  updated_at: string
}

export interface Season {
  id: string
  name: string
  start_date: string
  end_date: string
  current_matchday: number
  total_matchdays: number
  status: 'preparing' | 'active' | 'finished'
  created_at: string
  updated_at: string
}

export interface League {
  id: string
  tier_id: string
  season_id: string
  name: string
  max_teams: number
  current_teams: number
  status: 'preparing' | 'active' | 'finished'
  tier?: LeagueTier
  season?: Season
  created_at: string
  updated_at: string
}

export interface PromotionRelegationResult {
  team_id: string
  team_name: string
  current_position: number
  action: 'promoted' | 'relegated' | 'stays'
  from_league?: string
  to_league?: string
  reward_amount?: number
}

export class LeagueTierSystem {
  private supabase = createClient()

  // Get all league tiers
  async getAllTiers(): Promise<LeagueTier[]> {
    const { data, error } = await this.supabase
      .from('league_tiers')
      .select('*')
      .order('level', { ascending: true })
      .order('division', { ascending: true })

    if (error) throw error
    return data || []
  }

  // Get tier by name and division
  async getTier(name: string, division: number): Promise<LeagueTier | null> {
    const { data, error } = await this.supabase
      .from('league_tiers')
      .select('*')
      .eq('name', name)
      .eq('division', division)
      .single()

    if (error) return null
    return data
  }

  // Get current active season
  async getCurrentSeason(): Promise<Season | null> {
    const { data, error } = await this.supabase
      .from('seasons')
      .select('*')
      .eq('status', 'active')
      .order('start_date', { ascending: false })
      .limit(1)
      .single()

    if (error) return null
    return data
  }

  // Get leagues for a specific tier and season
  async getLeaguesForTier(tierId: string, seasonId: string): Promise<League[]> {
    const { data, error } = await this.supabase
      .from('leagues')
      .select(`
        *,
        tier:league_tiers(*),
        season:seasons(*)
      `)
      .eq('tier_id', tierId)
      .eq('season_id', seasonId)

    if (error) throw error
    return data || []
  }

  // Get team's current league
  async getTeamCurrentLeague(teamId: string): Promise<League | null> {
    const { data, error } = await this.supabase
      .from('teams')
      .select(`
        league_id,
        leagues!inner(
          *,
          tier:league_tiers(*),
          season:seasons(*)
        )
      `)
      .eq('id', teamId)
      .single()

    if (error || !data?.leagues) return null
    return data.leagues as League
  }

  // Calculate tier progression rewards
  calculateTierRewards(tier: LeagueTier, position: number, totalTeams: number): {
    prize_money: number
    promotion_bonus: number
    relegation_penalty: number
  } {
    let prize_money = tier.prize_money_base
    let promotion_bonus = 0
    let relegation_penalty = 0

    // Position-based prize money (higher positions get more)
    const positionMultiplier = (totalTeams - position + 1) / totalTeams
    prize_money = Math.floor(prize_money * positionMultiplier)

    // Promotion bonus for top 3
    if (position <= 3 && tier.level < 5) {
      promotion_bonus = tier.promotion_bonus
    }

    // Relegation penalty for bottom 3
    if (position >= totalTeams - 2 && tier.level > 1) {
      relegation_penalty = tier.relegation_penalty
    }

    return { prize_money, promotion_bonus, relegation_penalty }
  }

  // Process promotion and relegation at season end
  async processPromotionRelegation(leagueId: string): Promise<PromotionRelegationResult[]> {
    const results: PromotionRelegationResult[] = []

    // Get league standings
    const { data: standings } = await this.supabase
      .from('league_standings')
      .select(`
        *,
        team:teams(id, name, user_id),
        league:leagues(
          *,
          tier:league_tiers(*)
        )
      `)
      .eq('league_id', leagueId)
      .order('position', { ascending: true })

    if (!standings || standings.length === 0) return results

    const league = standings[0].league
    const tier = league.tier

    for (const standing of standings) {
      const team = standing.team
      const position = standing.position
      const totalTeams = standings.length

      let action: 'promoted' | 'relegated' | 'stays' = 'stays'
      let toLeague: string | undefined
      let rewardAmount = 0

      // Calculate rewards
      const rewards = this.calculateTierRewards(tier, position, totalTeams)
      rewardAmount = rewards.prize_money

      // Promotion (top 3 positions)
      if (position <= 3 && tier.level < 5) {
        action = 'promoted'
        rewardAmount += rewards.promotion_bonus

        // Find higher tier league with available space
        const higherTier = await this.getTier(tier.name, Math.max(1, tier.division - 1))
        if (!higherTier && tier.level < 5) {
          // Move to next tier level, division 5
          const nextTierName = this.getNextTierName(tier.name)
          if (nextTierName) {
            const nextTier = await this.getTier(nextTierName, 5)
            if (nextTier) {
              toLeague = await this.findAvailableLeague(nextTier.id, league.season_id)
            }
          }
        } else if (higherTier) {
          toLeague = await this.findAvailableLeague(higherTier.id, league.season_id)
        }
      }
      // Relegation (bottom 3 positions)
      else if (position >= totalTeams - 2 && tier.level > 1) {
        action = 'relegated'
        rewardAmount -= rewards.relegation_penalty

        // Find lower tier league
        const lowerTier = await this.getTier(tier.name, Math.min(5, tier.division + 1))
        if (!lowerTier && tier.level > 1) {
          // Move to previous tier level, division 1
          const prevTierName = this.getPreviousTierName(tier.name)
          if (prevTierName) {
            const prevTier = await this.getTier(prevTierName, 1)
            if (prevTier) {
              toLeague = await this.findAvailableLeague(prevTier.id, league.season_id)
            }
          }
        } else if (lowerTier) {
          toLeague = await this.findAvailableLeague(lowerTier.id, league.season_id)
        }
      }

      // Award currency to team owner
      if (team.user_id && rewardAmount > 0) {
        await this.awardSeasonRewards(team.user_id, rewardAmount, action, position)
      }

      results.push({
        team_id: team.id,
        team_name: team.name,
        current_position: position,
        action,
        from_league: league.name,
        to_league: toLeague,
        reward_amount: rewardAmount
      })
    }

    return results
  }

  // Helper methods
  private getNextTierName(currentTier: string): string | null {
    const tiers = ['Bronze', 'Silver', 'Gold', 'Elite', 'Legends']
    const currentIndex = tiers.indexOf(currentTier)
    return currentIndex < tiers.length - 1 ? tiers[currentIndex + 1] : null
  }

  private getPreviousTierName(currentTier: string): string | null {
    const tiers = ['Bronze', 'Silver', 'Gold', 'Elite', 'Legends']
    const currentIndex = tiers.indexOf(currentTier)
    return currentIndex > 0 ? tiers[currentIndex - 1] : null
  }

  private async findAvailableLeague(tierId: string, seasonId: string): Promise<string | undefined> {
    const { data } = await this.supabase
      .from('leagues')
      .select('id, current_teams, max_teams')
      .eq('tier_id', tierId)
      .eq('season_id', seasonId)
      .lt('current_teams', 14) // Less than max teams
      .order('current_teams', { ascending: true })
      .limit(1)
      .single()

    return data?.id
  }

  private async awardSeasonRewards(userId: string, amount: number, action: string, position: number) {
    // Award currency through economy system
    await this.supabase
      .from('economy_transactions')
      .insert({
        user_id: userId,
        type: 'earn',
        source: 'season_reward',
        coins_change: amount,
        description: `Season reward: ${action} (Position ${position})`,
        metadata: { action, position }
      })
  }

  // Get tier display information
  getTierDisplayInfo(tier: LeagueTier): {
    displayName: string
    color: string
    icon: string
    description: string
  } {
    const romanNumerals = ['I', 'II', 'III', 'IV', 'V']
    const displayName = `${tier.name} ${romanNumerals[tier.division - 1]}`

    const tierInfo = {
      Bronze: {
        color: 'bg-amber-600',
        icon: '🥉',
        description: 'Starter leagues for new managers'
      },
      Silver: {
        color: 'bg-gray-400',
        icon: '🥈',
        description: 'Intermediate leagues with tactical bonuses'
      },
      Gold: {
        color: 'bg-yellow-500',
        icon: '🥇',
        description: 'High-skill leagues with better rewards'
      },
      Elite: {
        color: 'bg-purple-600',
        icon: '💎',
        description: 'Top-tier leagues for elite managers'
      },
      Legends: {
        color: 'bg-red-600',
        icon: '👑',
        description: 'Legendary leagues for the top 1%'
      }
    }

    return {
      displayName,
      ...tierInfo[tier.name]
    }
  }
}

export const leagueTierSystem = new LeagueTierSystem()
