import { Profile, Team, Player, Facility, FACILITY_TYPES } from './types'
import { calculatePlayerOverall } from './game-engine'

// Manager Progression

export function calculateManagerLevel(experiencePoints: number): number {
  // Level progression: 100 XP for level 1, then +50 XP per level
  let level = 1
  let requiredXP = 100
  let totalXP = 0
  
  while (totalXP + requiredXP <= experiencePoints) {
    totalXP += requiredXP
    level++
    requiredXP += 50
  }
  
  return level
}

export function getExperienceForNextLevel(currentXP: number): { current: number; required: number; progress: number } {
  const currentLevel = calculateManagerLevel(currentXP)
  const nextLevelXP = getRequiredXPForLevel(currentLevel + 1)
  const currentLevelXP = getRequiredXPForLevel(currentLevel)
  
  return {
    current: currentXP - currentLevelXP,
    required: nextLevelXP - currentLevelXP,
    progress: (currentXP - currentLevelXP) / (nextLevelXP - currentLevelXP)
  }
}

function getRequiredXPForLevel(level: number): number {
  if (level <= 1) return 0
  
  let totalXP = 0
  for (let i = 1; i < level; i++) {
    totalXP += 100 + (i - 1) * 50
  }
  
  return totalXP
}

export function awardManagerExperience(
  profile: Profile,
  source: 'match_win' | 'match_draw' | 'training' | 'transfer' | 'facility_upgrade' | 'achievement',
  amount?: number
): { newProfile: Profile; levelUp: boolean; newLevel?: number } {
  const baseXP = {
    match_win: 25,
    match_draw: 10,
    training: 5,
    transfer: 15,
    facility_upgrade: 30,
    achievement: 50
  }
  
  const xpGained = amount || baseXP[source]
  const oldLevel = calculateManagerLevel(profile.experience_points)
  const newXP = profile.experience_points + xpGained
  const newLevel = calculateManagerLevel(newXP)
  
  const newProfile = {
    ...profile,
    experience_points: newXP,
    manager_level: newLevel
  }
  
  return {
    newProfile,
    levelUp: newLevel > oldLevel,
    newLevel: newLevel > oldLevel ? newLevel : undefined
  }
}

// Team Rating System

export function calculateTeamRating(team: Team): number {
  if (!team.players || team.players.length === 0) return 0
  
  // Get starting 7 players (best overall ratings)
  const sortedPlayers = team.players
    .map(player => ({ player, rating: calculatePlayerOverall(player) }))
    .sort((a, b) => b.rating - a.rating)
    .slice(0, 7)
  
  if (sortedPlayers.length === 0) return 0
  
  // Calculate weighted average (goalkeeper weighted more heavily)
  let totalWeight = 0
  let weightedSum = 0
  
  sortedPlayers.forEach(({ player, rating }) => {
    const weight = player.position === 'goalkeeper' ? 1.5 : 1.0
    weightedSum += rating * weight
    totalWeight += weight
  })
  
  const baseRating = weightedSum / totalWeight
  
  // Apply team chemistry bonus (simplified)
  const chemistryBonus = calculateTeamChemistry(team)
  
  // Apply facility bonuses
  const facilityBonus = calculateFacilityBonus(team)
  
  return Math.round(baseRating * (1 + chemistryBonus / 100) * (1 + facilityBonus / 100))
}

function calculateTeamChemistry(team: Team): number {
  if (!team.players) return 0
  
  // Chemistry based on average morale and time together
  const averageMorale = team.players.reduce((sum, player) => sum + player.morale, 0) / team.players.length
  
  // Simplified chemistry calculation
  return (averageMorale - 75) / 2 // -12.5 to +12.5
}

function calculateFacilityBonus(team: Team): number {
  if (!team.facilities) return 0
  
  // Training pool improves team performance
  const trainingPool = team.facilities.find(f => f.facility_type === 'training_pool')
  const trainingBonus = trainingPool ? trainingPool.level * 2 : 0
  
  // Arena improves home advantage (simplified)
  const arena = team.facilities.find(f => f.facility_type === 'aquatic_arena')
  const arenaBonus = arena ? arena.level * 1 : 0
  
  return trainingBonus + arenaBonus
}

// Facility Management

export function calculateFacilityUpgradeCost(facility: Facility): number {
  const facilityType = FACILITY_TYPES[facility.facility_type]
  const baseCost = facilityType.base_cost
  
  // Cost increases exponentially with level
  return Math.round(baseCost * Math.pow(1.5, facility.level - 1))
}

export function calculateFacilityMaintenanceCost(facility: Facility): number {
  const facilityType = FACILITY_TYPES[facility.facility_type]
  const baseMaintenance = facilityType.base_maintenance
  
  // Maintenance increases with level
  return Math.round(baseMaintenance * facility.level)
}

export function upgradeFacility(facility: Facility): Facility {
  const facilityType = FACILITY_TYPES[facility.facility_type]
  
  if (facility.level >= facilityType.max_level) {
    throw new Error('Facility is already at maximum level')
  }
  
  const newLevel = facility.level + 1
  const newUpgradeCost = calculateFacilityUpgradeCost({ ...facility, level: newLevel })
  const newMaintenanceCost = calculateFacilityMaintenanceCost({ ...facility, level: newLevel })
  
  return {
    ...facility,
    level: newLevel,
    upgrade_cost: newUpgradeCost,
    maintenance_cost: newMaintenanceCost,
    bonus_percentage: facility.bonus_percentage + 10, // +10% per level
    updated_at: new Date().toISOString()
  }
}

export function createDefaultFacilities(teamId: string): Facility[] {
  return Object.keys(FACILITY_TYPES).map(facilityType => {
    const type = facilityType as keyof typeof FACILITY_TYPES
    const facilityInfo = FACILITY_TYPES[type]
    
    return {
      id: crypto.randomUUID(),
      team_id: teamId,
      facility_type: type,
      level: 1,
      upgrade_cost: facilityInfo.base_cost,
      maintenance_cost: facilityInfo.base_maintenance,
      bonus_percentage: 10, // Base 10% bonus
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  })
}

// Achievement System

export interface Achievement {
  id: string
  name: string
  description: string
  category: 'matches' | 'training' | 'transfers' | 'facilities' | 'league' | 'special'
  requirement: {
    type: string
    value: number
    current?: number
  }
  reward: {
    experience: number
    cash?: number
    unlock?: string
  }
  unlocked: boolean
  completed: boolean
}

export const ACHIEVEMENTS: Achievement[] = [
  {
    id: 'first_win',
    name: 'First Victory',
    description: 'Win your first match',
    category: 'matches',
    requirement: { type: 'matches_won', value: 1 },
    reward: { experience: 50, cash: 1000 },
    unlocked: true,
    completed: false
  },
  {
    id: 'winning_streak',
    name: 'Winning Streak',
    description: 'Win 5 matches in a row',
    category: 'matches',
    requirement: { type: 'consecutive_wins', value: 5 },
    reward: { experience: 100, cash: 5000 },
    unlocked: true,
    completed: false
  },
  {
    id: 'training_master',
    name: 'Training Master',
    description: 'Complete 50 training sessions',
    category: 'training',
    requirement: { type: 'training_sessions', value: 50 },
    reward: { experience: 75, unlock: 'advanced_training' },
    unlocked: true,
    completed: false
  },
  {
    id: 'transfer_guru',
    name: 'Transfer Guru',
    description: 'Complete 10 successful transfers',
    category: 'transfers',
    requirement: { type: 'successful_transfers', value: 10 },
    reward: { experience: 100, cash: 10000 },
    unlocked: true,
    completed: false
  },
  {
    id: 'facility_builder',
    name: 'Facility Builder',
    description: 'Upgrade all facilities to level 5',
    category: 'facilities',
    requirement: { type: 'min_facility_level', value: 5 },
    reward: { experience: 200, cash: 25000 },
    unlocked: false,
    completed: false
  },
  {
    id: 'league_champion',
    name: 'League Champion',
    description: 'Win your first league title',
    category: 'league',
    requirement: { type: 'league_titles', value: 1 },
    reward: { experience: 300, cash: 50000 },
    unlocked: false,
    completed: false
  },
  {
    id: 'youth_developer',
    name: 'Youth Developer',
    description: 'Develop 5 youth players to 80+ rating',
    category: 'special',
    requirement: { type: 'youth_developed', value: 5 },
    reward: { experience: 150, unlock: 'elite_youth_academy' },
    unlocked: false,
    completed: false
  }
]

export function checkAchievements(
  team: Team,
  profile: Profile,
  gameStats: Record<string, number>
): Achievement[] {
  const completedAchievements: Achievement[] = []
  
  ACHIEVEMENTS.forEach(achievement => {
    if (achievement.completed || !achievement.unlocked) return
    
    const currentValue = gameStats[achievement.requirement.type] || 0
    
    if (currentValue >= achievement.requirement.value) {
      achievement.completed = true
      completedAchievements.push(achievement)
    }
  })
  
  return completedAchievements
}

// Financial System

export function calculateDailyRevenue(team: Team): number {
  let revenue = 0
  
  // Base revenue from fan base
  revenue += team.fan_base * 0.5
  
  // Arena revenue bonus
  const arena = team.facilities?.find(f => f.facility_type === 'aquatic_arena')
  if (arena) {
    revenue += arena.level * 100
  }
  
  // League position bonus (higher position = more revenue)
  const positionBonus = Math.max(0, (15 - team.league_position) * 50)
  revenue += positionBonus
  
  return Math.round(revenue)
}

export function calculateDailyCosts(team: Team): number {
  let costs = 0
  
  // Player salaries (daily portion)
  if (team.players) {
    const totalSalaries = team.players.reduce((sum, player) => sum + player.contract_salary, 0)
    costs += totalSalaries / 30 // Monthly salary divided by 30 days
  }
  
  // Facility maintenance
  if (team.facilities) {
    const maintenanceCosts = team.facilities.reduce((sum, facility) => sum + facility.maintenance_cost, 0)
    costs += maintenanceCosts
  }
  
  return Math.round(costs)
}

export function processDailyFinancialUpdate(team: Team): { revenue: number; costs: number; netIncome: number; newBalance: number } {
  const revenue = calculateDailyRevenue(team)
  const costs = calculateDailyCosts(team)
  const netIncome = revenue - costs
  const newBalance = team.cash_balance + netIncome
  
  return {
    revenue,
    costs,
    netIncome,
    newBalance: Math.max(0, newBalance) // Can't go below 0
  }
}

// Unlock System

export interface Unlock {
  id: string
  name: string
  description: string
  type: 'feature' | 'formation' | 'training' | 'facility'
  requirement: {
    manager_level?: number
    achievement?: string
    facility_level?: { type: string; level: number }
  }
}

export const UNLOCKS: Unlock[] = [
  {
    id: 'advanced_training',
    name: 'Advanced Training',
    description: 'Unlock high-intensity training sessions',
    type: 'training',
    requirement: { manager_level: 5 }
  },
  {
    id: 'custom_formations',
    name: 'Custom Formations',
    description: 'Create your own tactical formations',
    type: 'formation',
    requirement: { manager_level: 10 }
  },
  {
    id: 'elite_youth_academy',
    name: 'Elite Youth Academy',
    description: 'Generate higher quality youth players',
    type: 'facility',
    requirement: { achievement: 'youth_developer' }
  },
  {
    id: 'international_scouting',
    name: 'International Scouting',
    description: 'Scout players from other leagues',
    type: 'feature',
    requirement: { manager_level: 15 }
  }
]

export function checkUnlocks(profile: Profile, achievements: Achievement[]): Unlock[] {
  const availableUnlocks: Unlock[] = []
  
  UNLOCKS.forEach(unlock => {
    let canUnlock = true
    
    // Check manager level requirement
    if (unlock.requirement.manager_level && profile.manager_level < unlock.requirement.manager_level) {
      canUnlock = false
    }
    
    // Check achievement requirement
    if (unlock.requirement.achievement) {
      const achievement = achievements.find(a => a.id === unlock.requirement.achievement)
      if (!achievement || !achievement.completed) {
        canUnlock = false
      }
    }
    
    if (canUnlock) {
      availableUnlocks.push(unlock)
    }
  })
  
  return availableUnlocks
}
