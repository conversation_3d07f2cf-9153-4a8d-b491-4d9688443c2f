import { createClient } from '@/lib/supabase-client'
import { UserCurrency } from './types'

/**
 * Unified Currency Service
 * Manages all currency operations using user_currencies table
 * Replaces the split system of team.cash_balance and user_currencies
 */
export class UnifiedCurrencyService {
  private supabase = createClient()
  private currencyUpdateCallbacks: Set<(currency: UserCurrency) => void> = new Set()

  /**
   * Subscribe to currency updates
   */
  onCurrencyUpdate(callback: (currency: UserCurrency) => void) {
    this.currencyUpdateCallbacks.add(callback)
    return () => this.currencyUpdateCallbacks.delete(callback)
  }

  /**
   * Broadcast currency update to all subscribers
   */
  private broadcastCurrencyUpdate(currency: UserCurrency) {
    this.currencyUpdateCallbacks.forEach(callback => {
      try {
        callback(currency)
      } catch (error) {
        console.error('Error in currency update callback:', error)
      }
    })
  }

  /**
   * Get user's currency by user ID
   */
  async getUserCurrency(userId: string): Promise<UserCurrency | null> {
    const { data, error } = await this.supabase
      .from('user_currencies')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching user currency:', error)
      return null
    }

    return data
  }

  /**
   * Get user's currency by team ID (for backward compatibility)
   */
  async getUserCurrencyByTeamId(teamId: string): Promise<UserCurrency | null> {
    const { data, error } = await this.supabase
      .from('teams')
      .select(`
        user_id,
        user_currencies!inner(*)
      `)
      .eq('id', teamId)
      .single()

    if (error) {
      console.error('Error fetching user currency by team ID:', error)
      return null
    }

    return data.user_currencies as UserCurrency
  }

  /**
   * Initialize currency for a new user
   */
  async initializeUserCurrency(userId: string): Promise<UserCurrency | null> {
    const { data, error } = await this.supabase
      .from('user_currencies')
      .insert({
        user_id: userId,
        coins: 50000, // Starting amount
        tokens: 0,
        boosters: { xp_boost: 0, recovery_boost: 0, injury_heal: 0 }
      })
      .select()
      .single()

    if (error) {
      console.error('Error initializing user currency:', error)
      return null
    }

    return data
  }

  /**
   * Check if user has sufficient coins
   */
  async hasSufficientCoins(userId: string, amount: number): Promise<boolean> {
    const currency = await this.getUserCurrency(userId)
    return currency ? currency.coins >= amount : false
  }

  /**
   * Spend coins (for training, transfers, facilities, etc.)
   */
  async spendCoins(
    userId: string, 
    amount: number, 
    description: string,
    source: string = 'general'
  ): Promise<{ success: boolean; newBalance?: number; error?: string }> {
    try {
      const currency = await this.getUserCurrency(userId)
      
      if (!currency) {
        return { success: false, error: 'User currency not found' }
      }

      if (currency.coins < amount) {
        return { success: false, error: 'Insufficient coins' }
      }

      const newBalance = currency.coins - amount

      // Update currency
      const { error: updateError } = await this.supabase
        .from('user_currencies')
        .update({ 
          coins: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (updateError) {
        console.error('Error updating currency:', updateError)
        return { success: false, error: 'Failed to update currency' }
      }

      // Record transaction
      await this.recordTransaction(userId, 'spend', source, -amount, 0, description)

      // Get updated currency and broadcast
      const updatedCurrency = await this.getUserCurrency(userId)
      if (updatedCurrency) {
        this.broadcastCurrencyUpdate(updatedCurrency)
      }

      return { success: true, newBalance }
    } catch (error) {
      console.error('Error spending coins:', error)
      return { success: false, error: 'Transaction failed' }
    }
  }

  /**
   * Award coins (for match wins, daily rewards, etc.)
   */
  async awardCoins(
    userId: string, 
    amount: number, 
    description: string,
    source: string = 'general'
  ): Promise<{ success: boolean; newBalance?: number; error?: string }> {
    try {
      const currency = await this.getUserCurrency(userId)
      
      if (!currency) {
        // Initialize currency if it doesn't exist
        const newCurrency = await this.initializeUserCurrency(userId)
        if (!newCurrency) {
          return { success: false, error: 'Failed to initialize currency' }
        }
      }

      const currentCurrency = currency || await this.getUserCurrency(userId)
      if (!currentCurrency) {
        return { success: false, error: 'Currency not found' }
      }

      const newBalance = currentCurrency.coins + amount

      // Update currency
      const { error: updateError } = await this.supabase
        .from('user_currencies')
        .update({ 
          coins: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (updateError) {
        console.error('Error updating currency:', updateError)
        return { success: false, error: 'Failed to update currency' }
      }

      // Record transaction
      await this.recordTransaction(userId, 'earn', source, amount, 0, description)

      // Get updated currency and broadcast
      const updatedCurrency = await this.getUserCurrency(userId)
      if (updatedCurrency) {
        this.broadcastCurrencyUpdate(updatedCurrency)
      }

      return { success: true, newBalance }
    } catch (error) {
      console.error('Error awarding coins:', error)
      return { success: false, error: 'Transaction failed' }
    }
  }

  /**
   * Award tokens (for premium rewards)
   */
  async awardTokens(
    userId: string, 
    amount: number, 
    description: string,
    source: string = 'general'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const currency = await this.getUserCurrency(userId)
      
      if (!currency) {
        const newCurrency = await this.initializeUserCurrency(userId)
        if (!newCurrency) {
          return { success: false, error: 'Failed to initialize currency' }
        }
      }

      const currentCurrency = currency || await this.getUserCurrency(userId)
      if (!currentCurrency) {
        return { success: false, error: 'Currency not found' }
      }

      const newBalance = currentCurrency.tokens + amount

      // Update currency
      const { error: updateError } = await this.supabase
        .from('user_currencies')
        .update({ 
          tokens: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (updateError) {
        console.error('Error updating tokens:', updateError)
        return { success: false, error: 'Failed to update tokens' }
      }

      // Record transaction
      await this.recordTransaction(userId, 'earn', source, 0, amount, description)

      return { success: true }
    } catch (error) {
      console.error('Error awarding tokens:', error)
      return { success: false, error: 'Transaction failed' }
    }
  }

  /**
   * Record a transaction in the economy_transactions table
   */
  private async recordTransaction(
    userId: string,
    type: 'earn' | 'spend',
    source: string,
    coinsChange: number,
    tokensChange: number,
    description: string,
    metadata?: any
  ): Promise<void> {
    try {
      await this.supabase
        .from('economy_transactions')
        .insert({
          user_id: userId,
          type,
          source,
          coins_change: coinsChange,
          tokens_change: tokensChange,
          description,
          metadata: metadata || {}
        })
    } catch (error) {
      console.error('Error recording transaction:', error)
    }
  }

  /**
   * Get user's transaction history
   */
  async getTransactionHistory(userId: string, limit: number = 20) {
    const { data, error } = await this.supabase
      .from('economy_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching transaction history:', error)
      return []
    }

    return data || []
  }

  /**
   * Format currency amount for display
   */
  formatCoins(amount: number): string {
    return `$${amount.toLocaleString()}`
  }

  /**
   * Format budget for header bar display
   */
  formatBudgetHeader(teamName: string, amount: number): string {
    return `${teamName} • $${amount.toLocaleString()} available`
  }

  /**
   * Format budget for stat cards
   */
  formatBudgetStat(amount: number): string {
    return `$${amount.toLocaleString()}`
  }

  /**
   * Format budget for side nav (abbreviated for collapsed view)
   */
  formatBudgetSideNav(amount: number, abbreviated: boolean = false): string {
    if (abbreviated) {
      return `${Math.round(amount / 1000)}k`
    }
    return `$${amount.toLocaleString()}`
  }

  /**
   * Format tokens for display
   */
  formatTokens(amount: number): string {
    return `${amount} tokens`
  }
}

export const unifiedCurrencyService = new UnifiedCurrencyService()
