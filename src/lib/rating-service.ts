import { createClient } from '@/lib/supabase-client'
import { Team, Player } from './types'
import { calculatePlayerOverall } from './game-engine'
import { calculateTeamRating } from './progression-system'

export interface RatingData {
  teamRating: number
  avgRating: number
  playerCount: number
}

/**
 * Rating Service
 * Manages real-time rating calculations and synchronization
 * Provides team rating, average player rating, and broadcasts updates
 */
export class RatingService {
  private supabase = createClient()
  private ratingUpdateCallbacks: Set<(ratings: RatingData) => void> = new Set()

  /**
   * Subscribe to rating updates
   */
  onRatingUpdate(callback: (ratings: RatingData) => void) {
    this.ratingUpdateCallbacks.add(callback)
    return () => this.ratingUpdateCallbacks.delete(callback)
  }

  /**
   * Broadcast rating update to all subscribers
   */
  private broadcastRatingUpdate(ratings: RatingData) {
    this.ratingUpdateCallbacks.forEach(callback => {
      try {
        callback(ratings)
      } catch (error) {
        console.error('Error in rating update callback:', error)
      }
    })
  }

  /**
   * Get team with players for rating calculations
   */
  async getTeamWithPlayers(userId: string): Promise<Team | null> {
    try {
      // Fetch teams for the user
      const { data: teamsData, error: teamError } = await this.supabase
        .from('teams')
        .select('*')
        .eq('user_id', userId)

      if (teamError || !teamsData || teamsData.length === 0) {
        return null
      }

      const teamData = teamsData[0] // Take the first team

      // Fetch players separately
      const { data: playersData, error: playersError } = await this.supabase
        .from('players')
        .select('*')
        .eq('team_id', teamData.id)

      if (playersError) {
        // Return team without players rather than null
        return { ...teamData, players: [] } as Team
      }

      return {
        ...teamData,
        players: playersData || []
      } as Team

    } catch (error) {
      console.error('Error in getTeamWithPlayers:', error)
      return null
    }
  }

  /**
   * Calculate current ratings for a user's team
   */
  async calculateCurrentRatings(userId: string): Promise<RatingData> {
    try {
      const team = await this.getTeamWithPlayers(userId)

      if (!team || !team.players || team.players.length === 0) {
        return {
          teamRating: 0,
          avgRating: 0,
          playerCount: 0
        }
      }

      // Calculate team rating using existing system
      const teamRating = calculateTeamRating(team)

      // Calculate average player rating
      const playerRatings = team.players.map(player => calculatePlayerOverall(player))
      const avgRating = playerRatings.length > 0
        ? Math.round(playerRatings.reduce((sum, rating) => sum + rating, 0) / playerRatings.length)
        : 0

      return {
        teamRating,
        avgRating,
        playerCount: team.players.length
      }

    } catch (error) {
      console.error('Error calculating ratings:', error)
      return {
        teamRating: 0,
        avgRating: 0,
        playerCount: 0
      }
    }
  }

  /**
   * Calculate ratings from team data (without database fetch)
   */
  calculateRatingsFromTeam(team: Team | null): RatingData {
    if (!team) {
      return {
        teamRating: 0,
        avgRating: 0,
        playerCount: 0
      }
    }

    if (!team.players || team.players.length === 0) {
      return {
        teamRating: 0,
        avgRating: 0,
        playerCount: 0
      }
    }

    // Calculate team rating using existing system
    const teamRating = calculateTeamRating(team)

    // Calculate average player rating
    const playerRatings = team.players.map(player => calculatePlayerOverall(player))
    const avgRating = playerRatings.length > 0
      ? Math.round(playerRatings.reduce((sum, rating) => sum + rating, 0) / playerRatings.length)
      : 0

    return {
      teamRating,
      avgRating,
      playerCount: team.players.length
    }
  }

  /**
   * Update team rating in database and broadcast changes
   */
  async updateTeamRating(userId: string): Promise<RatingData> {
    const ratings = await this.calculateCurrentRatings(userId)
    
    // Update team rating in database
    const { error } = await this.supabase
      .from('teams')
      .update({ 
        team_rating: ratings.teamRating,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)

    if (error) {
      console.error('Error updating team rating:', error)
    }

    // Broadcast the update
    this.broadcastRatingUpdate(ratings)
    
    return ratings
  }

  /**
   * Trigger rating update after player changes (training, transfers, etc.)
   */
  async onPlayerChange(userId: string): Promise<void> {
    await this.updateTeamRating(userId)
  }

  /**
   * Format team rating for display
   */
  formatTeamRating(rating: number): string {
    return rating.toString()
  }

  /**
   * Format average rating for display
   */
  formatAvgRating(rating: number): string {
    return rating.toString()
  }

  /**
   * Get rating color based on value
   */
  getRatingColor(rating: number): string {
    if (rating >= 85) return 'text-green-600'
    if (rating >= 75) return 'text-blue-600'
    if (rating >= 65) return 'text-yellow-600'
    if (rating >= 50) return 'text-orange-600'
    return 'text-red-600'
  }

  /**
   * Get rating badge color based on value
   */
  getRatingBadgeColor(rating: number): string {
    if (rating >= 85) return 'bg-green-100 text-green-800'
    if (rating >= 75) return 'bg-blue-100 text-blue-800'
    if (rating >= 65) return 'bg-yellow-100 text-yellow-800'
    if (rating >= 50) return 'bg-orange-100 text-orange-800'
    return 'bg-red-100 text-red-800'
  }

  /**
   * Get rating description based on value
   */
  getRatingDescription(rating: number): string {
    if (rating >= 90) return 'World Class'
    if (rating >= 85) return 'Elite'
    if (rating >= 80) return 'Excellent'
    if (rating >= 75) return 'Very Good'
    if (rating >= 70) return 'Good'
    if (rating >= 65) return 'Above Average'
    if (rating >= 60) return 'Average'
    if (rating >= 50) return 'Below Average'
    if (rating >= 40) return 'Poor'
    return 'Very Poor'
  }

  /**
   * Format rating for header display
   */
  formatRatingHeader(teamName: string, rating: number): string {
    return `${teamName} • ${rating} OVR`
  }

  /**
   * Format rating for stat cards
   */
  formatRatingStat(rating: number): string {
    return rating.toString()
  }

  /**
   * Format rating for side nav
   */
  formatRatingSideNav(rating: number, abbreviated: boolean = false): string {
    if (abbreviated) {
      return rating.toString()
    }
    return `${rating} OVR`
  }
}

export const ratingService = new RatingService()
