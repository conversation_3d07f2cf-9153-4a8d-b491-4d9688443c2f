'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { createClient } from './supabase-client'
import { Profile } from './types'
import { defaultTeamService } from './default-team-service'

interface AuthContextType {
  user: User | null
  session: Session | null
  profile: Profile | null
  loading: boolean
  signInWithEmail: (email: string, password: string) => Promise<void>
  signUpWithEmail: (email: string, password: string, fullName: string) => Promise<void>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  
  const supabase = createClient()

  const fetchProfile = async (userId: string) => {
    try {
      // First, get the basic profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (profileError) {
        // If profile doesn't exist, try to create it
        if (profileError.code === 'PGRST116') {
          const user = await supabase.auth.getUser()
          if (user.data.user) {
            const { data: newProfile, error: createError } = await supabase
              .from('profiles')
              .insert({
                id: userId,
                email: user.data.user.email || '',
                full_name: user.data.user.user_metadata?.full_name || null
              })
              .select()
              .single()

            if (createError) {
              console.error('Error creating profile:', createError)
              return null
            }

            return newProfile
          }
        }
        console.error('Error fetching profile:', profileError)
        return null
      }

      // Get the user's teams and currency separately
      const [teamsResult, currencyResult] = await Promise.all([
        supabase
          .from('teams')
          .select(`
            *,
            players:players(*),
            facilities:facilities(*)
          `)
          .eq('user_id', userId),
        supabase
          .from('user_currencies')
          .select('*')
          .eq('user_id', userId)
          .single()
      ])

      const { data: teams, error: teamsError } = teamsResult
      const { data: userCurrency } = currencyResult

      if (teamsError) {
        console.error('Error fetching teams:', teamsError)
        console.error('Teams error details:', {
          message: teamsError.message,
          details: teamsError.details,
          hint: teamsError.hint,
          code: teamsError.code
        })
        // Return profile without teams if teams query fails
        return profile
      }

      // If no team exists, create a default team for the user
      let team = teams && teams.length > 0 ? teams[0] : null

      if (!team) {
        console.log('No team found for user, creating default team...')
        const teamResult = await defaultTeamService.createDefaultTeam(userId)

        if (teamResult.success && teamResult.team) {
          // Fetch the complete team data with players and facilities
          const { data: newTeamData } = await supabase
            .from('teams')
            .select(`
              *,
              players:players(*),
              facilities:facilities(*)
            `)
            .eq('id', teamResult.team.id)
            .single()

          team = newTeamData
        }
      }

      return {
        ...profile,
        team,
        userCurrency
      }


    } catch (error) {
      console.error('Error fetching profile:', error)
      return null
    }
  }

  const refreshProfile = async () => {
    if (user) {
      const profileData = await fetchProfile(user.id)
      setProfile(profileData)
    }
  }

  const signInWithEmail = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      if (error) throw error
    } catch (error) {
      console.error('Error signing in:', error)
      throw error
    }
  }

  const signUpWithEmail = async (email: string, password: string, fullName: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName
          }
        }
      })

      if (error) {
        throw error
      }
    } catch (error) {
      console.error('Error signing up:', error)
      throw error
    }
  }

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      setUser(null)
      setSession(null)
      setProfile(null)
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      if (session?.user) {
        fetchProfile(session.user.id).then(setProfile)
      }
      setLoading(false)
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session)
      setUser(session?.user ?? null)

      if (session?.user) {
        const profileData = await fetchProfile(session.user.id)
        setProfile(profileData)
      } else {
        setProfile(null)
      }

      setLoading(false)
    })

    return () => subscription.unsubscribe()
  }, [])

  const value = {
    user,
    session,
    profile,
    loading,
    signInWithEmail,
    signUpWithEmail,
    signOut,
    refreshProfile,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
