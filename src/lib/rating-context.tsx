'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './auth-context'
import { ratingService, RatingData } from './rating-service'

interface RatingContextType {
  ratings: RatingData | null
  isLoading: boolean
  refreshRatings: () => Promise<void>
  formatTeamRating: () => string
  formatAvgRating: () => string
  formatRatingHeader: (teamName: string) => string
  formatRatingStat: (type: 'team' | 'avg') => string
  formatRatingSideNav: (type: 'team' | 'avg', abbreviated?: boolean) => string
  getRatingColor: (type: 'team' | 'avg') => string
  getRatingDescription: (type: 'team' | 'avg') => string
}

const RatingContext = createContext<RatingContextType | undefined>(undefined)

export function RatingProvider({ children }: { children: React.ReactNode }) {
  const { user, profile } = useAuth()
  const [ratings, setRatings] = useState<RatingData | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Load initial ratings
  useEffect(() => {
    if (user?.id && profile?.team && profile?.team?.players) {
      loadRatings()
    } else if (user?.id && !profile?.team) {
      setRatings(null)
      setIsLoading(true)
    } else {
      setRatings(null)
      setIsLoading(false)
    }
  }, [user?.id, profile?.team?.id, profile?.team?.players?.length])

  // Subscribe to rating updates
  useEffect(() => {
    if (!user?.id) return

    const unsubscribe = ratingService.onRatingUpdate((updatedRatings) => {
      setRatings(updatedRatings)
    })

    return unsubscribe
  }, [user?.id])

  const loadRatings = async () => {
    if (!user?.id) return

    if (!profile?.team) {
      setRatings({
        teamRating: 0,
        avgRating: 0,
        playerCount: 0
      })
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      // Use team data from auth context instead of fetching separately
      const currentRatings = ratingService.calculateRatingsFromTeam(profile.team)
      setRatings(currentRatings)
    } catch (error) {
      console.error('Error loading ratings:', error)
      setRatings({
        teamRating: 0,
        avgRating: 0,
        playerCount: 0
      })
    } finally {
      setIsLoading(false)
    }
  }

  const refreshRatings = async () => {
    if (!user?.id || !profile?.team) return

    try {
      // Just update the database - the subscription will handle the state update
      await ratingService.updateTeamRating(user.id)
    } catch (error) {
      console.error('Error refreshing ratings:', error)
    }
  }

  // Formatting functions
  const formatTeamRating = (): string => {
    return ratingService.formatTeamRating(ratings?.teamRating || 0)
  }

  const formatAvgRating = (): string => {
    return ratingService.formatAvgRating(ratings?.avgRating || 0)
  }

  const formatRatingHeader = (teamName: string): string => {
    return ratingService.formatRatingHeader(teamName, ratings?.teamRating || 0)
  }

  const formatRatingStat = (type: 'team' | 'avg'): string => {
    const rating = type === 'team' ? ratings?.teamRating || 0 : ratings?.avgRating || 0
    return ratingService.formatRatingStat(rating)
  }

  const formatRatingSideNav = (type: 'team' | 'avg', abbreviated: boolean = false): string => {
    const rating = type === 'team' ? ratings?.teamRating || 0 : ratings?.avgRating || 0
    return ratingService.formatRatingSideNav(rating, abbreviated)
  }

  const getRatingColor = (type: 'team' | 'avg'): string => {
    const rating = type === 'team' ? ratings?.teamRating || 0 : ratings?.avgRating || 0
    return ratingService.getRatingColor(rating)
  }

  const getRatingDescription = (type: 'team' | 'avg'): string => {
    const rating = type === 'team' ? ratings?.teamRating || 0 : ratings?.avgRating || 0
    return ratingService.getRatingDescription(rating)
  }

  const value: RatingContextType = {
    ratings,
    isLoading,
    refreshRatings,
    formatTeamRating,
    formatAvgRating,
    formatRatingHeader,
    formatRatingStat,
    formatRatingSideNav,
    getRatingColor,
    getRatingDescription
  }

  return (
    <RatingContext.Provider value={value}>
      {children}
    </RatingContext.Provider>
  )
}

export function useRating() {
  const context = useContext(RatingContext)
  if (context === undefined) {
    throw new Error('useRating must be used within a RatingProvider')
  }
  return context
}
