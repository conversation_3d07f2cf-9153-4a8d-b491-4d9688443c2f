-- Water Polo Manager Database Migration
-- This script creates the missing tables needed for the training system

-- Add missing columns to teams table
ALTER TABLE teams 
ADD COLUMN IF NOT EXISTS cash_balance INTEGER DEFAULT 50000,
ADD COLUMN IF NOT EXISTS league_position INTEGER,
ADD COLUMN IF NOT EXISTS league_points INTEGER DEFAULT 0;

-- Create training_sessions table
CREATE TABLE IF NOT EXISTS training_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
    training_type TEXT NOT NULL,
    intensity TEXT NOT NULL CHECK (intensity IN ('light', 'medium', 'high', 'extreme')),
    duration_minutes INTEGER NOT NULL,
    cost INTEGER NOT NULL,
    experience_gained INTEGER NOT NULL,
    fatigue_added INTEGER NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create player_training table
CREATE TABLE IF NOT EXISTS player_training (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    player_id UUID NOT NULL REFERENCES players(id) ON DELETE CASCADE,
    training_session_id UUID NOT NULL REFERENCES training_sessions(id) ON DELETE CASCADE,
    stat_improvements JSONB NOT NULL DEFAULT '{}',
    experience_gained INTEGER NOT NULL,
    fatigue_added INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create facilities table
CREATE TABLE IF NOT EXISTS facilities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
    facility_type TEXT NOT NULL,
    level INTEGER NOT NULL DEFAULT 1,
    bonus_percentage INTEGER NOT NULL DEFAULT 0,
    maintenance_cost INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(team_id, facility_type)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_training_sessions_team_id ON training_sessions(team_id);
CREATE INDEX IF NOT EXISTS idx_training_sessions_created_at ON training_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_player_training_player_id ON player_training(player_id);
CREATE INDEX IF NOT EXISTS idx_player_training_session_id ON player_training(training_session_id);
CREATE INDEX IF NOT EXISTS idx_facilities_team_id ON facilities(team_id);
CREATE INDEX IF NOT EXISTS idx_facilities_type ON facilities(facility_type);

-- Enable Row Level Security (RLS)
ALTER TABLE training_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE player_training ENABLE ROW LEVEL SECURITY;
ALTER TABLE facilities ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for training_sessions
CREATE POLICY "Users can view their own team's training sessions" ON training_sessions
    FOR SELECT USING (
        team_id IN (
            SELECT id FROM teams WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert training sessions for their own teams" ON training_sessions
    FOR INSERT WITH CHECK (
        team_id IN (
            SELECT id FROM teams WHERE user_id = auth.uid()
        )
    );

-- Create RLS policies for player_training
CREATE POLICY "Users can view their own players' training" ON player_training
    FOR SELECT USING (
        player_id IN (
            SELECT p.id FROM players p
            JOIN teams t ON p.team_id = t.id
            WHERE t.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert training for their own players" ON player_training
    FOR INSERT WITH CHECK (
        player_id IN (
            SELECT p.id FROM players p
            JOIN teams t ON p.team_id = t.id
            WHERE t.user_id = auth.uid()
        )
    );

-- Create RLS policies for facilities
CREATE POLICY "Users can view their own team's facilities" ON facilities
    FOR SELECT USING (
        team_id IN (
            SELECT id FROM teams WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage their own team's facilities" ON facilities
    FOR ALL USING (
        team_id IN (
            SELECT id FROM teams WHERE user_id = auth.uid()
        )
    );

-- Insert default training facility for existing teams
INSERT INTO facilities (team_id, facility_type, level, bonus_percentage, maintenance_cost)
SELECT 
    id as team_id,
    'training_pool' as facility_type,
    1 as level,
    5 as bonus_percentage,
    1000 as maintenance_cost
FROM teams
WHERE id NOT IN (
    SELECT team_id FROM facilities WHERE facility_type = 'training_pool'
);

-- Update teams that don't have cash_balance set
UPDATE teams 
SET cash_balance = 50000 
WHERE cash_balance IS NULL;

-- Update teams that don't have league_points set
UPDATE teams 
SET league_points = 0 
WHERE league_points IS NULL;
