-- Step 2: Enable Row Level Security and create policies
-- Run this after Step 1 is completed successfully

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.formations ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Create policies for teams
CREATE POLICY "Users can view own teams" ON public.teams
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own teams" ON public.teams
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own teams" ON public.teams
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own teams" ON public.teams
    FOR DELETE USING (auth.uid() = user_id);

-- Create policies for players
CREATE POLICY "Users can view own players" ON public.players
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own players" ON public.players
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own players" ON public.players
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete own players" ON public.players
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

-- Create policies for matches
CREATE POLICY "Users can view matches involving their teams" ON public.matches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE (teams.id = matches.home_team_id OR teams.id = matches.away_team_id)
            AND teams.user_id = auth.uid()
        )
    );

-- Create policies for formations
CREATE POLICY "Users can view own formations" ON public.formations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = formations.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own formations" ON public.formations
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = formations.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own formations" ON public.formations
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = formations.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete own formations" ON public.formations
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = formations.team_id 
            AND teams.user_id = auth.uid()
        )
    );
