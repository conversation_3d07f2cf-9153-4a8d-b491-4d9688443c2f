-- Water Polo Manager Currency Unification Migration
-- This script unifies team cash_balance and user_currencies into a single user-based system

-- Step 1: Migrate existing team cash_balance to user_currencies
-- For users who have teams but no user_currencies record
INSERT INTO user_currencies (user_id, coins, tokens, boosters)
SELECT 
    t.user_id,
    COALESCE(t.cash_balance, 50000) as coins,
    0 as tokens,
    '{"xp_boost": 0, "recovery_boost": 0, "injury_heal": 0}'::jsonb as boosters
FROM teams t
LEFT JOIN user_currencies uc ON t.user_id = uc.user_id
WHERE uc.user_id IS NULL;

-- Step 2: For users who have both team and user_currencies, 
-- add team cash_balance to their existing coins
UPDATE user_currencies 
SET coins = coins + COALESCE(
    (SELECT cash_balance FROM teams WHERE user_id = user_currencies.user_id LIMIT 1), 
    0
)
WHERE user_id IN (
    SELECT DISTINCT user_id FROM teams WHERE cash_balance > 0
);

-- Step 3: Ensure all users have a user_currencies record with at least 50000 coins
INSERT INTO user_currencies (user_id, coins, tokens, boosters)
SELECT 
    p.id as user_id,
    50000 as coins,
    0 as tokens,
    '{"xp_boost": 0, "recovery_boost": 0, "injury_heal": 0}'::jsonb as boosters
FROM profiles p
LEFT JOIN user_currencies uc ON p.id = uc.user_id
WHERE uc.user_id IS NULL;

-- Step 4: Update any user_currencies with 0 coins to have at least 50000
UPDATE user_currencies 
SET coins = GREATEST(coins, 50000)
WHERE coins < 50000;

-- Step 5: Add comment to cash_balance column indicating it's deprecated
COMMENT ON COLUMN teams.cash_balance IS 'DEPRECATED: Use user_currencies.coins instead. This column will be removed in a future migration.';

-- Step 6: Create a view for backward compatibility during transition
CREATE OR REPLACE VIEW team_with_user_currency AS
SELECT 
    t.*,
    uc.coins as user_coins,
    uc.tokens as user_tokens,
    uc.boosters as user_boosters
FROM teams t
LEFT JOIN user_currencies uc ON t.user_id = uc.user_id;

-- Step 7: Create function to get user currency by team_id (for backward compatibility)
CREATE OR REPLACE FUNCTION get_user_currency_by_team_id(team_uuid UUID)
RETURNS TABLE(coins INTEGER, tokens INTEGER, boosters JSONB) AS $$
BEGIN
    RETURN QUERY
    SELECT uc.coins, uc.tokens, uc.boosters
    FROM user_currencies uc
    JOIN teams t ON t.user_id = uc.user_id
    WHERE t.id = team_uuid;
END;
$$ LANGUAGE plpgsql;

-- Step 8: Create function to update user currency by team_id
CREATE OR REPLACE FUNCTION update_user_currency_by_team_id(
    team_uuid UUID,
    coins_change INTEGER DEFAULT 0,
    tokens_change INTEGER DEFAULT 0
)
RETURNS BOOLEAN AS $$
DECLARE
    user_uuid UUID;
BEGIN
    -- Get user_id from team_id
    SELECT user_id INTO user_uuid FROM teams WHERE id = team_uuid;
    
    IF user_uuid IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Update user currency
    UPDATE user_currencies 
    SET 
        coins = coins + coins_change,
        tokens = tokens + tokens_change,
        updated_at = NOW()
    WHERE user_id = user_uuid;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Step 9: Verify migration
SELECT 
    'Migration Summary' as status,
    COUNT(*) as total_users,
    COUNT(CASE WHEN uc.coins >= 50000 THEN 1 END) as users_with_sufficient_coins,
    AVG(uc.coins) as average_coins
FROM profiles p
LEFT JOIN user_currencies uc ON p.id = uc.user_id;
