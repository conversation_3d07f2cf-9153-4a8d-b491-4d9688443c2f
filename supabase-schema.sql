-- Drop existing policies and triggers to avoid conflicts
DO $$
DECLARE
    r RECORD;
BEGIN
    -- Drop all existing policies
    FOR r IN (SELECT schemaname, tablename, policyname FROM pg_policies WHERE schemaname = 'public') LOOP
        EXECUTE 'DROP POLICY IF EXISTS ' || quote_ident(r.policyname) || ' ON ' || quote_ident(r.schemaname) || '.' || quote_ident(r.tablename);
    END LOOP;

    -- Drop all existing triggers
    FOR r IN (SELECT trigger_name, event_object_table FROM information_schema.triggers WHERE trigger_schema = 'public') LOOP
        EXECUTE 'DROP TRIGGER IF EXISTS ' || quote_ident(r.trigger_name) || ' ON public.' || quote_ident(r.event_object_table);
    END LOOP;
END $$;

-- Create profiles table
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    manager_level INTEGER DEFAULT 1,
    experience_points INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create league tiers table
CREATE TABLE IF NOT EXISTS public.league_tiers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL, -- Bronze, Silver, Gold, Elite, Legends
    level INTEGER NOT NULL CHECK (level >= 1 AND level <= 5), -- 1=Bronze, 2=Silver, 3=Gold, 4=Elite, 5=Legends
    division INTEGER NOT NULL CHECK (division >= 1 AND division <= 5), -- I, II, III, IV, V
    prize_money_base INTEGER DEFAULT 10000,
    promotion_bonus INTEGER DEFAULT 5000,
    relegation_penalty INTEGER DEFAULT 2000,
    tactics_bonus_enabled BOOLEAN DEFAULT FALSE, -- Silver+ gets tactics bonuses
    scouting_quality_multiplier DECIMAL(3,2) DEFAULT 1.0,
    fanbase_growth_multiplier DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(name, division)
);

-- Create seasons table
CREATE TABLE IF NOT EXISTS public.seasons (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL, -- e.g., "Season 2024/25"
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    current_matchday INTEGER DEFAULT 1,
    total_matchdays INTEGER DEFAULT 26, -- 13 teams * 2 (home/away)
    status TEXT DEFAULT 'preparing' CHECK (status IN ('preparing', 'active', 'finished')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced leagues table
CREATE TABLE IF NOT EXISTS public.leagues (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    tier_id UUID REFERENCES public.league_tiers(id) ON DELETE CASCADE NOT NULL,
    season_id UUID REFERENCES public.seasons(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL, -- e.g., "Bronze Division I"
    max_teams INTEGER DEFAULT 14,
    current_teams INTEGER DEFAULT 0,
    status TEXT DEFAULT 'preparing' CHECK (status IN ('preparing', 'active', 'finished')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tier_id, season_id)
);

-- Create teams table
CREATE TABLE IF NOT EXISTS public.teams (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    logo_url TEXT,
    formation TEXT DEFAULT 'standard-7v7',
    team_rating INTEGER DEFAULT 50 CHECK (team_rating >= 0 AND team_rating <= 100),
    league_id UUID REFERENCES public.leagues(id),
    league_position INTEGER DEFAULT 1,
    league_points INTEGER DEFAULT 0,
    matches_played INTEGER DEFAULT 0,
    matches_won INTEGER DEFAULT 0,
    matches_drawn INTEGER DEFAULT 0,
    matches_lost INTEGER DEFAULT 0,
    goals_for INTEGER DEFAULT 0,
    goals_against INTEGER DEFAULT 0,
    cash_balance INTEGER DEFAULT 50000,
    fan_base INTEGER DEFAULT 1000,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create players table
CREATE TABLE IF NOT EXISTS public.players (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    position TEXT NOT NULL CHECK (position IN ('goalkeeper', 'left-wing', 'right-wing', 'left-driver', 'right-driver', 'center-forward', 'point')),
    jersey_number INTEGER DEFAULT 1 CHECK (jersey_number >= 1 AND jersey_number <= 99),
    shooting INTEGER DEFAULT 50 CHECK (shooting >= 0 AND shooting <= 100),
    speed INTEGER DEFAULT 50 CHECK (speed >= 0 AND speed <= 100),
    passing INTEGER DEFAULT 50 CHECK (passing >= 0 AND passing <= 100),
    defense INTEGER DEFAULT 50 CHECK (defense >= 0 AND defense <= 100),
    endurance INTEGER DEFAULT 50 CHECK (endurance >= 0 AND endurance <= 100),
    awareness INTEGER DEFAULT 50 CHECK (awareness >= 0 AND awareness <= 100),
    goalkeeping INTEGER DEFAULT 50 CHECK (goalkeeping >= 0 AND goalkeeping <= 100),
    experience INTEGER DEFAULT 0,
    age INTEGER DEFAULT 20 CHECK (age >= 16 AND age <= 40),
    morale INTEGER DEFAULT 75 CHECK (morale >= 0 AND morale <= 100),
    fatigue INTEGER DEFAULT 0 CHECK (fatigue >= 0 AND fatigue <= 100),
    injury_status TEXT DEFAULT 'healthy' CHECK (injury_status IN ('healthy', 'minor', 'major', 'critical')),
    injury_days INTEGER DEFAULT 0 CHECK (injury_days >= 0),
    contract_salary INTEGER DEFAULT 1000,
    contract_expires_at TIMESTAMP WITH TIME ZONE,
    market_value INTEGER DEFAULT 10000,
    potential INTEGER DEFAULT 75 CHECK (potential >= 50 AND potential <= 100),
    special_abilities TEXT[] DEFAULT '{}',
    perks JSONB DEFAULT '{}',
    training_focus TEXT DEFAULT 'balanced' CHECK (training_focus IN ('shooting', 'speed', 'passing', 'defense', 'endurance', 'awareness', 'goalkeeping', 'balanced')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create matches table with enhanced features
CREATE TABLE IF NOT EXISTS public.matches (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    home_team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
    away_team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
    league_id UUID REFERENCES public.leagues(id) ON DELETE CASCADE,
    season_id UUID REFERENCES public.seasons(id) ON DELETE CASCADE,
    matchday INTEGER NOT NULL,
    home_score INTEGER DEFAULT 0,
    away_score INTEGER DEFAULT 0,
    match_date TIMESTAMP WITH TIME ZONE NOT NULL,
    competition TEXT NOT NULL DEFAULT 'league' CHECK (competition IN ('league', 'cup', 'friendly')),
    status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'live', 'completed', 'cancelled')),
    home_tactics JSONB,
    away_tactics JSONB,
    home_timeouts_used INTEGER DEFAULT 0,
    away_timeouts_used INTEGER DEFAULT 0,
    home_tactical_changes_used INTEGER DEFAULT 0,
    away_tactical_changes_used INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create formations table
CREATE TABLE IF NOT EXISTS public.formations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    formation_data JSONB NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create competitions table
CREATE TABLE IF NOT EXISTS public.competitions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('cup', 'tournament', 'friendly')),
    status TEXT DEFAULT 'upcoming' CHECK (status IN ('upcoming', 'active', 'finished')),
    entry_fee INTEGER DEFAULT 0,
    prize_money INTEGER DEFAULT 5000,
    max_participants INTEGER DEFAULT 32,
    current_round INTEGER DEFAULT 1,
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create training_sessions table
CREATE TABLE IF NOT EXISTS public.training_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
    training_type TEXT NOT NULL CHECK (training_type IN ('shooting', 'speed', 'passing', 'defense', 'endurance', 'awareness', 'goalkeeping', 'tactical')),
    intensity TEXT DEFAULT 'medium' CHECK (intensity IN ('light', 'medium', 'high', 'extreme')),
    duration_minutes INTEGER DEFAULT 90 CHECK (duration_minutes >= 30 AND duration_minutes <= 180),
    cost INTEGER DEFAULT 100,
    experience_gained INTEGER DEFAULT 10,
    fatigue_added INTEGER DEFAULT 5,
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create player_training table
CREATE TABLE IF NOT EXISTS public.player_training (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    player_id UUID REFERENCES public.players(id) ON DELETE CASCADE NOT NULL,
    training_session_id UUID REFERENCES public.training_sessions(id) ON DELETE CASCADE NOT NULL,
    stat_improvements JSONB DEFAULT '{}',
    experience_gained INTEGER DEFAULT 0,
    fatigue_added INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create facilities table
CREATE TABLE IF NOT EXISTS public.facilities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
    facility_type TEXT NOT NULL CHECK (facility_type IN ('training_pool', 'recovery_pool', 'youth_academy', 'aquatic_arena', 'medical_center')),
    level INTEGER DEFAULT 1 CHECK (level >= 1 AND level <= 10),
    upgrade_cost INTEGER DEFAULT 5000,
    maintenance_cost INTEGER DEFAULT 100,
    bonus_percentage INTEGER DEFAULT 10,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create transfer_market table
CREATE TABLE IF NOT EXISTS public.transfer_market (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    player_id UUID REFERENCES public.players(id) ON DELETE CASCADE NOT NULL,
    selling_team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE,
    asking_price INTEGER NOT NULL,
    transfer_type TEXT DEFAULT 'sale' CHECK (transfer_type IN ('sale', 'loan', 'free')),
    status TEXT DEFAULT 'available' CHECK (status IN ('available', 'sold', 'withdrawn')),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create transfer_bids table
CREATE TABLE IF NOT EXISTS public.transfer_bids (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    transfer_id UUID REFERENCES public.transfer_market(id) ON DELETE CASCADE NOT NULL,
    bidding_team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
    bid_amount INTEGER NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'outbid')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create enhanced match_events table
CREATE TABLE IF NOT EXISTS public.match_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    match_id UUID REFERENCES public.matches(id) ON DELETE CASCADE NOT NULL,
    minute INTEGER NOT NULL CHECK (minute >= 0 AND minute <= 120),
    quarter INTEGER NOT NULL CHECK (quarter >= 1 AND quarter <= 4),
    event_type TEXT NOT NULL CHECK (event_type IN ('goal', 'exclusion', 'timeout', 'substitution', 'penalty', 'save', 'tactical_change', 'injury', 'yellow_card', 'red_card')),
    player_id UUID REFERENCES public.players(id),
    team_id UUID REFERENCES public.teams(id) NOT NULL,
    description TEXT,
    impact_rating DECIMAL(3,2) DEFAULT 0.0, -- For player performance calculation
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create daily_logins table
CREATE TABLE IF NOT EXISTS public.daily_logins (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    login_date DATE NOT NULL,
    streak_count INTEGER DEFAULT 1,
    rewards_claimed JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, login_date)
);

-- Create user_currencies table
CREATE TABLE IF NOT EXISTS public.user_currencies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL UNIQUE,
    coins INTEGER DEFAULT 50000,
    tokens INTEGER DEFAULT 0,
    boosters JSONB DEFAULT '{"xp_boost": 0, "recovery_boost": 0, "injury_heal": 0}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create achievements table
CREATE TABLE IF NOT EXISTS public.achievements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    achievement_type TEXT NOT NULL,
    achievement_name TEXT NOT NULL,
    description TEXT,
    reward_coins INTEGER DEFAULT 0,
    reward_tokens INTEGER DEFAULT 0,
    unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create economy_transactions table
CREATE TABLE IF NOT EXISTS public.economy_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('earn', 'spend')),
    source TEXT NOT NULL,
    coins_change INTEGER DEFAULT 0,
    tokens_change INTEGER DEFAULT 0,
    boosters_change JSONB DEFAULT '{}',
    description TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create season_rewards table
CREATE TABLE IF NOT EXISTS public.season_rewards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    season_id UUID REFERENCES public.seasons(id) ON DELETE CASCADE NOT NULL,
    team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
    league_id UUID REFERENCES public.leagues(id) ON DELETE CASCADE NOT NULL,
    final_position INTEGER NOT NULL,
    prize_money INTEGER DEFAULT 0,
    promotion_bonus INTEGER DEFAULT 0,
    relegation_penalty INTEGER DEFAULT 0,
    top_scorer_bonus INTEGER DEFAULT 0,
    best_goalkeeper_bonus INTEGER DEFAULT 0,
    awarded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create match_notifications table
CREATE TABLE IF NOT EXISTS public.match_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    match_id UUID REFERENCES public.matches(id) ON DELETE CASCADE NOT NULL,
    notification_type TEXT NOT NULL CHECK (notification_type IN ('pre_match', 'match_start', 'half_time', 'match_end')),
    scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE,
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create league_standings table for historical tracking
CREATE TABLE IF NOT EXISTS public.league_standings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    league_id UUID REFERENCES public.leagues(id) ON DELETE CASCADE NOT NULL,
    team_id UUID REFERENCES public.teams(id) ON DELETE CASCADE NOT NULL,
    season_id UUID REFERENCES public.seasons(id) ON DELETE CASCADE NOT NULL,
    position INTEGER NOT NULL,
    matches_played INTEGER DEFAULT 0,
    wins INTEGER DEFAULT 0,
    draws INTEGER DEFAULT 0,
    losses INTEGER DEFAULT 0,
    goals_for INTEGER DEFAULT 0,
    goals_against INTEGER DEFAULT 0,
    points INTEGER DEFAULT 0,
    form JSONB DEFAULT '[]', -- Last 5 results
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(league_id, team_id, season_id)
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.players ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.formations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.leagues ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.competitions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.training_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.player_training ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.facilities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transfer_market ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transfer_bids ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.match_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_logins ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_currencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.economy_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.league_tiers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.seasons ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.season_rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.match_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.league_standings ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Create policies for teams
CREATE POLICY "Users can view own teams" ON public.teams
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own teams" ON public.teams
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own teams" ON public.teams
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own teams" ON public.teams
    FOR DELETE USING (auth.uid() = user_id);

-- Create policies for players
CREATE POLICY "Users can view own players" ON public.players
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own players" ON public.players
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own players" ON public.players
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete own players" ON public.players
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE teams.id = players.team_id 
            AND teams.user_id = auth.uid()
        )
    );

-- Create policies for matches
CREATE POLICY "Users can view matches involving their teams" ON public.matches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.teams 
            WHERE (teams.id = matches.home_team_id OR teams.id = matches.away_team_id)
            AND teams.user_id = auth.uid()
        )
    );

-- Create policies for formations
CREATE POLICY "Users can view own formations" ON public.formations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = formations.team_id
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own formations" ON public.formations
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = formations.team_id
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own formations" ON public.formations
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = formations.team_id
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete own formations" ON public.formations
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = formations.team_id
            AND teams.user_id = auth.uid()
        )
    );

-- Create policies for leagues (public read access)
CREATE POLICY "Anyone can view leagues" ON public.leagues
    FOR SELECT USING (true);

-- Create policies for competitions (public read access)
CREATE POLICY "Anyone can view competitions" ON public.competitions
    FOR SELECT USING (true);

-- Create policies for training_sessions
CREATE POLICY "Users can view own training sessions" ON public.training_sessions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = training_sessions.team_id
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own training sessions" ON public.training_sessions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = training_sessions.team_id
            AND teams.user_id = auth.uid()
        )
    );

-- Create policies for player_training
CREATE POLICY "Users can view own player training" ON public.player_training
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.players
            JOIN public.teams ON teams.id = players.team_id
            WHERE players.id = player_training.player_id
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own player training" ON public.player_training
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.players
            JOIN public.teams ON teams.id = players.team_id
            WHERE players.id = player_training.player_id
            AND teams.user_id = auth.uid()
        )
    );

-- Create policies for facilities
CREATE POLICY "Users can view own facilities" ON public.facilities
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = facilities.team_id
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own facilities" ON public.facilities
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = facilities.team_id
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own facilities" ON public.facilities
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = facilities.team_id
            AND teams.user_id = auth.uid()
        )
    );

-- Create policies for transfer_market (public read, owner write)
CREATE POLICY "Anyone can view transfer market" ON public.transfer_market
    FOR SELECT USING (true);

CREATE POLICY "Users can insert own transfers" ON public.transfer_market
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.players
            JOIN public.teams ON teams.id = players.team_id
            WHERE players.id = transfer_market.player_id
            AND teams.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own transfers" ON public.transfer_market
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.players
            JOIN public.teams ON teams.id = players.team_id
            WHERE players.id = transfer_market.player_id
            AND teams.user_id = auth.uid()
        )
    );

-- Create policies for transfer_bids
CREATE POLICY "Users can view relevant transfer bids" ON public.transfer_bids
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = transfer_bids.bidding_team_id
            AND teams.user_id = auth.uid()
        )
        OR
        EXISTS (
            SELECT 1 FROM public.transfer_market tm
            JOIN public.players p ON p.id = tm.player_id
            JOIN public.teams t ON t.id = p.team_id
            WHERE tm.id = transfer_bids.transfer_id
            AND t.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own transfer bids" ON public.transfer_bids
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = transfer_bids.bidding_team_id
            AND teams.user_id = auth.uid()
        )
    );

-- Create policies for match_events (public read access)
CREATE POLICY "Anyone can view match events" ON public.match_events
    FOR SELECT USING (true);

-- Create policies for league_tiers (public read access)
CREATE POLICY "Anyone can view league tiers" ON public.league_tiers
    FOR SELECT USING (true);

-- Create policies for seasons (public read access)
CREATE POLICY "Anyone can view seasons" ON public.seasons
    FOR SELECT USING (true);

-- Create policies for season_rewards
CREATE POLICY "Users can view their team's season rewards" ON public.season_rewards
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.teams
            WHERE teams.id = season_rewards.team_id
            AND teams.user_id = auth.uid()
        )
    );

-- Create policies for match_notifications
CREATE POLICY "Users can view their own match notifications" ON public.match_notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own match notifications" ON public.match_notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Create policies for league_standings (public read access)
CREATE POLICY "Anyone can view league standings" ON public.league_standings
    FOR SELECT USING (true);

-- Create functions for updated_at timestamps
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.teams
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.players
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.formations
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.leagues
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.competitions
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.facilities
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.transfer_market
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.transfer_bids
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.user_currencies
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Create policies for daily_logins
CREATE POLICY "Users can view their own daily logins" ON public.daily_logins
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own daily logins" ON public.daily_logins
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own daily logins" ON public.daily_logins
    FOR UPDATE USING (user_id = auth.uid());

-- Create policies for user_currencies
CREATE POLICY "Users can view their own currencies" ON public.user_currencies
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own currencies" ON public.user_currencies
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own currencies" ON public.user_currencies
    FOR UPDATE USING (user_id = auth.uid());

-- Create policies for achievements
CREATE POLICY "Users can view their own achievements" ON public.achievements
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own achievements" ON public.achievements
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Create policies for economy_transactions
CREATE POLICY "Users can view their own transactions" ON public.economy_transactions
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own transactions" ON public.economy_transactions
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Sample data can be inserted manually after setup if needed
-- INSERT INTO public.profiles (id, email, full_name, manager_level, experience_points)
-- VALUES
--     ('00000000-0000-0000-0000-000000000001', '<EMAIL>', 'Demo Manager', 5, 1250)
-- ON CONFLICT (id) DO NOTHING;
