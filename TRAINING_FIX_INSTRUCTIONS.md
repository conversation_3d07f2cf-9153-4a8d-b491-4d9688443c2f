# Training System Fix Instructions

## Problem
The training button keeps loading and doesn't actually train players because the required database tables are missing.

## Solution
You need to run the database migration to create the missing tables.

## Steps to Fix

### 1. Open Supabase Dashboard
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Sign in to your account
3. Select your Water Polo Manager project

### 2. Run the Migration
1. In the Supabase dashboard, go to the **SQL Editor** (left sidebar)
2. Click **"New Query"**
3. Copy and paste the entire contents of the `database-migration.sql` file into the SQL editor
4. Click **"Run"** to execute the migration

### 3. Verify the Migration
After running the migration, you should see these new tables in your database:
- `training_sessions`
- `player_training` 
- `facilities`

You can verify this by going to **Table Editor** in the Supabase dashboard.

### 4. Test the Training System
1. Go back to your app at `http://localhost:3005/training`
2. Select a drill and some players
3. Click the training button
4. The training should now work properly!

## What the Migration Does

The migration script:
1. **Adds missing columns** to the `teams` table (`cash_balance`, `league_position`, `league_points`)
2. **Creates `training_sessions` table** to store training session data
3. **Creates `player_training` table** to track individual player training records
4. **Creates `facilities` table** to store team facilities (like training pools)
5. **Sets up proper indexes** for better performance
6. **Configures Row Level Security (RLS)** to ensure users can only access their own data
7. **Adds default data** (gives existing teams a basic training facility and starting cash)

## Troubleshooting

If you encounter any errors:
1. Make sure you're using the correct Supabase project
2. Check that you have the proper permissions (you should be the project owner)
3. If some statements fail, try running them individually
4. Contact me if you need help with specific error messages

## After the Fix

Once the migration is complete, the training system will be fully functional:
- Players will gain stats based on the training type and intensity
- Team cash will be deducted for training costs
- Player fatigue will increase after training
- Training history will be recorded
- Facility bonuses will apply to training effectiveness
