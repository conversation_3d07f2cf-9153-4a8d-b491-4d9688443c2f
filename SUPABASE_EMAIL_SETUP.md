# Supabase Email Configuration Guide

This guide will help you set up custom email templates and improve the confirmation email experience for your Water Polo Manager application.

## 📧 Email Template Setup

### Step 1: Access Supabase Dashboard
1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your Water Polo Manager project
3. Navigate to **Authentication** → **Email Templates**

### Step 2: Configure Confirmation Email Template

#### HTML Template
1. Click on **"Confirm signup"** template
2. Replace the default HTML with the content from `email-templates/confirmation-email.html`
3. Make sure to update the following variables:
   - `{{ .Name }}` - User's full name
   - `{{ .Email }}` - User's email address
   - `{{ .ConfirmationURL }}` - Confirmation link

#### Text Template (Fallback)
1. In the same template editor, switch to the **Text** tab
2. Replace with content from `email-templates/confirmation-email.txt`

### Step 3: Configure Email Settings

#### Site URL Configuration
1. Go to **Authentication** → **URL Configuration**
2. Set **Site URL** to: `https://your-domain.com` (or `http://localhost:3009` for development)
3. Add **Redirect URLs**:
   - `https://your-domain.com/auth/confirm`
   - `http://localhost:3009/auth/confirm` (for development)

#### Email Provider Settings
1. Go to **Authentication** → **Settings**
2. Under **SMTP Settings**, configure your email provider:

**Option A: Use Supabase's Default (Recommended for development)**
- Keep "Enable email confirmations" checked
- Use default Supabase SMTP (limited to 3 emails per hour)

**Option B: Custom SMTP (Recommended for production)**
- Enable "Use custom SMTP server"
- Configure with your email provider (Gmail, SendGrid, etc.)

### Step 4: Email Template Variables

The following variables are available in your email templates:

| Variable | Description | Example |
|----------|-------------|---------|
| `{{ .Email }}` | User's email address | `<EMAIL>` |
| `{{ .Name }}` | User's full name | `John Doe` |
| `{{ .ConfirmationURL }}` | Email confirmation link | `https://your-app.com/auth/confirm?token=...` |
| `{{ .SiteURL }}` | Your site URL | `https://your-app.com` |
| `{{ .Token }}` | Confirmation token | `abc123...` |

## 🎨 Customization Options

### Branding
- Update the logo and colors in the HTML template
- Modify the header text and welcome message
- Add your company information in the footer

### Content
- Customize the welcome message
- Add specific features or benefits
- Include social media links
- Add contact information

## 🔧 Advanced Configuration

### Custom Email Provider Setup (Production)

#### Using SendGrid
1. Sign up for [SendGrid](https://sendgrid.com/)
2. Create an API key
3. In Supabase SMTP settings:
   - **Host**: `smtp.sendgrid.net`
   - **Port**: `587`
   - **Username**: `apikey`
   - **Password**: Your SendGrid API key

#### Using Gmail SMTP
1. Enable 2-factor authentication on your Gmail account
2. Generate an app-specific password
3. In Supabase SMTP settings:
   - **Host**: `smtp.gmail.com`
   - **Port**: `587`
   - **Username**: Your Gmail address
   - **Password**: App-specific password

### Email Rate Limiting
- Default Supabase: 3 emails per hour
- Custom SMTP: Depends on your provider
- Consider implementing email queuing for high-volume applications

## 🧪 Testing

### Test Email Confirmation Flow
1. Sign up with a test email address
2. Check that the email is received
3. Verify the email template renders correctly
4. Test the confirmation link
5. Ensure successful redirect to `/auth/confirm`

### Email Template Testing
1. Use Supabase's template preview feature
2. Send test emails to yourself
3. Test on different email clients (Gmail, Outlook, etc.)
4. Verify mobile responsiveness

## 🚀 Deployment Checklist

Before going live, ensure:

- [ ] Custom email templates are configured
- [ ] Site URL is set to your production domain
- [ ] Redirect URLs include your production domain
- [ ] Custom SMTP is configured (recommended)
- [ ] Email templates tested across different clients
- [ ] Confirmation flow tested end-to-end
- [ ] Rate limiting is appropriate for your needs

## 📱 Mobile Optimization

The provided email template is mobile-responsive and includes:
- Responsive design that works on all devices
- Touch-friendly buttons
- Readable font sizes
- Proper spacing for mobile screens

## 🔒 Security Considerations

- Confirmation links expire after 24 hours
- Use HTTPS for all redirect URLs
- Validate tokens on the server side
- Implement rate limiting for email requests
- Monitor for suspicious email activity

## 📞 Support

If you encounter issues:
1. Check Supabase logs in the Dashboard
2. Verify email template syntax
3. Test with different email providers
4. Check spam/junk folders
5. Contact Supabase support if needed

---

**Note**: Remember to test the entire email flow in a staging environment before deploying to production.
